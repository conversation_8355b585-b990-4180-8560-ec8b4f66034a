"""
Reporting Service

This module provides comprehensive reporting functionality including
data export, analytics, and report generation for the assessment system.
"""

import logging
import io
import csv
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from sqlalchemy.orm import Session
from sqlalchemy import and_, func

from database.connection import db_connection
from models.assessment import Assessment, PeerEvaluation
from models.batch import Batch, Student
from models.user import User
from core.exceptions import ValidationError, DatabaseError


class ReportingService:
    """Service class for reporting and analytics operations."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def get_dashboard_statistics(self) -> Dict[str, Any]:
        """Get overall dashboard statistics."""
        try:
            def _get_dashboard_stats(session: Session) -> Dict[str, Any]:
                # Total assessments
                total_assessments = session.query(Assessment).count()
                active_assessments = session.query(Assessment).filter(Assessment.is_active == True).count()
                
                # Total evaluations
                total_evaluations = session.query(PeerEvaluation).count()
                
                # Active students
                active_students = session.query(Student).filter(Student.is_active == True).count()
                
                # Average score
                avg_score = session.query(func.avg(PeerEvaluation.overall_score)).scalar() or 0
                
                # Assessment type distribution
                assessment_types = session.query(
                    Assessment.assessment_type,
                    func.count(Assessment.id)
                ).group_by(Assessment.assessment_type).all()
                
                # Performance trends (last 6 months)
                from datetime import datetime, timedelta
                six_months_ago = datetime.utcnow() - timedelta(days=180)
                
                trend_data = session.query(
                    func.date(Assessment.created_at).label('date'),
                    func.avg(PeerEvaluation.overall_score).label('avg_score')
                ).join(PeerEvaluation, Assessment.id == PeerEvaluation.assessment_id)\
                .filter(Assessment.created_at >= six_months_ago)\
                .group_by(func.date(Assessment.created_at))\
                .order_by(func.date(Assessment.created_at)).all()
                
                return {
                    'total_assessments': total_assessments,
                    'active_assessments': active_assessments,
                    'total_evaluations': total_evaluations,
                    'active_students': active_students,
                    'average_score': float(avg_score),
                    'assessment_types': dict(assessment_types),
                    'trend_data': [(str(d.date), float(d.avg_score or 0)) for d in trend_data]
                }
            
            return db_connection.execute_transaction(_get_dashboard_stats)
                
        except Exception as e:
            self.logger.error(f"Failed to get dashboard statistics: {e}")
            raise DatabaseError(f"Failed to get dashboard statistics: {e}")
    
    def generate_assessment_report_data(self, assessment_id: int, report_type: str = 'summary') -> Dict[str, Any]:
        """Generate assessment report data."""
        try:
            def _get_assessment_report_data(session: Session) -> Dict[str, Any]:
                assessment = session.query(Assessment).filter(Assessment.id == assessment_id).first()
                if not assessment:
                    raise ValidationError("Assessment not found")
                
                # Get all evaluations
                evaluations = session.query(PeerEvaluation).filter(
                    PeerEvaluation.assessment_id == assessment_id
                ).all()
                
                # Get batch students
                batch_students = []
                if assessment.batch:
                    batch_students = session.query(Student).filter(
                        and_(Student.batch_id == assessment.batch_id, Student.is_active == True)
                    ).all()
                
                # Calculate statistics
                if evaluations:
                    avg_overall = sum(e.overall_score for e in evaluations) / len(evaluations)
                    avg_leadership = sum(e.leadership_score for e in evaluations) / len(evaluations)
                    avg_teamwork = sum(e.teamwork_score for e in evaluations) / len(evaluations)
                    avg_communication = sum(e.communication_score for e in evaluations) / len(evaluations)
                    avg_technical = sum(e.technical_score for e in evaluations) / len(evaluations)
                    avg_professionalism = sum(e.professionalism_score for e in evaluations) / len(evaluations)
                    
                    participation_rate = (len(set(e.evaluator_id for e in evaluations)) / len(batch_students) * 100) if batch_students else 0
                else:
                    avg_overall = avg_leadership = avg_teamwork = avg_communication = avg_technical = avg_professionalism = 0
                    participation_rate = 0
                
                return {
                    'assessment': assessment,
                    'evaluations': evaluations,
                    'batch_students': batch_students,
                    'statistics': {
                        'total_evaluations': len(evaluations),
                        'participation_rate': participation_rate,
                        'average_scores': {
                            'overall': avg_overall,
                            'leadership': avg_leadership,
                            'teamwork': avg_teamwork,
                            'communication': avg_communication,
                            'technical': avg_technical,
                            'professionalism': avg_professionalism
                        }
                    },
                    'report_type': report_type,
                    'generated_at': datetime.utcnow()
                }
            
            return db_connection.execute_transaction(_get_assessment_report_data)
                
        except Exception as e:
            self.logger.error(f"Failed to generate assessment report data: {e}")
            raise DatabaseError(f"Failed to generate assessment report data: {e}")
    
    def generate_student_report_data(self, student_id: int, report_type: str = 'individual') -> Dict[str, Any]:
        """Generate student report data."""
        try:
            def _get_student_report_data(session: Session) -> Dict[str, Any]:
                student = session.query(Student).filter(Student.id == student_id).first()
                if not student:
                    raise ValidationError("Student not found")
                
                # Get all evaluations for this student
                evaluations_received = session.query(PeerEvaluation).filter(
                    PeerEvaluation.evaluated_id == student_id
                ).all()
                
                # Get all evaluations by this student
                evaluations_given = session.query(PeerEvaluation).filter(
                    PeerEvaluation.evaluator_id == student_id
                ).all()
                
                # Get assessments this student participated in
                assessment_ids = list(set([e.assessment_id for e in evaluations_received + evaluations_given]))
                assessments = session.query(Assessment).filter(Assessment.id.in_(assessment_ids)).all()
                
                # Calculate performance metrics
                if evaluations_received:
                    avg_scores = {
                        'overall': sum(e.overall_score for e in evaluations_received) / len(evaluations_received),
                        'leadership': sum(e.leadership_score for e in evaluations_received) / len(evaluations_received),
                        'teamwork': sum(e.teamwork_score for e in evaluations_received) / len(evaluations_received),
                        'communication': sum(e.communication_score for e in evaluations_received) / len(evaluations_received),
                        'technical': sum(e.technical_score for e in evaluations_received) / len(evaluations_received),
                        'professionalism': sum(e.professionalism_score for e in evaluations_received) / len(evaluations_received)
                    }
                else:
                    avg_scores = {
                        'overall': 0, 'leadership': 0, 'teamwork': 0,
                        'communication': 0, 'technical': 0, 'professionalism': 0
                    }
                
                # Performance trend over time
                performance_trend = []
                for assessment in sorted(assessments, key=lambda a: a.created_at or datetime.min):
                    assessment_evals = [e for e in evaluations_received if e.assessment_id == assessment.id]
                    if assessment_evals:
                        avg_score = sum(e.overall_score for e in assessment_evals) / len(assessment_evals)
                        performance_trend.append({
                            'assessment_title': assessment.title,
                            'date': assessment.created_at,
                            'score': avg_score
                        })
                
                return {
                    'student': student,
                    'evaluations_received': evaluations_received,
                    'evaluations_given': evaluations_given,
                    'assessments': assessments,
                    'average_scores': avg_scores,
                    'performance_trend': performance_trend,
                    'statistics': {
                        'total_evaluations_received': len(evaluations_received),
                        'total_evaluations_given': len(evaluations_given),
                        'assessments_participated': len(assessments),
                        'participation_rate': (len(evaluations_given) / len(assessments) * 100) if assessments else 0
                    },
                    'report_type': report_type,
                    'generated_at': datetime.utcnow()
                }
            
            return db_connection.execute_transaction(_get_student_report_data)
                
        except Exception as e:
            self.logger.error(f"Failed to generate student report data: {e}")
            raise DatabaseError(f"Failed to generate student report data: {e}")
    
    def generate_batch_report_data(self, batch_id: int, report_type: str = 'overview') -> Dict[str, Any]:
        """Generate batch report data."""
        try:
            def _get_batch_report_data(session: Session) -> Dict[str, Any]:
                batch = session.query(Batch).filter(Batch.id == batch_id).first()
                if not batch:
                    raise ValidationError("Batch not found")
                
                # Get batch students
                students = session.query(Student).filter(
                    and_(Student.batch_id == batch_id, Student.is_active == True)
                ).all()
                
                # Get batch assessments
                assessments = session.query(Assessment).filter(Assessment.batch_id == batch_id).all()
                
                # Get all evaluations for this batch
                assessment_ids = [a.id for a in assessments]
                evaluations = session.query(PeerEvaluation).filter(
                    PeerEvaluation.assessment_id.in_(assessment_ids)
                ).all() if assessment_ids else []
                
                # Calculate batch statistics
                if evaluations:
                    batch_avg_scores = {
                        'overall': sum(e.overall_score for e in evaluations) / len(evaluations),
                        'leadership': sum(e.leadership_score for e in evaluations) / len(evaluations),
                        'teamwork': sum(e.teamwork_score for e in evaluations) / len(evaluations),
                        'communication': sum(e.communication_score for e in evaluations) / len(evaluations),
                        'technical': sum(e.technical_score for e in evaluations) / len(evaluations),
                        'professionalism': sum(e.professionalism_score for e in evaluations) / len(evaluations)
                    }
                else:
                    batch_avg_scores = {
                        'overall': 0, 'leadership': 0, 'teamwork': 0,
                        'communication': 0, 'technical': 0, 'professionalism': 0
                    }
                
                # Student performance summary
                student_performance = []
                for student in students:
                    student_evals = [e for e in evaluations if e.evaluated_id == student.id]
                    if student_evals:
                        avg_score = sum(e.overall_score for e in student_evals) / len(student_evals)
                        student_performance.append({
                            'student': student,
                            'average_score': avg_score,
                            'evaluation_count': len(student_evals)
                        })
                    else:
                        student_performance.append({
                            'student': student,
                            'average_score': 0,
                            'evaluation_count': 0
                        })
                
                return {
                    'batch': batch,
                    'students': students,
                    'assessments': assessments,
                    'evaluations': evaluations,
                    'batch_average_scores': batch_avg_scores,
                    'student_performance': student_performance,
                    'statistics': {
                        'total_students': len(students),
                        'total_assessments': len(assessments),
                        'total_evaluations': len(evaluations),
                        'completion_rate': (len([sp for sp in student_performance if sp['evaluation_count'] > 0]) / len(students) * 100) if students else 0
                    },
                    'report_type': report_type,
                    'generated_at': datetime.utcnow()
                }
            
            return db_connection.execute_transaction(_get_batch_report_data)
                
        except Exception as e:
            self.logger.error(f"Failed to generate batch report data: {e}")
            raise DatabaseError(f"Failed to generate batch report data: {e}")
    
    def export_data_to_csv(self, data: List[Dict[str, Any]], filename: str) -> io.StringIO:
        """Export data to CSV format."""
        try:
            output = io.StringIO()
            
            if not data:
                return output
            
            # Get all unique keys from the data
            fieldnames = set()
            for row in data:
                fieldnames.update(row.keys())
            fieldnames = sorted(list(fieldnames))
            
            writer = csv.DictWriter(output, fieldnames=fieldnames)
            writer.writeheader()
            
            for row in data:
                # Convert any complex objects to strings
                clean_row = {}
                for key, value in row.items():
                    if isinstance(value, (datetime, date)):
                        clean_row[key] = value.strftime('%Y-%m-%d %H:%M:%S') if isinstance(value, datetime) else value.strftime('%Y-%m-%d')
                    elif hasattr(value, '__dict__'):
                        clean_row[key] = str(value)
                    else:
                        clean_row[key] = value
                writer.writerow(clean_row)
            
            output.seek(0)
            return output
            
        except Exception as e:
            self.logger.error(f"Failed to export data to CSV: {e}")
            raise DatabaseError(f"Failed to export data to CSV: {e}")
    
    def get_assessment_statistics(self) -> Dict[str, Any]:
        """Get assessment statistics for reports dashboard."""
        try:
            def _get_assessment_stats(session: Session) -> Dict[str, Any]:
                total_assessments = session.query(Assessment).count()
                active_assessments = session.query(Assessment).filter(Assessment.is_active == True).count()
                
                # Count by status
                pending_assessments = session.query(Assessment).filter(
                    and_(Assessment.is_active == True, Assessment.start_date > datetime.utcnow())
                ).count()
                
                completed_assessments = session.query(Assessment).filter(
                    and_(Assessment.is_active == True, Assessment.end_date < datetime.utcnow())
                ).count()
                
                return {
                    'total_assessments': total_assessments,
                    'active_assessments': active_assessments,
                    'pending_assessments': pending_assessments,
                    'completed_assessments': completed_assessments
                }
            
            return db_connection.execute_transaction(_get_assessment_stats)
                
        except Exception as e:
            self.logger.error(f"Failed to get assessment statistics: {e}")
            raise DatabaseError(f"Failed to get assessment statistics: {e}")


# Global service instance
reporting_service = ReportingService()
