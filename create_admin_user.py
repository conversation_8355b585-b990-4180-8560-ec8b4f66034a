#!/usr/bin/env python3
"""
Create Admin User Script
Manually creates the default admin user for the peer review system.
"""

import sys
import os
from datetime import datetime

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from config.database import DatabaseManager
from models.user import User
from core.encryption import password_hasher


def create_admin_user():
    """Create the default admin user."""
    try:
        # Initialize database
        db_manager = DatabaseManager()
        db_manager.initialize()
        
        # Get a session directly from the database manager
        session = db_manager.get_session()
        
        try:
            # Check if admin user already exists
            existing_admin = session.query(User).filter(
                User.username == "admin"
            ).first()
            
            if existing_admin:
                print("Admin user already exists!")
                print(f"Username: {existing_admin.username}")
                print(f"Full Name: {existing_admin.full_name}")
                print(f"Role: {existing_admin.role}")
                return
            
            # Create admin user
            admin_password = "Admin@123"
            password_hash = password_hasher.hash_password(admin_password)
            
            admin_user = User(
                username="admin",
                password_hash=password_hash,
                full_name="System Administrator",
                email="<EMAIL>",
                role="super_admin",
                is_active=True,
                is_verified=True,
                created_at=datetime.utcnow(),
                notes="Default system administrator account"
            )
            
            session.add(admin_user)
            session.commit()
            
            print("✅ Admin user created successfully!")
            print("📋 Login Details:")
            print(f"   Username: admin")
            print(f"   Password: Admin@123")
            print(f"   Role: super_admin")
            print("")
            print("⚠️  IMPORTANT: Please change the default password after first login!")
            
        except Exception as e:
            session.rollback()
            print(f"❌ Error creating admin user: {e}")
            return False
        finally:
            session.close()
            
        return True
        
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False


def create_sample_users():
    """Create sample teacher users."""
    try:
        db_manager = DatabaseManager()
        session = db_manager.get_session()
        
        try:
            # Check if sample users already exist
            existing_users = session.query(User).filter(
                User.username.in_(["teacher1", "teacher2"])
            ).count()
            
            if existing_users > 0:
                print("Sample users already exist!")
                return
            
            # Create sample users
            sample_users = [
                {
                    "username": "teacher1",
                    "password": "Teacher@123",
                    "full_name": "Dr. John Smith",
                    "email": "<EMAIL>",
                    "role": "teacher",
                    "notes": "Sample teacher account"
                },
                {
                    "username": "teacher2",
                    "password": "Teacher@123",
                    "full_name": "Prof. Sarah Johnson",
                    "email": "<EMAIL>",
                    "role": "teacher",
                    "notes": "Sample teacher account"
                }
            ]
            
            for user_data in sample_users:
                password_hash = password_hasher.hash_password(user_data["password"])
                
                user = User(
                    username=user_data["username"],
                    password_hash=password_hash,
                    full_name=user_data["full_name"],
                    email=user_data["email"],
                    role=user_data["role"],
                    is_active=True,
                    is_verified=True,
                    created_at=datetime.utcnow(),
                    notes=user_data["notes"]
                )
                
                session.add(user)
            
            session.commit()
            print("✅ Sample teacher users created successfully!")
            print("📋 Sample Login Details:")
            for user_data in sample_users:
                print(f"   Username: {user_data['username']} | Password: {user_data['password']}")
            
        except Exception as e:
            session.rollback()
            print(f"❌ Error creating sample users: {e}")
            return False
        finally:
            session.close()
            
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def main():
    """Main function."""
    print("🚀 Military Peer Review Assessment System - User Creation Script")
    print("=" * 60)
    
    # Create admin user
    print("Creating admin user...")
    if create_admin_user():
        print("")
        
        # Create sample users
        print("Creating sample users...")
        create_sample_users()
        
        print("")
        print("🎉 Setup completed successfully!")
        print("🌐 You can now access the web application at: http://localhost:5000")
    else:
        print("❌ Setup failed!")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
