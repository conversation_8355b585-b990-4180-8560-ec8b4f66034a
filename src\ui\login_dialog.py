"""
Login Dialog

This module provides the login interface for user authentication.
"""

import logging
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout, QLabel, QLineEdit,
    QPushButton, QCheckBox, QMessageBox, QFrame, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, QThread, QObject, Signal
from PySide6.QtGui import QFont, QPixmap, QIcon

from core.auth import auth_manager
from core.exceptions import AuthenticationError
from config.settings import app_settings


class LoginWorker(QObject):
    """Worker thread for authentication to prevent UI blocking."""

    finished = Signal(bool, str)  # success, message

    def __init__(self, username: str, password: str):
        super().__init__()
        self.username = username
        self.password = password
        self.logger = logging.getLogger(__name__)

    def authenticate(self):
        """Perform authentication in background thread."""
        try:
            auth_manager.authenticate(self.username, self.password)
            self.finished.emit(True, "Authentication successful")
        except AuthenticationError as e:
            self.logger.warning(f"Authentication failed for user {self.username}: {e}")
            self.finished.emit(False, str(e))
        except Exception as e:
            self.logger.error(f"Unexpected authentication error: {e}")
            self.finished.emit(False, "An unexpected error occurred during login")


class LoginDialog(QDialog):
    """Login dialog for user authentication."""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)

        # Thread management
        self.auth_thread = None
        self.auth_worker = None

        self._setup_ui()
        self._load_settings()

    def _setup_ui(self):
        """Set up the login dialog UI."""
        self.setWindowTitle("Login - Military Peer Review System")
        self.setFixedSize(480, 420)  # Increased size for better proportions
        self.setModal(True)

        # Keep window frame but make it modern
        self.setWindowFlags(Qt.Dialog)

        # Main layout
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(0)

        # Create main frame with enhanced styling
        main_frame = QFrame()
        main_frame.setStyleSheet("""
            QFrame {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #ffffff, stop:1 #f8f9fa);
                border: 1px solid #dee2e6;
                border-radius: 12px;
            }
        """)
        main_layout.addWidget(main_frame)

        # Frame layout with better spacing
        frame_layout = QVBoxLayout(main_frame)
        frame_layout.setContentsMargins(40, 35, 40, 35)
        frame_layout.setSpacing(25)

        # Header section
        self._create_header(frame_layout)

        # Login form section
        self._create_form(frame_layout)

        # Button section
        self._create_buttons(frame_layout)

        # Institution info section
        self._create_footer(frame_layout)

    def _create_header(self, layout):
        """Create the header section with logo and title."""
        header_layout = QVBoxLayout()
        header_layout.setAlignment(Qt.AlignCenter)
        header_layout.setSpacing(12)

        # Application title with enhanced styling
        title_label = QLabel("Military Peer Review System")
        title_font = QFont()
        title_font.setPointSize(20)
        title_font.setBold(True)
        title_font.setFamily("Segoe UI")
        title_label.setFont(title_font)
        title_label.setAlignment(Qt.AlignCenter)
        title_label.setStyleSheet("""
            color: #1a365d;
            margin-bottom: 8px;
            letter-spacing: 0.5px;
        """)
        header_layout.addWidget(title_label)

        # Subtitle with better styling
        subtitle_label = QLabel("Please enter your credentials to continue")
        subtitle_label.setAlignment(Qt.AlignCenter)
        subtitle_label.setStyleSheet("""
            color: #4a5568;
            font-size: 14px;
            font-weight: 400;
            margin-bottom: 5px;
        """)
        header_layout.addWidget(subtitle_label)

        layout.addLayout(header_layout)

    def _create_form(self, layout):
        """Create the login form."""
        form_layout = QFormLayout()
        form_layout.setSpacing(20)
        form_layout.setFieldGrowthPolicy(QFormLayout.AllNonFixedFieldsGrow)

        # Enhanced input field styling
        input_style = """
            QLineEdit {
                padding: 14px 16px;
                border: 2px solid #e2e8f0;
                border-radius: 8px;
                font-size: 15px;
                font-family: "Segoe UI";
                background-color: #ffffff;
                min-height: 20px;
            }
            QLineEdit:focus {
                border-color: #4299e1;
                background-color: #f7fafc;
                outline: none;
            }
            QLineEdit:hover {
                border-color: #cbd5e0;
            }
        """

        # Username field with enhanced styling
        self.username_edit = QLineEdit()
        self.username_edit.setPlaceholderText("Enter your username")
        self.username_edit.setStyleSheet(input_style)
        self.username_edit.setMinimumHeight(50)

        username_label = QLabel("Username:")
        username_label.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        """)
        form_layout.addRow(username_label, self.username_edit)

        # Password field with enhanced styling
        self.password_edit = QLineEdit()
        self.password_edit.setEchoMode(QLineEdit.Password)
        self.password_edit.setPlaceholderText("Enter your password")
        self.password_edit.setStyleSheet(input_style)
        self.password_edit.setMinimumHeight(50)

        password_label = QLabel("Password:")
        password_label.setStyleSheet("""
            font-size: 14px;
            font-weight: 600;
            color: #2d3748;
            margin-bottom: 5px;
        """)
        form_layout.addRow(password_label, self.password_edit)

        # Remember username checkbox with better styling
        self.remember_checkbox = QCheckBox("Remember username")
        self.remember_checkbox.setStyleSheet("""
            QCheckBox {
                color: #4a5568;
                font-size: 13px;
                spacing: 8px;
            }
            QCheckBox::indicator {
                width: 18px;
                height: 18px;
                border-radius: 3px;
                border: 2px solid #e2e8f0;
            }
            QCheckBox::indicator:checked {
                background-color: #4299e1;
                border-color: #4299e1;
            }
        """)
        form_layout.addRow("", self.remember_checkbox)

        layout.addLayout(form_layout)

        # Connect Enter key to login
        self.username_edit.returnPressed.connect(self._on_login_clicked)
        self.password_edit.returnPressed.connect(self._on_login_clicked)

    def _create_buttons(self, layout):
        """Create the button section."""
        button_layout = QHBoxLayout()
        button_layout.setSpacing(15)

        # Add spacer
        button_layout.addItem(QSpacerItem(40, 20, QSizePolicy.Expanding, QSizePolicy.Minimum))

        # Enhanced button styling
        button_base_style = """
            QPushButton {{
                border: none;
                border-radius: 8px;
                font-size: 15px;
                font-weight: 600;
                font-family: "Segoe UI";
                min-height: 45px;
                min-width: 100px;
                padding: 12px 24px;
            }}
        """

        # Cancel button with enhanced styling
        self.cancel_button = QPushButton("Cancel")
        self.cancel_button.setStyleSheet(button_base_style + """
            QPushButton {
                background-color: #e2e8f0;
                color: #4a5568;
                border: 1px solid #cbd5e0;
            }
            QPushButton:hover {
                background-color: #cbd5e0;
                color: #2d3748;
            }
            QPushButton:pressed {
                background-color: #a0aec0;
            }
        """)
        self.cancel_button.clicked.connect(self.reject)
        button_layout.addWidget(self.cancel_button)

        # Login button with enhanced styling
        self.login_button = QPushButton("Sign In")
        self.login_button.setDefault(True)
        self.login_button.setStyleSheet(button_base_style + """
            QPushButton {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #4299e1, stop:1 #3182ce);
                color: white;
            }
            QPushButton:hover {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #3182ce, stop:1 #2c5282);
            }
            QPushButton:pressed {
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 #2c5282, stop:1 #2a4365);
            }
            QPushButton:disabled {
                background-color: #a0aec0;
                color: #718096;
            }
        """)
        self.login_button.clicked.connect(self._on_login_clicked)
        button_layout.addWidget(self.login_button)

        layout.addLayout(button_layout)

    def _create_footer(self, layout):
        """Create the footer with institution information."""
        footer_layout = QVBoxLayout()
        footer_layout.setAlignment(Qt.AlignCenter)

        # Institution name
        institution_info = app_settings.get_institution_info()
        institution_label = QLabel(institution_info["name"])
        institution_label.setAlignment(Qt.AlignCenter)
        institution_label.setStyleSheet("color: #7f8c8d; font-size: 11px; margin-top: 10px;")
        footer_layout.addWidget(institution_label)

        # Copyright
        copyright_label = QLabel("© 2024 Ajinkyacreatiion PVT. LTD.")
        copyright_label.setAlignment(Qt.AlignCenter)
        copyright_label.setStyleSheet("color: #95a5a6; font-size: 10px;")
        footer_layout.addWidget(copyright_label)

        layout.addLayout(footer_layout)

    def _load_settings(self):
        """Load saved settings."""
        # Load remembered username
        remembered_username = app_settings.get("login/remembered_username", "")
        if remembered_username:
            self.username_edit.setText(remembered_username)
            self.remember_checkbox.setChecked(True)
            self.password_edit.setFocus()
        else:
            self.username_edit.setFocus()

    def _save_settings(self):
        """Save settings."""
        if self.remember_checkbox.isChecked():
            app_settings.set("login/remembered_username", self.username_edit.text())
        else:
            app_settings.set("login/remembered_username", "")

    def _on_login_clicked(self):
        """Handle login button click."""
        username = self.username_edit.text().strip()
        password = self.password_edit.text()

        # Validate input
        if not username:
            QMessageBox.warning(self, "Invalid Input", "Please enter your username.")
            self.username_edit.setFocus()
            return

        if not password:
            QMessageBox.warning(self, "Invalid Input", "Please enter your password.")
            self.password_edit.setFocus()
            return

        # Disable UI during authentication
        self._set_ui_enabled(False)
        self.login_button.setText("Logging in...")

        # Start authentication in background thread
        self._start_authentication(username, password)

    def _start_authentication(self, username: str, password: str):
        """Start authentication in background thread."""
        self.auth_thread = QThread()
        self.auth_worker = LoginWorker(username, password)
        self.auth_worker.moveToThread(self.auth_thread)

        # Connect signals
        self.auth_thread.started.connect(self.auth_worker.authenticate)
        self.auth_worker.finished.connect(self._on_authentication_finished)
        self.auth_worker.finished.connect(self.auth_thread.quit)
        self.auth_worker.finished.connect(self.auth_worker.deleteLater)
        self.auth_thread.finished.connect(self.auth_thread.deleteLater)

        # Start the thread
        self.auth_thread.start()

    def _on_authentication_finished(self, success: bool, message: str):
        """Handle authentication completion."""
        # Re-enable UI
        self._set_ui_enabled(True)
        self.login_button.setText("Login")

        if success:
            # Save settings
            self._save_settings()

            # Accept dialog
            self.accept()
        else:
            # Show error message
            QMessageBox.critical(self, "Login Failed", message)

            # Clear password and focus
            self.password_edit.clear()
            self.password_edit.setFocus()

    def _set_ui_enabled(self, enabled: bool):
        """Enable or disable UI elements."""
        self.username_edit.setEnabled(enabled)
        self.password_edit.setEnabled(enabled)
        self.remember_checkbox.setEnabled(enabled)
        self.login_button.setEnabled(enabled)
        self.cancel_button.setEnabled(enabled)

    def keyPressEvent(self, event):
        """Handle key press events."""
        if event.key() == Qt.Key_Escape:
            self.reject()
        else:
            super().keyPressEvent(event)
