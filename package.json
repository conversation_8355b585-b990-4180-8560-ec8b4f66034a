{"name": "military-peer-review-system", "version": "1.0.0", "description": "Military Peer Review Assessment System - Desktop Application", "main": "desktop/main.js", "author": "Maj. <PERSON><PERSON>", "license": "MIT", "homepage": "./", "scripts": {"start": "electron .", "build": "electron-builder", "build-win": "electron-builder --win", "build-mac": "electron-builder --mac", "build-linux": "electron-builder --linux", "pack": "electron-builder --dir", "dist": "electron-builder --publish=never", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.ajinkyacreatiion.military-peer-review", "productName": "Military Peer Review Assessment System", "directories": {"output": "dist"}, "files": ["desktop/**/*", "src/**/*", "data/**/*", "logs/**/*", "requirements.txt", "*.py", "*.md", "*.txt"], "extraResources": [{"from": "python-runtime", "to": "python-runtime", "filter": ["**/*"]}], "win": {"target": [{"target": "nsis", "arch": ["x64"]}, {"target": "portable", "arch": ["x64"]}], "icon": "desktop/assets/icon.ico", "requestedExecutionLevel": "asInvoker"}, "mac": {"target": "dmg", "icon": "desktop/assets/icon.icns", "category": "public.app-category.education"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "desktop/assets/icon.png", "category": "Education"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "Military Peer Review System", "include": "desktop/installer.nsh"}, "publish": {"provider": "generic", "url": "https://releases.example.com/"}}, "devDependencies": {"electron": "^27.0.0", "electron-builder": "^24.6.4"}, "dependencies": {"electron-log": "^4.4.8", "electron-updater": "^6.1.4", "node-fetch": "^3.3.2"}}