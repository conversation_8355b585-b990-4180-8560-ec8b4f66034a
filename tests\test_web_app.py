"""
Tests for the web application functionality
"""

import pytest
import sys
import os
from unittest.mock import patch, MagicMock

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


def test_web_app_creation():
    """Test that the Flask app can be created."""
    from web_app import create_app
    
    app = create_app()
    assert app is not None
    assert app.config['SECRET_KEY'] is not None


def test_web_app_routes():
    """Test that all routes are registered."""
    from web_app import create_app
    
    app = create_app()
    
    # Get all registered routes
    routes = [rule.rule for rule in app.url_map.iter_rules()]
    
    # Check that essential routes exist
    assert '/' in routes
    assert '/login' in routes
    assert '/logout' in routes
    assert '/dashboard' in routes
    assert '/api/stats' in routes


@patch('web_app.initialize_database')
def test_app_initialization_with_db_failure(mock_init_db):
    """Test app behavior when database initialization fails."""
    mock_init_db.return_value = False
    
    from web_app import create_app
    
    with pytest.raises(RuntimeError, match="Failed to initialize database"):
        create_app()


def test_authentication_function():
    """Test the authentication function."""
    from web_app import authenticate_user
    
    # Test with invalid credentials (should return None)
    result = authenticate_user("nonexistent", "wrongpassword")
    assert result is None


def test_dashboard_stats_function():
    """Test the dashboard statistics function."""
    from web_app import get_dashboard_stats
    
    stats = get_dashboard_stats()
    
    # Should return a dictionary with required keys
    assert isinstance(stats, dict)
    assert 'total_users' in stats
    assert 'total_batches' in stats
    assert 'active_assessments' in stats
    assert 'completed_assessments' in stats


def test_recent_activity_function():
    """Test the recent activity function."""
    from web_app import get_recent_activity
    
    activity = get_recent_activity()
    
    # Should return a list
    assert isinstance(activity, list)
    
    # Each item should have required keys
    if activity:
        for item in activity:
            assert 'time' in item
            assert 'action' in item
            assert 'user' in item


def test_flask_app_config():
    """Test Flask app configuration."""
    from web_app import create_app
    
    app = create_app()
    
    # Check essential configuration
    assert app.config['SESSION_TYPE'] == 'filesystem'
    assert app.config['SESSION_PERMANENT'] is False
    assert app.config['SESSION_USE_SIGNER'] is True
    assert 'peer_review_' in app.config['SESSION_KEY_PREFIX']


def test_template_rendering():
    """Test that templates can be rendered without errors."""
    from web_app import create_app
    
    app = create_app()
    
    with app.test_client() as client:
        # Test login page
        response = client.get('/login')
        assert response.status_code == 200
        assert b'Military Peer Review' in response.data
        
        # Test redirect from home to login
        response = client.get('/')
        assert response.status_code == 302  # Redirect


def test_api_endpoints():
    """Test API endpoints."""
    from web_app import create_app
    
    app = create_app()
    
    with app.test_client() as client:
        # Test stats API without authentication
        response = client.get('/api/stats')
        assert response.status_code == 401  # Unauthorized
        
        # Check JSON response
        data = response.get_json()
        assert 'error' in data
        assert data['error'] == 'Not authenticated'


def test_error_handlers():
    """Test error handlers."""
    from web_app import create_app
    
    app = create_app()
    
    with app.test_client() as client:
        # Test 404 error
        response = client.get('/nonexistent-page')
        assert response.status_code == 404
        assert b'Page Not Found' in response.data or b'404' in response.data


def test_session_management():
    """Test session management functionality."""
    from web_app import create_app
    
    app = create_app()
    
    with app.test_client() as client:
        # Test logout without session
        response = client.get('/logout')
        assert response.status_code == 302  # Redirect to login
        
        # Test dashboard without session
        response = client.get('/dashboard')
        assert response.status_code == 302  # Redirect to login


def test_security_headers():
    """Test that security measures are in place."""
    from web_app import create_app
    
    app = create_app()
    
    # Check that secret key is set
    assert app.config['SECRET_KEY'] is not None
    assert len(app.config['SECRET_KEY']) > 10  # Should be reasonably long


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
