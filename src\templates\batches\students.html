{% extends "base.html" %}

{% block title %}Batch Students - Military Peer Review Assessment System{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">
                        <i class="bi bi-people me-2 text-primary"></i>Batch Students
                    </h1>
                    <p class="text-muted mb-0">
                        <strong>{{ batch.name }}</strong> ({{ batch.code }}) - 
                        {{ batch.academic_year }} Semester {{ batch.semester }}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('batches_list') }}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-arrow-left me-2"></i>Back to Batches
                    </a>
                    {% if current_user.role in ['super_admin', 'admin'] %}
                    <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStudentModal">
                        <i class="bi bi-plus-circle me-2"></i>Add Student
                    </button>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Batch Information Card -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary bg-opacity-10 rounded-3 p-3 me-3">
                                    <i class="bi bi-people-fill text-primary fs-4"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Enrolled</h6>
                                    <h3 class="fw-bold text-primary mb-0">{{ students|length }}</h3>
                                    <small class="text-muted">of {{ batch.max_students }} max</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-success bg-opacity-10 rounded-3 p-3 me-3">
                                    <i class="bi bi-check-circle-fill text-success fs-4"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Active</h6>
                                    <h3 class="fw-bold text-success mb-0">{{ students|selectattr('is_active')|list|length }}</h3>
                                    <small class="text-muted">students</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-info bg-opacity-10 rounded-3 p-3 me-3">
                                    <i class="bi bi-person-workspace text-info fs-4"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Instructor</h6>
                                    <h6 class="fw-bold text-info mb-0">
                                        {% if batch.instructor %}
                                            {{ batch.instructor.full_name }}
                                        {% else %}
                                            Not assigned
                                        {% endif %}
                                    </h6>
                                    <small class="text-muted">{{ batch.course_name or 'No course' }}</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-warning bg-opacity-10 rounded-3 p-3 me-3">
                                    <i class="bi bi-calendar-event text-warning fs-4"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Duration</h6>
                                    <h6 class="fw-bold text-warning mb-0">
                                        {% if batch.start_date and batch.end_date %}
                                            {{ (batch.end_date - batch.start_date).days }} days
                                        {% else %}
                                            Not set
                                        {% endif %}
                                    </h6>
                                    <small class="text-muted">
                                        {% if batch.start_date %}
                                            {{ batch.start_date.strftime('%Y-%m-%d') }}
                                        {% endif %}
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Students Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="fw-semibold mb-0">
                            <i class="bi bi-table me-2 text-primary"></i>Enrolled Students
                        </h5>
                        <div class="d-flex gap-2">
                            <div class="input-group" style="width: 300px;">
                                <input type="text" class="form-control" id="searchStudents" placeholder="Search students...">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                            <select class="form-select" id="filterStatus" style="width: 150px;">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if students %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle" id="studentsTable">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-semibold">Student Details</th>
                                    <th class="border-0 fw-semibold">Military Info</th>
                                    <th class="border-0 fw-semibold">Contact</th>
                                    <th class="border-0 fw-semibold">Enrollment</th>
                                    <th class="border-0 fw-semibold">Status</th>
                                    <th class="border-0 fw-semibold text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for student in students %}
                                <tr data-status="{{ 'active' if student.is_active else 'inactive' }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                                <i class="bi bi-person text-primary"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-semibold">{{ student.full_name }}</h6>
                                                <small class="text-muted">ID: {{ student.student_id }}</small>
                                                {% if student.service_number %}
                                                <br><small class="text-muted">Service: {{ student.service_number }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if student.rank %}
                                            <div class="fw-semibold">{{ student.rank }}</div>
                                        {% endif %}
                                        {% if student.unit %}
                                            <small class="text-muted">{{ student.unit }}</small>
                                        {% endif %}
                                        {% if student.branch %}
                                            <br><small class="text-muted">{{ student.branch }}</small>
                                        {% endif %}
                                        {% if not student.rank and not student.unit and not student.branch %}
                                            <span class="text-muted">Not specified</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if student.email %}
                                            <div class="fw-semibold">{{ student.email }}</div>
                                        {% endif %}
                                        {% if student.phone %}
                                            <small class="text-muted">{{ student.phone }}</small>
                                        {% endif %}
                                        {% if not student.email and not student.phone %}
                                            <span class="text-muted">Not provided</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="fw-semibold">{{ student.enrollment_date.strftime('%Y-%m-%d') if student.enrollment_date else 'N/A' }}</div>
                                        {% if student.graduation_date %}
                                            <small class="text-muted">Graduated: {{ student.graduation_date.strftime('%Y-%m-%d') }}</small>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if student.is_active %}
                                            <span class="badge bg-success">{{ student.status|title if student.status else 'Active' }}</span>
                                        {% else %}
                                            <span class="badge bg-secondary">{{ student.status|title if student.status else 'Inactive' }}</span>
                                        {% endif %}
                                        {% if student.overall_grade %}
                                            <br><small class="text-muted">Grade: {{ student.overall_grade }}</small>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            {% if current_user.role in ['super_admin', 'admin'] %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-primary" 
                                                    onclick="editStudent({{ student.id }})"
                                                    title="Edit Student">
                                                <i class="bi bi-pencil"></i>
                                            </button>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    onclick="removeStudent({{ student.id }}, '{{ student.full_name }}')"
                                                    title="Remove Student">
                                                <i class="bi bi-person-dash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <div class="mb-3">
                            <i class="bi bi-people text-muted" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="text-muted">No Students Enrolled</h5>
                        <p class="text-muted">This batch doesn't have any students enrolled yet.</p>
                        {% if current_user.role in ['super_admin', 'admin'] %}
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addStudentModal">
                            <i class="bi bi-plus-circle me-2"></i>Add First Student
                        </button>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Student Modal -->
{% if current_user.role in ['super_admin', 'admin'] %}
<div class="modal fade" id="addStudentModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person-plus me-2"></i>Add Student to Batch
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="addStudentForm">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label for="student_id" class="form-label fw-semibold">
                                Student ID <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="student_id" name="student_id" required>
                        </div>
                        <div class="col-md-6">
                            <label for="service_number" class="form-label fw-semibold">
                                Service Number
                            </label>
                            <input type="text" class="form-control" id="service_number" name="service_number">
                        </div>
                        <div class="col-md-12">
                            <label for="full_name" class="form-label fw-semibold">
                                Full Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="full_name" name="full_name" required>
                        </div>
                        <div class="col-md-6">
                            <label for="email" class="form-label fw-semibold">Email</label>
                            <input type="email" class="form-control" id="email" name="email">
                        </div>
                        <div class="col-md-6">
                            <label for="phone" class="form-label fw-semibold">Phone</label>
                            <input type="text" class="form-control" id="phone" name="phone">
                        </div>
                        <div class="col-md-4">
                            <label for="rank" class="form-label fw-semibold">Rank</label>
                            <input type="text" class="form-control" id="rank" name="rank">
                        </div>
                        <div class="col-md-4">
                            <label for="unit" class="form-label fw-semibold">Unit</label>
                            <input type="text" class="form-control" id="unit" name="unit">
                        </div>
                        <div class="col-md-4">
                            <label for="branch" class="form-label fw-semibold">Branch</label>
                            <input type="text" class="form-control" id="branch" name="branch">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="addStudent()">Add Student</button>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block extra_scripts %}
<script>
    // Search functionality
    document.getElementById('searchStudents').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('#studentsTable tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    // Status filter functionality
    document.getElementById('filterStatus').addEventListener('change', function() {
        const selectedStatus = this.value;
        const rows = document.querySelectorAll('#studentsTable tbody tr');
        
        rows.forEach(row => {
            const status = row.getAttribute('data-status');
            row.style.display = (!selectedStatus || status === selectedStatus) ? '' : 'none';
        });
    });

    // Add student function
    function addStudent() {
        const form = document.getElementById('addStudentForm');
        const formData = new FormData(form);
        
        // Add batch ID
        formData.append('batch_id', {{ batch.id }});
        
        fetch('/api/students/add', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while adding the student.');
        });
    }

    // Edit student function
    function editStudent(studentId) {
        // Implement edit functionality
        alert('Edit student functionality will be implemented in the assessment system.');
    }

    // Remove student function
    function removeStudent(studentId, studentName) {
        if (confirm(`Are you sure you want to remove "${studentName}" from this batch?`)) {
            fetch(`/api/students/${studentId}/remove`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while removing the student.');
            });
        }
    }
</script>
{% endblock %}
