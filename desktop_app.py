#!/usr/bin/env python3
"""
Military Peer Review Assessment System - Desktop Application Wrapper

This module provides a desktop application wrapper for the web-based
peer review system using Python's built-in libraries.

Author: Maj<PERSON> <PERSON><PERSON>
Developer: <PERSON><PERSON><PERSON><PERSON>h <PERSON>e
Company: Ajinkyacreatiion PVT. LTD.
"""

import os
import sys
import time
import threading
import webbrowser
import tkinter as tk
from tkinter import ttk, messagebox
import subprocess
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))


class PeerReviewDesktopApp:
    """Desktop wrapper for the Military Peer Review Assessment System."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.web_process = None
        self.server_running = False
        self.setup_ui()
        
    def setup_ui(self):
        """Setup the desktop application UI."""
        # Configure main window
        self.root.title("Military Peer Review Assessment System")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Set window icon (if available)
        try:
            icon_path = Path("resources/icons/app_icon.ico")
            if icon_path.exists():
                self.root.iconbitmap(str(icon_path))
        except:
            pass  # Icon not available, continue without it
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Main frame
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # Title
        title_label = ttk.Label(
            header_frame, 
            text="Military Peer Review Assessment System",
            font=("Arial", 16, "bold")
        )
        title_label.grid(row=0, column=0, sticky=tk.W)
        
        # Subtitle
        subtitle_label = ttk.Label(
            header_frame,
            text="Desktop Application",
            font=("Arial", 10)
        )
        subtitle_label.grid(row=1, column=0, sticky=tk.W)
        
        # Status frame
        status_frame = ttk.LabelFrame(main_frame, text="Server Status", padding="10")
        status_frame.grid(row=1, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        status_frame.columnconfigure(1, weight=1)
        
        # Status indicator
        self.status_label = ttk.Label(status_frame, text="Server Status:")
        self.status_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        
        self.status_indicator = ttk.Label(
            status_frame, 
            text="Stopped", 
            foreground="red",
            font=("Arial", 10, "bold")
        )
        self.status_indicator.grid(row=0, column=1, sticky=tk.W)
        
        # URL display
        self.url_label = ttk.Label(status_frame, text="")
        self.url_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, pady=(5, 0))
        
        # Control buttons frame
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=2, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # Start server button
        self.start_button = ttk.Button(
            control_frame,
            text="Start Server",
            command=self.start_server,
            width=15
        )
        self.start_button.grid(row=0, column=0, padx=(0, 10))
        
        # Stop server button
        self.stop_button = ttk.Button(
            control_frame,
            text="Stop Server",
            command=self.stop_server,
            state=tk.DISABLED,
            width=15
        )
        self.stop_button.grid(row=0, column=1, padx=(0, 10))
        
        # Open browser button
        self.browser_button = ttk.Button(
            control_frame,
            text="Open in Browser",
            command=self.open_browser,
            state=tk.DISABLED,
            width=15
        )
        self.browser_button.grid(row=0, column=2)
        
        # Information frame
        info_frame = ttk.LabelFrame(main_frame, text="Application Information", padding="10")
        info_frame.grid(row=3, column=0, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 20))
        info_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(3, weight=1)
        
        # Information text
        info_text = tk.Text(
            info_frame,
            wrap=tk.WORD,
            height=15,
            width=70,
            font=("Arial", 10)
        )
        info_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Scrollbar for text
        scrollbar = ttk.Scrollbar(info_frame, orient=tk.VERTICAL, command=info_text.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        info_text.configure(yscrollcommand=scrollbar.set)
        
        # Insert information text
        info_content = """
MILITARY PEER REVIEW ASSESSMENT SYSTEM
======================================

This desktop application provides a local interface to the Military Peer Review Assessment System.

FEATURES:
• User Management with Role-based Access Control
• Student Batch Management
• Peer Assessment Creation and Management
• Comprehensive Reporting and Analytics
• Secure Authentication and Session Management
• Responsive Web Interface

DEFAULT LOGIN CREDENTIALS:
Username: admin
Password: Admin@123

IMPORTANT: Please change the default password after first login for security.

SYSTEM REQUIREMENTS:
• Python 3.8 or higher
• Modern web browser (Chrome, Firefox, Safari, Edge)
• Minimum 4GB RAM
• 1GB free disk space

USAGE INSTRUCTIONS:
1. Click "Start Server" to launch the web application
2. Click "Open in Browser" to access the system
3. Login with the default credentials
4. Create users, manage batches, and conduct assessments

SUPPORT:
Author: Maj. Sachin Kumar Singh
Developer: Hrishikesh Mohite
Company: Ajinkyacreatiion PVT. LTD.

For technical support or questions, please contact your system administrator.
        """
        
        info_text.insert(tk.END, info_content.strip())
        info_text.configure(state=tk.DISABLED)
        
        # Footer
        footer_frame = ttk.Frame(main_frame)
        footer_frame.grid(row=4, column=0, columnspan=2, sticky=(tk.W, tk.E))
        
        footer_label = ttk.Label(
            footer_frame,
            text="© 2024 Ajinkyacreatiion PVT. LTD. - Military Peer Review Assessment System v1.0",
            font=("Arial", 8)
        )
        footer_label.grid(row=0, column=0, sticky=tk.W)
        
        # Bind window close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def start_server(self):
        """Start the web server."""
        try:
            # Start the web application in a separate thread
            self.server_thread = threading.Thread(target=self.run_web_server, daemon=True)
            self.server_thread.start()
            
            # Update UI
            self.start_button.configure(state=tk.DISABLED)
            self.stop_button.configure(state=tk.NORMAL)
            self.browser_button.configure(state=tk.NORMAL)
            
            self.status_indicator.configure(text="Starting...", foreground="orange")
            self.url_label.configure(text="Server URL: http://localhost:5000")
            
            # Check server status after a delay
            self.root.after(3000, self.check_server_status)
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start server: {e}")
    
    def run_web_server(self):
        """Run the web server in a separate process."""
        try:
            # Change to the application directory
            app_dir = os.path.dirname(os.path.abspath(__file__))
            
            # Start the web application
            self.web_process = subprocess.Popen(
                [sys.executable, "src/web_app.py"],
                cwd=app_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            self.server_running = True
            
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("Error", f"Failed to start web server: {e}"))
    
    def stop_server(self):
        """Stop the web server."""
        try:
            if self.web_process:
                self.web_process.terminate()
                self.web_process = None
            
            self.server_running = False
            
            # Update UI
            self.start_button.configure(state=tk.NORMAL)
            self.stop_button.configure(state=tk.DISABLED)
            self.browser_button.configure(state=tk.DISABLED)
            
            self.status_indicator.configure(text="Stopped", foreground="red")
            self.url_label.configure(text="")
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to stop server: {e}")
    
    def check_server_status(self):
        """Check if the server is running."""
        if self.server_running and self.web_process:
            if self.web_process.poll() is None:  # Process is still running
                self.status_indicator.configure(text="Running", foreground="green")
            else:  # Process has terminated
                self.status_indicator.configure(text="Error", foreground="red")
                messagebox.showerror("Error", "Web server failed to start. Check the logs for details.")
                self.stop_server()
    
    def open_browser(self):
        """Open the web application in the default browser."""
        try:
            webbrowser.open("http://localhost:5000")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to open browser: {e}")
    
    def on_closing(self):
        """Handle application closing."""
        if self.server_running:
            if messagebox.askokcancel("Quit", "The server is still running. Do you want to stop it and quit?"):
                self.stop_server()
                self.root.destroy()
        else:
            self.root.destroy()
    
    def run(self):
        """Run the desktop application."""
        self.root.mainloop()


def main():
    """Main function to run the desktop application."""
    try:
        app = PeerReviewDesktopApp()
        app.run()
    except Exception as e:
        print(f"Failed to start desktop application: {e}")
        input("Press Enter to exit...")


if __name__ == "__main__":
    main()
