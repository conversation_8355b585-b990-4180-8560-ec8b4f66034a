<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Military Peer Review Assessment System - CLI</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/xterm/4.19.0/xterm.min.css">
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #1e1e1e;
            color: #ffffff;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            padding: 10px 20px;
            border-bottom: 2px solid #ffd700;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
        }

        .header h1 {
            margin: 0;
            font-size: 18px;
            font-weight: bold;
            display: flex;
            align-items: center;
        }

        .header .icon {
            margin-right: 10px;
            font-size: 20px;
        }

        .header .subtitle {
            font-size: 12px;
            opacity: 0.8;
            margin-top: 2px;
        }

        .terminal-container {
            height: calc(100vh - 80px);
            padding: 10px;
            background-color: #1e1e1e;
        }

        .terminal {
            width: 100%;
            height: 100%;
            background-color: #000000;
            border: 1px solid #333333;
            border-radius: 4px;
        }

        .status-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background-color: #2d2d30;
            color: #cccccc;
            padding: 5px 20px;
            font-size: 12px;
            border-top: 1px solid #3e3e42;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .status-item {
            display: flex;
            align-items: center;
        }

        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-indicator.connected {
            background-color: #4caf50;
        }

        .status-indicator.disconnected {
            background-color: #f44336;
        }

        .loading {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            flex-direction: column;
            color: #cccccc;
        }

        .loading .spinner {
            border: 3px solid #333;
            border-top: 3px solid #ffd700;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-message {
            color: #f44336;
            text-align: center;
            padding: 20px;
            background-color: #2d1b1b;
            border: 1px solid #f44336;
            border-radius: 4px;
            margin: 20px;
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #2d2d30;
        }

        ::-webkit-scrollbar-thumb {
            background: #555;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #777;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>
            <span class="icon">🎖️</span>
            Military Peer Review Assessment System
        </h1>
        <div class="subtitle">Command Line Interface - Version 1.0.0</div>
    </div>

    <div class="terminal-container">
        <div id="loading" class="loading">
            <div class="spinner"></div>
            <div>Starting Military Peer Review Assessment System...</div>
            <div style="font-size: 12px; margin-top: 10px; opacity: 0.7;">
                Initializing database and CLI interface...
            </div>
        </div>
        <div id="terminal" class="terminal" style="display: none;"></div>
        <div id="error" style="display: none;">
            <div class="error-message">
                <h3>⚠️ Connection Error</h3>
                <p>Failed to start the CLI application. Please check the logs and try restarting.</p>
                <p><strong>Troubleshooting:</strong></p>
                <ul style="text-align: left; display: inline-block;">
                    <li>Ensure Python is installed and accessible</li>
                    <li>Check that all dependencies are installed</li>
                    <li>Try restarting the application</li>
                    <li>Check the application logs for detailed error information</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="status-bar">
        <div class="status-item">
            <div id="status-indicator" class="status-indicator disconnected"></div>
            <span id="status-text">Connecting...</span>
        </div>
        <div class="status-item">
            <span>CLI Version 1.0.0 | Author: Maj. Sachin Kumar Singh | Developer: Hrishikesh Mohite | © 2024 Ajinkyacreatiion PVT. LTD.</span>
        </div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/xterm/4.19.0/xterm.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xterm/4.19.0/addons/fit/xterm-addon-fit.min.js"></script>
    <script>
        const { ipcRenderer } = require('electron');

        class TerminalInterface {
            constructor() {
                this.terminal = null;
                this.fitAddon = null;
                this.isConnected = false;
                this.initializeTerminal();
                this.setupEventListeners();
            }

            initializeTerminal() {
                // Create xterm terminal
                this.terminal = new Terminal({
                    cursorBlink: true,
                    cursorStyle: 'block',
                    fontSize: 14,
                    fontFamily: 'Consolas, Monaco, "Courier New", monospace',
                    theme: {
                        background: '#000000',
                        foreground: '#ffffff',
                        cursor: '#ffffff',
                        selection: '#3e3e42',
                        black: '#000000',
                        red: '#f44336',
                        green: '#4caf50',
                        yellow: '#ffd700',
                        blue: '#2196f3',
                        magenta: '#e91e63',
                        cyan: '#00bcd4',
                        white: '#ffffff',
                        brightBlack: '#666666',
                        brightRed: '#ff5722',
                        brightGreen: '#8bc34a',
                        brightYellow: '#ffeb3b',
                        brightBlue: '#03a9f4',
                        brightMagenta: '#ff4081',
                        brightCyan: '#26c6da',
                        brightWhite: '#ffffff'
                    },
                    cols: 120,
                    rows: 30
                });

                // Add fit addon
                this.fitAddon = new FitAddon.FitAddon();
                this.terminal.loadAddon(this.fitAddon);

                // Open terminal in container
                const terminalElement = document.getElementById('terminal');
                this.terminal.open(terminalElement);

                // Fit terminal to container
                this.fitAddon.fit();

                // Handle terminal input
                this.terminal.onData((data) => {
                    if (this.isConnected) {
                        ipcRenderer.send('terminal-input', data);
                    }
                });

                // Handle terminal resize
                this.terminal.onResize((size) => {
                    ipcRenderer.send('terminal-resize', size.cols, size.rows);
                });

                // Start CLI application
                this.startCLI();
            }

            setupEventListeners() {
                // Handle terminal output from main process
                ipcRenderer.on('terminal-output', (event, data) => {
                    if (this.terminal) {
                        this.terminal.write(data);
                    }
                });

                // Handle clear terminal
                ipcRenderer.on('clear-terminal', () => {
                    if (this.terminal) {
                        this.terminal.clear();
                    }
                });

                // Handle window resize
                window.addEventListener('resize', () => {
                    if (this.fitAddon) {
                        this.fitAddon.fit();
                    }
                });

                // Handle keyboard shortcuts
                document.addEventListener('keydown', (event) => {
                    // Ctrl+K to clear terminal
                    if (event.ctrlKey && event.key === 'k') {
                        event.preventDefault();
                        if (this.terminal) {
                            this.terminal.clear();
                        }
                    }
                });
            }

            async startCLI() {
                try {
                    this.updateStatus('Connecting...', false);

                    const result = await ipcRenderer.invoke('start-cli');

                    if (result.success) {
                        this.showTerminal();
                        this.updateStatus('Connected', true);
                        this.isConnected = true;

                        // Welcome message
                        setTimeout(() => {
                            this.terminal.writeln('\x1b[1;32m🎖️  Military Peer Review Assessment System - CLI\x1b[0m');
                            this.terminal.writeln('\x1b[36mAuthor: Maj. Sachin Kumar Singh | Developer: Hrishikesh Mohite\x1b[0m');
                            this.terminal.writeln('\x1b[36m© 2024 Ajinkyacreatiion PVT. LTD.\x1b[0m');
                            this.terminal.writeln('');
                        }, 1000);
                    } else {
                        this.showError();
                        this.updateStatus('Connection Failed', false);
                    }
                } catch (error) {
                    console.error('Failed to start CLI:', error);
                    this.showError();
                    this.updateStatus('Connection Failed', false);
                }
            }

            showTerminal() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('error').style.display = 'none';
                document.getElementById('terminal').style.display = 'block';

                // Focus terminal
                this.terminal.focus();

                // Fit terminal after showing
                setTimeout(() => {
                    this.fitAddon.fit();
                }, 100);
            }

            showError() {
                document.getElementById('loading').style.display = 'none';
                document.getElementById('terminal').style.display = 'none';
                document.getElementById('error').style.display = 'block';
            }

            updateStatus(text, connected) {
                const statusIndicator = document.getElementById('status-indicator');
                const statusText = document.getElementById('status-text');

                statusText.textContent = text;
                statusIndicator.className = `status-indicator ${connected ? 'connected' : 'disconnected'}`;
                this.isConnected = connected;
            }
        }

        // Initialize terminal interface when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            new TerminalInterface();
        });

        // Handle app focus
        window.addEventListener('focus', () => {
            // Focus terminal when window gains focus
            setTimeout(() => {
                const terminalInterface = window.terminalInterface;
                if (terminalInterface && terminalInterface.terminal) {
                    terminalInterface.terminal.focus();
                }
            }, 100);
        });
    </script>
</body>
</html>
