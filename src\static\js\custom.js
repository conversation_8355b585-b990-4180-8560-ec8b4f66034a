/**
 * Military Peer Review Assessment System - Custom JavaScript
 * 
 * Author: Maj<PERSON> <PERSON><PERSON>
 * Developer: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * Company: Ajinkyacreatiion PVT. LTD.
 */

// Global application object
window.PeerReviewApp = {
    // Configuration
    config: {
        refreshInterval: 300000, // 5 minutes
        alertTimeout: 5000, // 5 seconds
        apiEndpoints: {
            stats: '/api/stats',
            activity: '/api/activity'
        }
    },

    // Initialize application
    init: function() {
        this.setupEventListeners();
        this.initializeComponents();
        this.startPeriodicUpdates();
        console.log('Peer Review Application initialized');
    },

    // Setup global event listeners
    setupEventListeners: function() {
        // Handle form submissions with loading states
        document.addEventListener('submit', this.handleFormSubmit);
        
        // Handle navigation clicks
        document.addEventListener('click', this.handleNavigation);
        
        // Handle window resize for responsive adjustments
        window.addEventListener('resize', this.handleResize);
        
        // Handle visibility change for pausing updates
        document.addEventListener('visibilitychange', this.handleVisibilityChange);
    },

    // Initialize UI components
    initializeComponents: function() {
        // Initialize tooltips
        this.initializeTooltips();
        
        // Initialize popovers
        this.initializePopovers();
        
        // Add fade-in animation to cards
        this.animateCards();
        
        // Setup auto-hide alerts
        this.setupAutoHideAlerts();
    },

    // Initialize Bootstrap tooltips
    initializeTooltips: function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    },

    // Initialize Bootstrap popovers
    initializePopovers: function() {
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        popoverTriggerList.map(function(popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
    },

    // Animate cards on page load
    animateCards: function() {
        const cards = document.querySelectorAll('.card');
        cards.forEach((card, index) => {
            setTimeout(() => {
                card.classList.add('fade-in');
            }, index * 100);
        });
    },

    // Setup auto-hide alerts
    setupAutoHideAlerts: function() {
        const alerts = document.querySelectorAll('.alert:not(.alert-permanent)');
        alerts.forEach(alert => {
            setTimeout(() => {
                const bsAlert = new bootstrap.Alert(alert);
                if (bsAlert) {
                    bsAlert.close();
                }
            }, this.config.alertTimeout);
        });
    },

    // Handle form submissions
    handleFormSubmit: function(event) {
        const form = event.target;
        if (form.tagName === 'FORM') {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                // Add loading state
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
                submitBtn.disabled = true;
                
                // Reset after 10 seconds (fallback)
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }, 10000);
            }
        }
    },

    // Handle navigation clicks
    handleNavigation: function(event) {
        const target = event.target.closest('a[href]');
        if (target && target.href && !target.href.startsWith('#')) {
            // Add loading indicator for navigation
            const loadingIndicator = document.createElement('div');
            loadingIndicator.className = 'position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
            loadingIndicator.style.backgroundColor = 'rgba(255, 255, 255, 0.8)';
            loadingIndicator.style.zIndex = '9999';
            loadingIndicator.innerHTML = '<div class="spinner-border text-primary"></div>';
            
            // Remove after 5 seconds (fallback)
            setTimeout(() => {
                if (loadingIndicator.parentNode) {
                    loadingIndicator.remove();
                }
            }, 5000);
        }
    },

    // Handle window resize
    handleResize: function() {
        // Adjust layout for mobile devices
        const isMobile = window.innerWidth < 768;
        document.body.classList.toggle('mobile-layout', isMobile);
    },

    // Handle visibility change
    handleVisibilityChange: function() {
        if (document.hidden) {
            // Pause updates when tab is not visible
            PeerReviewApp.pauseUpdates();
        } else {
            // Resume updates when tab becomes visible
            PeerReviewApp.resumeUpdates();
        }
    },

    // Start periodic updates
    startPeriodicUpdates: function() {
        this.updateInterval = setInterval(() => {
            this.refreshDashboardData();
        }, this.config.refreshInterval);
    },

    // Pause updates
    pauseUpdates: function() {
        if (this.updateInterval) {
            clearInterval(this.updateInterval);
            this.updateInterval = null;
        }
    },

    // Resume updates
    resumeUpdates: function() {
        if (!this.updateInterval) {
            this.startPeriodicUpdates();
        }
    },

    // Refresh dashboard data
    refreshDashboardData: function() {
        if (document.getElementById('totalUsers')) {
            this.updateStatistics();
        }
    },

    // Update statistics
    updateStatistics: function() {
        fetch(this.config.apiEndpoints.stats)
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (!data.error) {
                    this.updateStatElement('totalUsers', data.total_users);
                    this.updateStatElement('totalBatches', data.total_batches);
                    this.updateStatElement('activeAssessments', data.active_assessments);
                    this.updateStatElement('completedAssessments', data.completed_assessments);
                }
            })
            .catch(error => {
                console.warn('Failed to refresh statistics:', error);
            });
    },

    // Update individual stat element with animation
    updateStatElement: function(elementId, newValue) {
        const element = document.getElementById(elementId);
        if (element && element.textContent !== String(newValue)) {
            element.style.transform = 'scale(1.1)';
            element.style.transition = 'transform 0.3s ease';
            
            setTimeout(() => {
                element.textContent = newValue || 0;
                element.style.transform = 'scale(1)';
            }, 150);
        }
    },

    // Show notification
    showNotification: function(message, type = 'info', duration = 5000) {
        const alertContainer = document.createElement('div');
        alertContainer.className = 'position-fixed top-0 end-0 p-3';
        alertContainer.style.zIndex = '9999';
        
        const alertElement = document.createElement('div');
        alertElement.className = `alert alert-${type} alert-dismissible fade show`;
        alertElement.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        alertContainer.appendChild(alertElement);
        document.body.appendChild(alertContainer);
        
        // Auto-remove after duration
        setTimeout(() => {
            const bsAlert = new bootstrap.Alert(alertElement);
            bsAlert.close();
            setTimeout(() => {
                if (alertContainer.parentNode) {
                    alertContainer.remove();
                }
            }, 500);
        }, duration);
    },

    // Show coming soon modal
    showComingSoon: function(feature = 'This feature') {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="bi bi-info-circle me-2"></i>Coming Soon
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <i class="bi bi-tools text-primary mb-3" style="font-size: 3rem;"></i>
                        <h6>${feature} is under development</h6>
                        <p class="text-muted mb-0">We're working hard to bring you this feature. Stay tuned!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Got it</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        const bsModal = new bootstrap.Modal(modal);
        bsModal.show();
        
        // Remove modal from DOM after it's hidden
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    },

    // Utility functions
    utils: {
        // Format date
        formatDate: function(date) {
            return new Intl.DateTimeFormat('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            }).format(date);
        },

        // Debounce function
        debounce: function(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        // Check if element is in viewport
        isInViewport: function(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        }
    }
};

// Global function for coming soon features
function showComingSoon(feature) {
    PeerReviewApp.showComingSoon(feature);
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    PeerReviewApp.init();
});

// Handle page unload
window.addEventListener('beforeunload', function() {
    PeerReviewApp.pauseUpdates();
});
