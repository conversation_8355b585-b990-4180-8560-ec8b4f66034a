<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Military Peer Review Assessment System - Loading</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }
        
        .loading-container {
            text-align: center;
            max-width: 600px;
            padding: 2rem;
        }
        
        .logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            margin: 0 auto 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 3rem;
            border: 3px solid rgba(255, 255, 255, 0.2);
        }
        
        .logo::before {
            content: "🎖️";
        }
        
        h1 {
            font-size: 2.5rem;
            font-weight: 300;
            margin-bottom: 0.5rem;
            letter-spacing: 1px;
        }
        
        .subtitle {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-bottom: 3rem;
            font-weight: 300;
        }
        
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 2rem;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .loading-text {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 1rem;
        }
        
        .progress-bar {
            width: 300px;
            height: 4px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 2px;
            margin: 0 auto 2rem;
            overflow: hidden;
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            border-radius: 2px;
            animation: progress 3s ease-in-out infinite;
        }
        
        @keyframes progress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
        
        .status-text {
            font-size: 0.9rem;
            opacity: 0.7;
            font-style: italic;
        }
        
        .footer {
            position: absolute;
            bottom: 2rem;
            text-align: center;
            font-size: 0.8rem;
            opacity: 0.6;
        }
        
        .error-container {
            display: none;
            background: rgba(244, 67, 54, 0.1);
            border: 1px solid rgba(244, 67, 54, 0.3);
            border-radius: 8px;
            padding: 1.5rem;
            margin-top: 2rem;
            text-align: left;
        }
        
        .error-title {
            color: #ff6b6b;
            font-weight: 600;
            margin-bottom: 0.5rem;
        }
        
        .error-message {
            font-size: 0.9rem;
            line-height: 1.4;
            opacity: 0.9;
        }
        
        .retry-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 0.8rem 2rem;
            border-radius: 4px;
            font-size: 1rem;
            cursor: pointer;
            margin-top: 1rem;
            transition: background 0.3s ease;
        }
        
        .retry-button:hover {
            background: #45a049;
        }
        
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div class="loading-container fade-in">
        <div class="logo"></div>
        <h1>Military Peer Review Assessment System</h1>
        <p class="subtitle">Professional Military Education Platform</p>
        
        <div class="loading-spinner"></div>
        <div class="loading-text" id="loadingText">Initializing application...</div>
        
        <div class="progress-bar">
            <div class="progress-fill"></div>
        </div>
        
        <div class="status-text" id="statusText">Starting Python server...</div>
        
        <div class="error-container" id="errorContainer">
            <div class="error-title">Startup Error</div>
            <div class="error-message" id="errorMessage"></div>
            <button class="retry-button" onclick="retryStartup()">Retry</button>
        </div>
    </div>
    
    <div class="footer">
        <div>Author: Maj. Sachin Kumar Singh | Developer: Hrishikesh Mohite</div>
        <div>© 2024 Ajinkyacreatiion PVT. LTD. - All Rights Reserved</div>
    </div>

    <script>
        let loadingSteps = [
            "Initializing application...",
            "Starting Python server...",
            "Loading database...",
            "Preparing user interface...",
            "Almost ready..."
        ];
        
        let currentStep = 0;
        let loadingInterval;
        
        function updateLoadingText() {
            const loadingText = document.getElementById('loadingText');
            const statusText = document.getElementById('statusText');
            
            if (currentStep < loadingSteps.length) {
                loadingText.textContent = loadingSteps[currentStep];
                statusText.textContent = getStatusMessage(currentStep);
                currentStep++;
            } else {
                currentStep = 0; // Reset for continuous animation
            }
        }
        
        function getStatusMessage(step) {
            const messages = [
                "Setting up application environment...",
                "Connecting to backend services...",
                "Verifying database integrity...",
                "Loading application resources...",
                "Finalizing startup sequence..."
            ];
            return messages[step] || "Please wait...";
        }
        
        function showError(message) {
            clearInterval(loadingInterval);
            document.querySelector('.loading-spinner').style.display = 'none';
            document.querySelector('.progress-bar').style.display = 'none';
            document.getElementById('loadingText').textContent = 'Startup Failed';
            document.getElementById('statusText').textContent = 'An error occurred during startup';
            document.getElementById('errorMessage').textContent = message;
            document.getElementById('errorContainer').style.display = 'block';
        }
        
        function retryStartup() {
            location.reload();
        }
        
        // Start loading animation
        loadingInterval = setInterval(updateLoadingText, 1500);
        
        // Simulate startup process
        setTimeout(() => {
            if (window.electronAPI) {
                // Check server status periodically
                const checkServer = setInterval(async () => {
                    try {
                        const status = await window.electronAPI.getServerStatus();
                        if (status.running) {
                            clearInterval(checkServer);
                            clearInterval(loadingInterval);
                            document.getElementById('loadingText').textContent = 'Ready!';
                            document.getElementById('statusText').textContent = 'Redirecting to application...';
                        }
                    } catch (error) {
                        clearInterval(checkServer);
                        showError('Failed to start the application server. Please ensure Python is installed and try again.');
                    }
                }, 1000);
                
                // Timeout after 30 seconds
                setTimeout(() => {
                    clearInterval(checkServer);
                    showError('Startup timeout. The application is taking longer than expected to start.');
                }, 30000);
            }
        }, 2000);
        
        // Handle window focus
        window.addEventListener('focus', () => {
            document.body.style.animation = 'none';
            document.body.offsetHeight; // Trigger reflow
            document.body.style.animation = null;
        });
    </script>
</body>
</html>
