"""
Reports CLI Module

Provides command-line interface for reporting and analytics.
"""

import os
import csv
from typing import List, Optional
from datetime import datetime, date
from core.exceptions import ValidationError, DatabaseError


class ReportsCLI:
    """CLI for reports and analytics."""
    
    def __init__(self, main_app):
        self.app = main_app
        self.reporting_service = main_app.reporting_service
        self.assessment_service = main_app.assessment_service
        self.batch_service = main_app.batch_service
        self.user_service = main_app.user_service
    
    def show_menu(self):
        """Show reports menu."""
        while True:
            self.app.clear_screen()
            print(f"\n{self.app.CLIColors.BOLD}📊 REPORTS & ANALYTICS{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            menu_options = []
            
            # Role-based menu options
            if self.app.current_user.role in ['super_admin', 'admin', 'teacher']:
                menu_options.extend([
                    "📈 Dashboard Overview",
                    "📝 Assessment Reports",
                    "👥 Student Reports",
                    "📚 Batch Reports",
                    "📊 System Statistics",
                    "💾 Export Data"
                ])
            elif self.app.current_user.role == 'student':
                menu_options.extend([
                    "📊 My Performance",
                    "📈 My Progress"
                ])
            
            menu_options.append("🔙 Back to Main Menu")
            
            choice = self.app.get_choice("Select an option:", menu_options)
            
            if choice == -1 or choice == len(menu_options) - 1:  # Back or cancelled
                break
            
            if self.app.current_user.role in ['super_admin', 'admin', 'teacher']:
                if choice == 0:  # Dashboard Overview
                    self.show_dashboard_overview()
                elif choice == 1:  # Assessment Reports
                    self.show_assessment_reports()
                elif choice == 2:  # Student Reports
                    self.show_student_reports()
                elif choice == 3:  # Batch Reports
                    self.show_batch_reports()
                elif choice == 4:  # System Statistics
                    self.show_system_statistics()
                elif choice == 5:  # Export Data
                    self.export_data()
            elif self.app.current_user.role == 'student':
                if choice == 0:  # My Performance
                    self.show_my_performance()
                elif choice == 1:  # My Progress
                    self.show_my_progress()
    
    def show_dashboard_overview(self):
        """Show dashboard overview."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}📈 DASHBOARD OVERVIEW{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            stats = self.reporting_service.get_dashboard_statistics()
            
            print(f"System Overview:")
            print(f"  Total Assessments: {stats.get('total_assessments', 0)}")
            print(f"  Active Assessments: {stats.get('active_assessments', 0)}")
            print(f"  Total Evaluations: {stats.get('total_evaluations', 0)}")
            print(f"  Active Students: {stats.get('active_students', 0)}")
            print(f"  Average Score: {stats.get('average_score', 0):.1f}")
            
            # Assessment types distribution
            assessment_types = stats.get('assessment_types', {})
            if assessment_types:
                print(f"\nAssessment Types:")
                for assessment_type, count in assessment_types.items():
                    print(f"  {assessment_type.replace('_', ' ').title()}: {count}")
            
            # Performance trends
            trend_data = stats.get('trend_data', [])
            if trend_data:
                print(f"\nRecent Performance Trends:")
                for date_str, avg_score in trend_data[-5:]:  # Last 5 data points
                    print(f"  {date_str}: {avg_score:.1f}")
            
        except Exception as e:
            self.app.print_error(f"Failed to show dashboard overview: {e}")
        
        self.app.wait_for_enter()
    
    def show_assessment_reports(self):
        """Show assessment reports."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}📝 ASSESSMENT REPORTS{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # Get all assessments
            assessments = self.assessment_service.get_all_assessments()
            
            if not assessments:
                self.app.print_info("No assessments found.")
                self.app.wait_for_enter()
                return
            
            # Select assessment
            assessment_options = [f"{a.title} (ID: {a.id}) - {a.batch.name if a.batch else 'No Batch'}" for a in assessments]
            assessment_choice = self.app.get_choice("Select Assessment:", assessment_options)
            if assessment_choice == -1:
                return
            
            assessment = assessments[assessment_choice]
            
            # Generate report
            report_data = self.reporting_service.generate_assessment_report_data(assessment.id, 'detailed')
            
            print(f"\n{self.app.CLIColors.BOLD}Assessment Report: {assessment.title}{self.app.CLIColors.ENDC}")
            print("-" * 60)
            
            print(f"Assessment Type: {assessment.assessment_type.replace('_', ' ').title()}")
            print(f"Batch: {assessment.batch.name if assessment.batch else 'No Batch'}")
            print(f"Created: {assessment.created_at.strftime('%Y-%m-%d') if assessment.created_at else 'Unknown'}")
            print(f"Status: {'Active' if assessment.is_active else 'Inactive'}")
            
            statistics = report_data.get('statistics', {})
            print(f"\nStatistics:")
            print(f"  Total Evaluations: {statistics.get('total_evaluations', 0)}")
            print(f"  Participation Rate: {statistics.get('participation_rate', 0):.1f}%")
            
            avg_scores = statistics.get('average_scores', {})
            if avg_scores.get('overall', 0) > 0:
                print(f"  Average Scores:")
                print(f"    Overall: {avg_scores.get('overall', 0):.1f}")
                print(f"    Leadership: {avg_scores.get('leadership', 0):.1f}")
                print(f"    Teamwork: {avg_scores.get('teamwork', 0):.1f}")
                print(f"    Communication: {avg_scores.get('communication', 0):.1f}")
                print(f"    Technical: {avg_scores.get('technical', 0):.1f}")
                print(f"    Professionalism: {avg_scores.get('professionalism', 0):.1f}")
            
            # Export option
            if self.app.confirm_action("\nWould you like to export this report to CSV?"):
                self.export_assessment_report(assessment.id, assessment.title)
            
        except Exception as e:
            self.app.print_error(f"Failed to show assessment reports: {e}")
        
        self.app.wait_for_enter()
    
    def show_student_reports(self):
        """Show student reports."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}👥 STUDENT REPORTS{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # Get all students
            students = self.user_service.get_users_by_role('student')
            
            if not students:
                self.app.print_info("No students found.")
                self.app.wait_for_enter()
                return
            
            # Select student
            student_options = [f"{s.full_name} ({s.username})" for s in students]
            student_choice = self.app.get_choice("Select Student:", student_options)
            if student_choice == -1:
                return
            
            student = students[student_choice]
            
            # Generate report
            report_data = self.reporting_service.generate_student_report_data(student.id, 'individual')
            
            print(f"\n{self.app.CLIColors.BOLD}Student Report: {student.full_name}{self.app.CLIColors.ENDC}")
            print("-" * 60)
            
            print(f"Student ID: {student.student_id if hasattr(student, 'student_id') else 'N/A'}")
            print(f"Username: {student.username}")
            print(f"Email: {student.email or 'Not set'}")
            
            statistics = report_data.get('statistics', {})
            print(f"\nParticipation Statistics:")
            print(f"  Evaluations Received: {statistics.get('total_evaluations_received', 0)}")
            print(f"  Evaluations Given: {statistics.get('total_evaluations_given', 0)}")
            print(f"  Assessments Participated: {statistics.get('assessments_participated', 0)}")
            print(f"  Participation Rate: {statistics.get('participation_rate', 0):.1f}%")
            
            avg_scores = report_data.get('average_scores', {})
            if avg_scores.get('overall', 0) > 0:
                print(f"\nAverage Performance Scores:")
                print(f"  Overall: {avg_scores.get('overall', 0):.1f}")
                print(f"  Leadership: {avg_scores.get('leadership', 0):.1f}")
                print(f"  Teamwork: {avg_scores.get('teamwork', 0):.1f}")
                print(f"  Communication: {avg_scores.get('communication', 0):.1f}")
                print(f"  Technical: {avg_scores.get('technical', 0):.1f}")
                print(f"  Professionalism: {avg_scores.get('professionalism', 0):.1f}")
            
            # Performance trend
            performance_trend = report_data.get('performance_trend', [])
            if performance_trend:
                print(f"\nPerformance Trend:")
                for trend_item in performance_trend[-5:]:  # Last 5 assessments
                    print(f"  {trend_item['assessment_title']}: {trend_item['score']:.1f}")
            
            # Export option
            if self.app.confirm_action("\nWould you like to export this report to CSV?"):
                self.export_student_report(student.id, student.full_name)
            
        except Exception as e:
            self.app.print_error(f"Failed to show student reports: {e}")
        
        self.app.wait_for_enter()
    
    def show_batch_reports(self):
        """Show batch reports."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}📚 BATCH REPORTS{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # Get all batches
            batches = self.batch_service.get_all_batches()
            
            if not batches:
                self.app.print_info("No batches found.")
                self.app.wait_for_enter()
                return
            
            # Select batch
            batch_options = [f"{b.name} ({b.code}) - {b.academic_year}" for b in batches]
            batch_choice = self.app.get_choice("Select Batch:", batch_options)
            if batch_choice == -1:
                return
            
            batch = batches[batch_choice]
            
            # Generate report
            report_data = self.reporting_service.generate_batch_report_data(batch.id, 'overview')
            
            print(f"\n{self.app.CLIColors.BOLD}Batch Report: {batch.name}{self.app.CLIColors.ENDC}")
            print("-" * 60)
            
            print(f"Batch Code: {batch.code}")
            print(f"Academic Year: {batch.academic_year}")
            print(f"Semester: {batch.semester}")
            print(f"Instructor: {batch.instructor.full_name if batch.instructor else 'Not assigned'}")
            
            statistics = report_data.get('statistics', {})
            print(f"\nBatch Statistics:")
            print(f"  Total Students: {statistics.get('total_students', 0)}")
            print(f"  Total Assessments: {statistics.get('total_assessments', 0)}")
            print(f"  Total Evaluations: {statistics.get('total_evaluations', 0)}")
            print(f"  Completion Rate: {statistics.get('completion_rate', 0):.1f}%")
            
            avg_scores = report_data.get('batch_average_scores', {})
            if avg_scores.get('overall', 0) > 0:
                print(f"\nBatch Average Scores:")
                print(f"  Overall: {avg_scores.get('overall', 0):.1f}")
                print(f"  Leadership: {avg_scores.get('leadership', 0):.1f}")
                print(f"  Teamwork: {avg_scores.get('teamwork', 0):.1f}")
                print(f"  Communication: {avg_scores.get('communication', 0):.1f}")
                print(f"  Technical: {avg_scores.get('technical', 0):.1f}")
                print(f"  Professionalism: {avg_scores.get('professionalism', 0):.1f}")
            
            # Top performers
            student_performance = report_data.get('student_performance', [])
            if student_performance:
                top_performers = sorted(student_performance, key=lambda x: x['average_score'], reverse=True)[:5]
                print(f"\nTop 5 Performers:")
                for i, performer in enumerate(top_performers, 1):
                    student_name = performer['student'].full_name
                    avg_score = performer['average_score']
                    print(f"  {i}. {student_name}: {avg_score:.1f}")
            
            # Export option
            if self.app.confirm_action("\nWould you like to export this report to CSV?"):
                self.export_batch_report(batch.id, batch.name)
            
        except Exception as e:
            self.app.print_error(f"Failed to show batch reports: {e}")
        
        self.app.wait_for_enter()
    
    def show_system_statistics(self):
        """Show system statistics."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}📊 SYSTEM STATISTICS{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # User statistics
            user_stats = self.user_service.get_user_statistics()
            print(f"User Statistics:")
            print(f"  Total Users: {user_stats.get('total_users', 0)}")
            print(f"  Active Users: {user_stats.get('active_users', 0)}")
            print(f"  Inactive Users: {user_stats.get('inactive_users', 0)}")
            
            role_counts = user_stats.get('role_counts', {})
            if role_counts:
                print(f"  Users by Role:")
                for role, count in role_counts.items():
                    print(f"    {role.replace('_', ' ').title()}: {count}")
            
            # Batch statistics
            batch_stats = self.batch_service.get_batch_statistics()
            print(f"\nBatch Statistics:")
            print(f"  Total Batches: {batch_stats.get('total_batches', 0)}")
            print(f"  Active Batches: {batch_stats.get('active_batches', 0)}")
            print(f"  Total Students: {batch_stats.get('total_students', 0)}")
            print(f"  Average Students per Batch: {batch_stats.get('avg_students_per_batch', 0):.1f}")
            
            # Assessment statistics
            from services.reporting_service import ReportingService
            reporting_service = ReportingService()
            assessment_stats = reporting_service.get_assessment_statistics()
            print(f"\nAssessment Statistics:")
            print(f"  Total Assessments: {assessment_stats.get('total_assessments', 0)}")
            print(f"  Active Assessments: {assessment_stats.get('active_assessments', 0)}")
            print(f"  Pending Assessments: {assessment_stats.get('pending_assessments', 0)}")
            print(f"  Completed Assessments: {assessment_stats.get('completed_assessments', 0)}")
            
        except Exception as e:
            self.app.print_error(f"Failed to show system statistics: {e}")
        
        self.app.wait_for_enter()
    
    def show_my_performance(self):
        """Show student's own performance."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}📊 MY PERFORMANCE{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # Generate student report for current user
            report_data = self.reporting_service.generate_student_report_data(self.app.current_user.id, 'individual')
            
            statistics = report_data.get('statistics', {})
            print(f"My Participation:")
            print(f"  Evaluations Received: {statistics.get('total_evaluations_received', 0)}")
            print(f"  Evaluations Given: {statistics.get('total_evaluations_given', 0)}")
            print(f"  Assessments Participated: {statistics.get('assessments_participated', 0)}")
            print(f"  Participation Rate: {statistics.get('participation_rate', 0):.1f}%")
            
            avg_scores = report_data.get('average_scores', {})
            if avg_scores.get('overall', 0) > 0:
                print(f"\nMy Average Scores:")
                print(f"  Overall: {avg_scores.get('overall', 0):.1f}")
                print(f"  Leadership: {avg_scores.get('leadership', 0):.1f}")
                print(f"  Teamwork: {avg_scores.get('teamwork', 0):.1f}")
                print(f"  Communication: {avg_scores.get('communication', 0):.1f}")
                print(f"  Technical: {avg_scores.get('technical', 0):.1f}")
                print(f"  Professionalism: {avg_scores.get('professionalism', 0):.1f}")
            else:
                self.app.print_info("No evaluation scores available yet.")
            
        except Exception as e:
            self.app.print_error(f"Failed to show performance: {e}")
        
        self.app.wait_for_enter()
    
    def show_my_progress(self):
        """Show student's progress over time."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}📈 MY PROGRESS{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # Generate student report for current user
            report_data = self.reporting_service.generate_student_report_data(self.app.current_user.id, 'individual')
            
            performance_trend = report_data.get('performance_trend', [])
            if performance_trend:
                print(f"Performance Trend:")
                headers = ["Assessment", "Date", "Score"]
                widths = [30, 12, 8]
                
                self.app.print_table_header(headers, widths)
                
                for trend_item in performance_trend:
                    date_str = trend_item['date'].strftime('%Y-%m-%d') if trend_item['date'] else 'Unknown'
                    values = [
                        trend_item['assessment_title'],
                        date_str,
                        f"{trend_item['score']:.1f}"
                    ]
                    self.app.print_table_row(values, widths)
            else:
                self.app.print_info("No progress data available yet.")
            
        except Exception as e:
            self.app.print_error(f"Failed to show progress: {e}")
        
        self.app.wait_for_enter()
    
    def export_data(self):
        """Export data to files."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}💾 EXPORT DATA{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            export_options = [
                "📝 Export All Assessments",
                "👥 Export All Students",
                "📚 Export All Batches",
                "📊 Export All Evaluations",
                "🔙 Back"
            ]
            
            choice = self.app.get_choice("Select export option:", export_options)
            
            if choice == -1 or choice == 4:  # Back or cancelled
                return
            elif choice == 0:  # Export Assessments
                self.export_all_assessments()
            elif choice == 1:  # Export Students
                self.export_all_students()
            elif choice == 2:  # Export Batches
                self.export_all_batches()
            elif choice == 3:  # Export Evaluations
                self.export_all_evaluations()
            
        except Exception as e:
            self.app.print_error(f"Failed to export data: {e}")
        
        self.app.wait_for_enter()
    
    def export_assessment_report(self, assessment_id: int, assessment_title: str):
        """Export assessment report to CSV."""
        try:
            # Create exports directory
            os.makedirs('exports', exist_ok=True)
            
            # Get assessment results
            results_data = self.assessment_service.get_assessment_results_summary(assessment_id)
            student_results = results_data.get('student_results', [])
            
            if not student_results:
                self.app.print_info("No data to export.")
                return
            
            # Generate filename
            safe_title = "".join(c for c in assessment_title if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"exports/assessment_report_{safe_title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            # Export to CSV
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['student_name', 'student_number', 'overall_score', 'leadership_score', 
                             'teamwork_score', 'communication_score', 'technical_score', 
                             'professionalism_score', 'evaluation_count']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for result in student_results:
                    writer.writerow({
                        'student_name': result.get('student_name', ''),
                        'student_number': result.get('student_number', ''),
                        'overall_score': result.get('overall_score', 0),
                        'leadership_score': result.get('leadership_score', 0),
                        'teamwork_score': result.get('teamwork_score', 0),
                        'communication_score': result.get('communication_score', 0),
                        'technical_score': result.get('technical_score', 0),
                        'professionalism_score': result.get('professionalism_score', 0),
                        'evaluation_count': result.get('evaluation_count', 0)
                    })
            
            self.app.print_success(f"Assessment report exported to: {filename}")
            
        except Exception as e:
            self.app.print_error(f"Failed to export assessment report: {e}")
    
    def export_student_report(self, student_id: int, student_name: str):
        """Export student report to CSV."""
        try:
            # Create exports directory
            os.makedirs('exports', exist_ok=True)
            
            # Get student evaluations
            from models.assessment import PeerEvaluation
            from database.connection import db_connection
            
            def get_student_evaluations(session):
                return session.query(PeerEvaluation).filter(
                    PeerEvaluation.evaluated_id == student_id
                ).all()
            
            evaluations = db_connection.execute_transaction(get_student_evaluations)
            
            if not evaluations:
                self.app.print_info("No data to export.")
                return
            
            # Generate filename
            safe_name = "".join(c for c in student_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"exports/student_report_{safe_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            # Export to CSV
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['assessment_title', 'overall_score', 'leadership_score', 'teamwork_score',
                             'communication_score', 'technical_score', 'professionalism_score',
                             'comments', 'strengths', 'areas_for_improvement', 'submitted_date']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for evaluation in evaluations:
                    writer.writerow({
                        'assessment_title': evaluation.assessment.title if evaluation.assessment else 'Unknown',
                        'overall_score': evaluation.overall_score,
                        'leadership_score': evaluation.leadership_score,
                        'teamwork_score': evaluation.teamwork_score,
                        'communication_score': evaluation.communication_score,
                        'technical_score': evaluation.technical_score,
                        'professionalism_score': evaluation.professionalism_score,
                        'comments': evaluation.comments or '',
                        'strengths': evaluation.strengths or '',
                        'areas_for_improvement': evaluation.areas_for_improvement or '',
                        'submitted_date': evaluation.submitted_at.strftime('%Y-%m-%d %H:%M:%S') if evaluation.submitted_at else ''
                    })
            
            self.app.print_success(f"Student report exported to: {filename}")
            
        except Exception as e:
            self.app.print_error(f"Failed to export student report: {e}")
    
    def export_batch_report(self, batch_id: int, batch_name: str):
        """Export batch report to CSV."""
        try:
            # Create exports directory
            os.makedirs('exports', exist_ok=True)
            
            # Get batch students
            students = self.batch_service.get_batch_students(batch_id)
            
            if not students:
                self.app.print_info("No data to export.")
                return
            
            # Generate filename
            safe_name = "".join(c for c in batch_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"exports/batch_report_{safe_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            
            # Export to CSV
            with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = ['student_id', 'full_name', 'email', 'phone', 'rank', 'unit', 'branch', 'status']
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                
                writer.writeheader()
                for student in students:
                    writer.writerow({
                        'student_id': student.student_id or '',
                        'full_name': student.full_name,
                        'email': student.email or '',
                        'phone': student.phone or '',
                        'rank': student.rank or '',
                        'unit': student.unit or '',
                        'branch': student.branch or '',
                        'status': 'Active' if student.is_active else 'Inactive'
                    })
            
            self.app.print_success(f"Batch report exported to: {filename}")
            
        except Exception as e:
            self.app.print_error(f"Failed to export batch report: {e}")
    
    def export_all_assessments(self):
        """Export all assessments to CSV."""
        self.app.print_info("Export all assessments functionality will be implemented.")
    
    def export_all_students(self):
        """Export all students to CSV."""
        self.app.print_info("Export all students functionality will be implemented.")
    
    def export_all_batches(self):
        """Export all batches to CSV."""
        self.app.print_info("Export all batches functionality will be implemented.")
    
    def export_all_evaluations(self):
        """Export all evaluations to CSV."""
        self.app.print_info("Export all evaluations functionality will be implemented.")
