2025-05-29 19:02:41,811 - __main__ - ERROR - System initialization error: 'DatabaseManager' object has no attribute 'initialize_database'
2025-05-29 19:03:18,032 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 19:03:18,035 - config.database - INFO - Database migrations completed successfully
2025-05-29 19:03:18,038 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 19:03:18,053 - config.database - INFO - Database migrations completed successfully
2025-05-29 19:03:18,113 - database.migrations - INFO - Super admin user already exists
2025-05-29 19:03:18,126 - database.migrations - INFO - Sample users already exist
2025-05-29 19:03:18,126 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 19:03:18,127 - database.migrations - INFO - Super admin user already exists
2025-05-29 19:03:18,128 - database.migrations - INFO - Sample users already exist
2025-05-29 19:03:18,129 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 19:03:18,129 - database.migrations - INFO - Super admin user already exists
2025-05-29 19:03:18,130 - database.migrations - INFO - Sample users already exist
2025-05-29 19:03:18,132 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 19:03:18,133 - root - INFO - Database initialized successfully
2025-05-29 19:05:06,277 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 19:05:06,280 - config.database - INFO - Database migrations completed successfully
2025-05-29 19:05:06,283 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 19:05:06,286 - config.database - INFO - Database migrations completed successfully
2025-05-29 19:05:06,455 - database.migrations - INFO - Super admin user already exists
2025-05-29 19:05:06,479 - database.migrations - INFO - Sample users already exist
2025-05-29 19:05:06,480 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 19:05:06,483 - database.migrations - INFO - Super admin user already exists
2025-05-29 19:05:06,484 - database.migrations - INFO - Sample users already exist
2025-05-29 19:05:06,484 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 19:05:06,485 - database.migrations - INFO - Super admin user already exists
2025-05-29 19:05:06,492 - database.migrations - INFO - Sample users already exist
2025-05-29 19:05:06,493 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 19:05:06,493 - root - INFO - Database initialized successfully
2025-05-29 19:13:15,784 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 19:13:15,786 - config.database - INFO - Database migrations completed successfully
2025-05-29 19:13:15,788 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 19:13:15,790 - config.database - INFO - Database migrations completed successfully
2025-05-29 19:13:15,862 - database.migrations - INFO - Super admin user already exists
2025-05-29 19:13:15,873 - database.migrations - INFO - Sample users already exist
2025-05-29 19:13:15,874 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 19:13:15,875 - database.migrations - INFO - Super admin user already exists
2025-05-29 19:13:15,876 - database.migrations - INFO - Sample users already exist
2025-05-29 19:13:15,876 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 19:13:15,877 - database.migrations - INFO - Super admin user already exists
2025-05-29 19:13:15,878 - database.migrations - INFO - Sample users already exist
2025-05-29 19:13:15,878 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 19:13:15,879 - root - INFO - Database initialized successfully
2025-05-29 19:14:02,489 - services.user_service - INFO - Authentication successful for user: admin
2025-05-29 19:15:11,742 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 19:15:11,744 - config.database - INFO - Database migrations completed successfully
2025-05-29 19:15:11,747 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 19:15:11,752 - config.database - INFO - Database migrations completed successfully
2025-05-29 19:15:11,823 - database.migrations - INFO - Super admin user already exists
2025-05-29 19:15:11,835 - database.migrations - INFO - Sample users already exist
2025-05-29 19:15:11,835 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 19:15:11,837 - database.migrations - INFO - Super admin user already exists
2025-05-29 19:15:11,838 - database.migrations - INFO - Sample users already exist
2025-05-29 19:15:11,839 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 19:15:11,840 - database.migrations - INFO - Super admin user already exists
2025-05-29 19:15:11,842 - database.migrations - INFO - Sample users already exist
2025-05-29 19:15:11,842 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 19:15:11,842 - root - INFO - Database initialized successfully
2025-05-29 19:15:58,808 - services.user_service - INFO - Authentication successful for user: admin
2025-05-29 19:16:15,308 - __main__ - ERROR - Main menu error: 'MilitaryPeerReviewCLI' object has no attribute 'CLIColors'
2025-05-29 19:16:25,592 - __main__ - ERROR - Main menu error: 'MilitaryPeerReviewCLI' object has no attribute 'CLIColors'
2025-05-29 19:16:36,006 - __main__ - ERROR - Main menu error: 'MilitaryPeerReviewCLI' object has no attribute 'CLIColors'
2025-05-29 19:16:39,823 - __main__ - ERROR - Main menu error: 'MilitaryPeerReviewCLI' object has no attribute 'CLIColors'
