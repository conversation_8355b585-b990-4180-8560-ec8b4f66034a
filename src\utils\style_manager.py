"""
Style Manager

This module handles application styling and theme management.
"""

import logging
from pathlib import Path
from typing import Optional
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QFile, QTextStream


class StyleManager:
    """Manages application styles and themes."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.current_theme = "main"
        self.styles_dir = Path("resources/styles")
    
    def load_stylesheet(self, theme_name: str = "main") -> Optional[str]:
        """Load stylesheet from file."""
        try:
            stylesheet_path = self.styles_dir / f"{theme_name}.qss"
            
            if not stylesheet_path.exists():
                self.logger.warning(f"Stylesheet not found: {stylesheet_path}")
                return None
            
            with open(stylesheet_path, 'r', encoding='utf-8') as f:
                stylesheet = f.read()
            
            self.logger.info(f"Loaded stylesheet: {theme_name}")
            return stylesheet
            
        except Exception as e:
            self.logger.error(f"Failed to load stylesheet {theme_name}: {e}")
            return None
    
    def apply_theme(self, app: QApplication, theme_name: str = "main") -> bool:
        """Apply theme to the application."""
        try:
            stylesheet = self.load_stylesheet(theme_name)
            
            if stylesheet:
                app.setStyleSheet(stylesheet)
                self.current_theme = theme_name
                self.logger.info(f"Applied theme: {theme_name}")
                return True
            else:
                # Fallback to default styling
                self._apply_default_styling(app)
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to apply theme {theme_name}: {e}")
            self._apply_default_styling(app)
            return False
    
    def _apply_default_styling(self, app: QApplication):
        """Apply basic default styling if stylesheet loading fails."""
        default_style = """
        QApplication {
            font-family: "Segoe UI", "Arial", sans-serif;
            font-size: 14px;
        }
        
        QMainWindow {
            background-color: #f8f9fa;
        }
        
        QPushButton {
            background-color: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 8px 16px;
            font-size: 14px;
            min-height: 20px;
        }
        
        QPushButton:hover {
            background-color: #f3f4f6;
            border-color: #9ca3af;
        }
        
        QLineEdit {
            background-color: #ffffff;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            padding: 10px 12px;
            font-size: 14px;
            min-height: 20px;
        }
        
        QLineEdit:focus {
            border-color: #3b82f6;
        }
        """
        
        app.setStyleSheet(default_style)
        self.logger.info("Applied default styling")
    
    def get_color_scheme(self) -> dict:
        """Get the current color scheme."""
        return {
            "primary": "#3b82f6",
            "primary_hover": "#2563eb",
            "secondary": "#6b7280",
            "success": "#10b981",
            "warning": "#f59e0b",
            "error": "#ef4444",
            "background": "#f8f9fa",
            "surface": "#ffffff",
            "text_primary": "#1f2937",
            "text_secondary": "#6b7280",
            "border": "#e5e7eb",
            "border_focus": "#3b82f6",
        }
    
    def create_button_style(self, variant: str = "default") -> str:
        """Create button style for specific variant."""
        colors = self.get_color_scheme()
        
        if variant == "primary":
            return f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {colors['primary']}, stop:1 {colors['primary_hover']});
                    border: 1px solid {colors['primary_hover']};
                    border-radius: 8px;
                    color: white;
                    font-weight: 600;
                    padding: 12px 20px;
                    min-height: 20px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {colors['primary_hover']}, stop:1 #1d4ed8);
                }}
                QPushButton:pressed {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #1d4ed8, stop:1 #1e40af);
                }}
            """
        elif variant == "success":
            return f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {colors['success']}, stop:1 #059669);
                    border: 1px solid #059669;
                    border-radius: 8px;
                    color: white;
                    font-weight: 600;
                    padding: 12px 20px;
                    min-height: 20px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #059669, stop:1 #047857);
                }}
            """
        elif variant == "warning":
            return f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {colors['warning']}, stop:1 #d97706);
                    border: 1px solid #d97706;
                    border-radius: 8px;
                    color: white;
                    font-weight: 600;
                    padding: 12px 20px;
                    min-height: 20px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #d97706, stop:1 #b45309);
                }}
            """
        elif variant == "danger":
            return f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {colors['error']}, stop:1 #dc2626);
                    border: 1px solid #dc2626;
                    border-radius: 8px;
                    color: white;
                    font-weight: 600;
                    padding: 12px 20px;
                    min-height: 20px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #dc2626, stop:1 #b91c1c);
                }}
            """
        else:  # default
            return f"""
                QPushButton {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 {colors['surface']}, stop:1 #f9fafb);
                    border: 1px solid {colors['border']};
                    border-radius: 8px;
                    color: {colors['text_primary']};
                    font-weight: 600;
                    padding: 12px 20px;
                    min-height: 20px;
                }}
                QPushButton:hover {{
                    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                        stop:0 #f9fafb, stop:1 #f3f4f6);
                    border-color: #9ca3af;
                }}
            """
    
    def create_card_style(self, elevated: bool = True) -> str:
        """Create card style for containers."""
        colors = self.get_color_scheme()
        
        shadow = "box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);" if elevated else ""
        
        return f"""
            QFrame {{
                background-color: {colors['surface']};
                border: 1px solid {colors['border']};
                border-radius: 12px;
                padding: 20px;
                {shadow}
            }}
            QFrame:hover {{
                border-color: #d1d5db;
            }}
        """


# Global style manager instance
style_manager = StyleManager()
