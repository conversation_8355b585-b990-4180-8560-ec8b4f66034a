"""
Student Assessments CLI Module

Provides command-line interface for students to participate in assessments.
"""

from typing import List, Optional
from datetime import datetime, date
from core.exceptions import ValidationError, DatabaseError


class StudentAssessmentsCLI:
    """CLI for student assessment participation."""
    
    def __init__(self, main_app):
        self.app = main_app
        self.assessment_service = main_app.assessment_service
        self.batch_service = main_app.batch_service
    
    def show_menu(self):
        """Show student assessments menu."""
        while True:
            self.app.clear_screen()
            print(f"\n{self.app.CLIColors.BOLD}📝 MY ASSESSMENTS{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            menu_options = [
                "📋 Available Assessments",
                "✍️ Submit Peer Evaluation",
                "📊 My Evaluation History",
                "🔙 Back to Main Menu"
            ]
            
            choice = self.app.get_choice("Select an option:", menu_options)
            
            if choice == -1 or choice == 3:  # Back or cancelled
                break
            elif choice == 0:  # Available Assessments
                self.list_available_assessments()
            elif choice == 1:  # Submit Evaluation
                self.submit_peer_evaluation()
            elif choice == 2:  # Evaluation History
                self.view_evaluation_history()
    
    def list_available_assessments(self):
        """List available assessments for the student."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}📋 AVAILABLE ASSESSMENTS{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # Get student's batch
            student_batch = self.get_student_batch()
            if not student_batch:
                self.app.print_error("You are not enrolled in any batch.")
                self.app.wait_for_enter()
                return
            
            # Get assessments for the student's batch
            assessments = self.assessment_service.get_assessments_by_batch(student_batch.id)
            active_assessments = [a for a in assessments if a.is_active]
            
            if not active_assessments:
                self.app.print_info("No active assessments available.")
                self.app.wait_for_enter()
                return
            
            print(f"Batch: {student_batch.name} ({student_batch.code})")
            print("-" * 40)
            
            # Display assessments
            headers = ["ID", "Title", "Type", "Start Date", "End Date", "Status"]
            widths = [5, 30, 18, 12, 12, 10]
            
            self.app.print_table_header(headers, widths)
            
            for assessment in active_assessments:
                start_date = assessment.start_date.strftime('%Y-%m-%d') if assessment.start_date else "Not set"
                end_date = assessment.end_date.strftime('%Y-%m-%d') if assessment.end_date else "Not set"
                
                # Determine status
                status = "Available"
                if assessment.start_date and assessment.start_date > date.today():
                    status = "Upcoming"
                elif assessment.end_date and assessment.end_date < date.today():
                    status = "Expired"
                
                values = [
                    str(assessment.id),
                    assessment.title,
                    assessment.assessment_type.replace('_', ' ').title(),
                    start_date,
                    end_date,
                    status
                ]
                self.app.print_table_row(values, widths)
            
            print(f"\nTotal available assessments: {len(active_assessments)}")
            
            # Show assessment details option
            if self.app.confirm_action("\nWould you like to view details of an assessment?"):
                self.view_assessment_details()
            
        except Exception as e:
            self.app.print_error(f"Failed to list assessments: {e}")
        
        self.app.wait_for_enter()
    
    def view_assessment_details(self):
        """View details of a specific assessment."""
        try:
            assessment_id = self.app.get_input("Enter Assessment ID: ")
            if not assessment_id:
                return
            
            try:
                assessment_id = int(assessment_id)
            except ValueError:
                self.app.print_error("Invalid Assessment ID.")
                return
            
            assessment = self.assessment_service.get_assessment_by_id(assessment_id)
            if not assessment:
                self.app.print_error("Assessment not found.")
                return
            
            print(f"\n{self.app.CLIColors.BOLD}Assessment Details{self.app.CLIColors.ENDC}")
            print("-" * 40)
            print(f"Title: {assessment.title}")
            print(f"Type: {assessment.assessment_type.replace('_', ' ').title()}")
            print(f"Description: {assessment.description or 'No description'}")
            print(f"Maximum Score: {assessment.max_score}")
            print(f"Passing Score: {assessment.passing_score}")
            print(f"Start Date: {assessment.start_date.strftime('%Y-%m-%d') if assessment.start_date else 'Not set'}")
            print(f"End Date: {assessment.end_date.strftime('%Y-%m-%d') if assessment.end_date else 'Not set'}")
            print(f"Anonymous Feedback: {'Yes' if assessment.anonymous_feedback else 'No'}")
            
            if assessment.instructions:
                print(f"\nInstructions:")
                print(f"{assessment.instructions}")
            
        except Exception as e:
            self.app.print_error(f"Failed to view assessment details: {e}")
    
    def submit_peer_evaluation(self):
        """Submit a peer evaluation."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}✍️ SUBMIT PEER EVALUATION{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # Get student's batch
            student_batch = self.get_student_batch()
            if not student_batch:
                self.app.print_error("You are not enrolled in any batch.")
                self.app.wait_for_enter()
                return
            
            # Get available assessments
            assessments = self.assessment_service.get_assessments_by_batch(student_batch.id)
            active_assessments = [a for a in assessments if a.is_active and a.assessment_type == 'peer_evaluation']
            
            if not active_assessments:
                self.app.print_info("No peer evaluation assessments available.")
                self.app.wait_for_enter()
                return
            
            # Select assessment
            assessment_options = [f"{a.title} (ID: {a.id})" for a in active_assessments]
            assessment_choice = self.app.get_choice("Select Assessment:", assessment_options)
            if assessment_choice == -1:
                return
            
            assessment = active_assessments[assessment_choice]
            
            # Get batch students (excluding self)
            batch_students = self.batch_service.get_batch_students(student_batch.id)
            other_students = [s for s in batch_students if s.id != self.app.current_user.id]
            
            if not other_students:
                self.app.print_info("No other students in your batch to evaluate.")
                self.app.wait_for_enter()
                return
            
            # Select student to evaluate
            student_options = [f"{s.full_name} ({s.student_id})" for s in other_students]
            student_choice = self.app.get_choice("Select Student to Evaluate:", student_options)
            if student_choice == -1:
                return
            
            evaluated_student = other_students[student_choice]
            
            print(f"\nEvaluating: {evaluated_student.full_name}")
            print(f"Assessment: {assessment.title}")
            print("-" * 40)
            
            # Check if evaluation already exists
            existing_evaluations = self.assessment_service.get_student_evaluations(assessment.id, evaluated_student.id)
            evaluator_evaluations = [e for e in existing_evaluations if e.evaluator_id == self.app.current_user.id]
            
            if evaluator_evaluations:
                self.app.print_warning("You have already evaluated this student for this assessment.")
                if not self.app.confirm_action("Do you want to update your evaluation?"):
                    return
            
            # Collect evaluation scores
            print(f"\nPlease rate the student on a scale of 1-10 for each criterion:")
            print("(1 = Poor, 5 = Average, 10 = Excellent)")
            print("-" * 40)
            
            scores = {}
            criteria = [
                ('leadership_score', 'Leadership'),
                ('teamwork_score', 'Teamwork'),
                ('communication_score', 'Communication'),
                ('technical_score', 'Technical Skills'),
                ('professionalism_score', 'Professionalism')
            ]
            
            for score_key, criterion_name in criteria:
                while True:
                    try:
                        score_input = self.app.get_input(f"{criterion_name} (1-10): ")
                        if not score_input:
                            return
                        
                        score = int(score_input)
                        if 1 <= score <= 10:
                            scores[score_key] = score
                            break
                        else:
                            self.app.print_error("Please enter a score between 1 and 10.")
                    except ValueError:
                        self.app.print_error("Please enter a valid number.")
            
            # Calculate overall score
            overall_score = sum(scores.values()) / len(scores)
            
            # Collect comments
            print(f"\nOptional Comments:")
            print("-" * 40)
            
            comments = self.app.get_input("General Comments (optional): ", required=False)
            strengths = self.app.get_input("Strengths (optional): ", required=False)
            areas_for_improvement = self.app.get_input("Areas for Improvement (optional): ", required=False)
            
            # Summary
            print(f"\n{self.app.CLIColors.BOLD}Evaluation Summary{self.app.CLIColors.ENDC}")
            print("-" * 40)
            print(f"Student: {evaluated_student.full_name}")
            print(f"Assessment: {assessment.title}")
            print(f"Overall Score: {overall_score:.1f}")
            print(f"Leadership: {scores['leadership_score']}")
            print(f"Teamwork: {scores['teamwork_score']}")
            print(f"Communication: {scores['communication_score']}")
            print(f"Technical Skills: {scores['technical_score']}")
            print(f"Professionalism: {scores['professionalism_score']}")
            
            if comments:
                print(f"Comments: {comments}")
            if strengths:
                print(f"Strengths: {strengths}")
            if areas_for_improvement:
                print(f"Areas for Improvement: {areas_for_improvement}")
            
            if self.app.confirm_action("\nSubmit this evaluation?"):
                # Prepare evaluation data
                evaluation_data = {
                    'assessment_id': assessment.id,
                    'evaluator_id': self.app.current_user.id,
                    'evaluated_id': evaluated_student.id,
                    'overall_score': overall_score,
                    'comments': comments if comments else None,
                    'strengths': strengths if strengths else None,
                    'areas_for_improvement': areas_for_improvement if areas_for_improvement else None,
                    'is_anonymous': assessment.anonymous_feedback
                }
                evaluation_data.update(scores)
                
                # Submit evaluation
                evaluation = self.assessment_service.submit_peer_evaluation(evaluation_data)
                self.app.print_success("Evaluation submitted successfully!")
            else:
                self.app.print_info("Evaluation cancelled.")
            
        except ValidationError as e:
            self.app.print_error(f"Validation error: {e}")
        except Exception as e:
            self.app.print_error(f"Failed to submit evaluation: {e}")
        
        self.app.wait_for_enter()
    
    def view_evaluation_history(self):
        """View evaluation history."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}📊 MY EVALUATION HISTORY{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # Get evaluations given by the current user
            from models.assessment import PeerEvaluation
            from database.connection import db_connection
            
            def get_evaluations_given(session):
                return session.query(PeerEvaluation).filter(
                    PeerEvaluation.evaluator_id == self.app.current_user.id
                ).order_by(PeerEvaluation.submitted_at.desc()).all()
            
            def get_evaluations_received(session):
                return session.query(PeerEvaluation).filter(
                    PeerEvaluation.evaluated_id == self.app.current_user.id
                ).order_by(PeerEvaluation.submitted_at.desc()).all()
            
            evaluations_given = db_connection.execute_transaction(get_evaluations_given)
            evaluations_received = db_connection.execute_transaction(get_evaluations_received)
            
            print(f"Evaluations Given: {len(evaluations_given)}")
            print(f"Evaluations Received: {len(evaluations_received)}")
            print("-" * 40)
            
            # Show evaluations given
            if evaluations_given:
                print(f"\n{self.app.CLIColors.BOLD}Evaluations You Have Given:{self.app.CLIColors.ENDC}")
                headers = ["Assessment", "Student Evaluated", "Overall Score", "Date"]
                widths = [25, 25, 12, 12]
                
                self.app.print_table_header(headers, widths)
                
                for evaluation in evaluations_given:
                    values = [
                        evaluation.assessment.title if evaluation.assessment else "Unknown",
                        evaluation.evaluated.full_name if evaluation.evaluated else "Unknown",
                        f"{evaluation.overall_score:.1f}",
                        evaluation.submitted_at.strftime('%Y-%m-%d') if evaluation.submitted_at else "Unknown"
                    ]
                    self.app.print_table_row(values, widths)
            
            # Show evaluations received
            if evaluations_received:
                print(f"\n{self.app.CLIColors.BOLD}Evaluations You Have Received:{self.app.CLIColors.ENDC}")
                headers = ["Assessment", "Overall Score", "Leadership", "Teamwork", "Communication", "Technical", "Professional"]
                widths = [25, 12, 10, 8, 12, 9, 12]
                
                self.app.print_table_header(headers, widths)
                
                for evaluation in evaluations_received:
                    values = [
                        evaluation.assessment.title if evaluation.assessment else "Unknown",
                        f"{evaluation.overall_score:.1f}",
                        f"{evaluation.leadership_score}",
                        f"{evaluation.teamwork_score}",
                        f"{evaluation.communication_score}",
                        f"{evaluation.technical_score}",
                        f"{evaluation.professionalism_score}"
                    ]
                    self.app.print_table_row(values, widths)
                
                # Calculate averages
                if evaluations_received:
                    avg_overall = sum(e.overall_score for e in evaluations_received) / len(evaluations_received)
                    avg_leadership = sum(e.leadership_score for e in evaluations_received) / len(evaluations_received)
                    avg_teamwork = sum(e.teamwork_score for e in evaluations_received) / len(evaluations_received)
                    avg_communication = sum(e.communication_score for e in evaluations_received) / len(evaluations_received)
                    avg_technical = sum(e.technical_score for e in evaluations_received) / len(evaluations_received)
                    avg_professionalism = sum(e.professionalism_score for e in evaluations_received) / len(evaluations_received)
                    
                    print(f"\n{self.app.CLIColors.BOLD}Your Average Scores:{self.app.CLIColors.ENDC}")
                    print(f"Overall: {avg_overall:.1f}")
                    print(f"Leadership: {avg_leadership:.1f}")
                    print(f"Teamwork: {avg_teamwork:.1f}")
                    print(f"Communication: {avg_communication:.1f}")
                    print(f"Technical Skills: {avg_technical:.1f}")
                    print(f"Professionalism: {avg_professionalism:.1f}")
            
            if not evaluations_given and not evaluations_received:
                self.app.print_info("No evaluation history found.")
            
        except Exception as e:
            self.app.print_error(f"Failed to view evaluation history: {e}")
        
        self.app.wait_for_enter()
    
    def get_student_batch(self):
        """Get the current student's batch."""
        try:
            from models.batch import Student
            from database.connection import db_connection
            
            def get_student(session):
                return session.query(Student).filter(Student.id == self.app.current_user.id).first()
            
            student = db_connection.execute_transaction(get_student)
            return student.batch if student else None
            
        except Exception as e:
            self.app.print_error(f"Failed to get student batch: {e}")
            return None
