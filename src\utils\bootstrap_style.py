"""
Bootstrap-inspired Styling System for Qt Applications

This module provides Bootstrap-like styling components and utilities
for creating modern, responsive desktop applications with PySide6.
"""

import logging
from typing import Dict, List, Optional, Tuple
from PySide6.QtWidgets import QWidget, QLayout, QH<PERSON><PERSON>Layout, Q<PERSON><PERSON>Layout, QGridLayout, QSizePolicy
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QFont, QPalette


class BootstrapColors:
    """Bootstrap color palette constants."""
    
    # Primary colors
    PRIMARY = "#0d6efd"
    SECONDARY = "#6c757d"
    SUCCESS = "#198754"
    DANGER = "#dc3545"
    WARNING = "#ffc107"
    INFO = "#0dcaf0"
    LIGHT = "#f8f9fa"
    DARK = "#212529"
    
    # Neutral colors
    WHITE = "#ffffff"
    GRAY_100 = "#f8f9fa"
    GRAY_200 = "#e9ecef"
    GRAY_300 = "#dee2e6"
    GRAY_400 = "#ced4da"
    GRAY_500 = "#adb5bd"
    GRAY_600 = "#6c757d"
    GRAY_700 = "#495057"
    GRAY_800 = "#343a40"
    GRAY_900 = "#212529"
    
    # Text colors
    TEXT_PRIMARY = "#212529"
    TEXT_SECONDARY = "#6c757d"
    TEXT_MUTED = "#6c757d"
    
    # Border colors
    BORDER_COLOR = "#dee2e6"
    BORDER_LIGHT = "#e9ecef"


class BootstrapSpacing:
    """Bootstrap spacing system (rem units converted to pixels)."""
    
    # Base spacing unit (1rem = 16px)
    BASE = 16
    
    # Spacing scale
    XS = 4    # 0.25rem
    SM = 8    # 0.5rem
    MD = 16   # 1rem
    LG = 24   # 1.5rem
    XL = 48   # 3rem
    XXL = 72  # 4.5rem


class BootstrapBreakpoints:
    """Bootstrap responsive breakpoints."""
    
    XS = 0      # Extra small devices
    SM = 576    # Small devices
    MD = 768    # Medium devices
    LG = 992    # Large devices
    XL = 1200   # Extra large devices
    XXL = 1400  # Extra extra large devices


class BootstrapStyleManager:
    """Manages Bootstrap-inspired styling for Qt widgets."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.colors = BootstrapColors()
        self.spacing = BootstrapSpacing()
        self.breakpoints = BootstrapBreakpoints()
    
    def get_button_style(self, variant: str = "primary", size: str = "md", outline: bool = False) -> str:
        """Generate Bootstrap-style button CSS."""
        
        # Color mappings
        color_map = {
            "primary": (self.colors.PRIMARY, self.colors.WHITE),
            "secondary": (self.colors.SECONDARY, self.colors.WHITE),
            "success": (self.colors.SUCCESS, self.colors.WHITE),
            "danger": (self.colors.DANGER, self.colors.WHITE),
            "warning": (self.colors.WARNING, self.colors.DARK),
            "info": (self.colors.INFO, self.colors.DARK),
            "light": (self.colors.LIGHT, self.colors.DARK),
            "dark": (self.colors.DARK, self.colors.WHITE),
        }
        
        # Size mappings (padding, font-size, border-radius)
        size_map = {
            "sm": ("6px 12px", "14px", "4px"),
            "md": ("8px 16px", "16px", "6px"),
            "lg": ("12px 24px", "18px", "8px"),
        }
        
        bg_color, text_color = color_map.get(variant, color_map["primary"])
        padding, font_size, border_radius = size_map.get(size, size_map["md"])
        
        if outline:
            return f"""
                QPushButton {{
                    background-color: transparent;
                    border: 2px solid {bg_color};
                    border-radius: {border_radius};
                    color: {bg_color};
                    font-size: {font_size};
                    font-weight: 600;
                    padding: {padding};
                    min-height: 20px;
                }}
                QPushButton:hover {{
                    background-color: {bg_color};
                    color: {text_color};
                }}
                QPushButton:pressed {{
                    background-color: {self._darken_color(bg_color)};
                    border-color: {self._darken_color(bg_color)};
                    color: {text_color};
                }}
                QPushButton:disabled {{
                    background-color: transparent;
                    border-color: {self.colors.GRAY_300};
                    color: {self.colors.GRAY_400};
                }}
            """
        else:
            return f"""
                QPushButton {{
                    background-color: {bg_color};
                    border: 2px solid {bg_color};
                    border-radius: {border_radius};
                    color: {text_color};
                    font-size: {font_size};
                    font-weight: 600;
                    padding: {padding};
                    min-height: 20px;
                }}
                QPushButton:hover {{
                    background-color: {self._darken_color(bg_color)};
                    border-color: {self._darken_color(bg_color)};
                }}
                QPushButton:pressed {{
                    background-color: {self._darken_color(bg_color, 0.2)};
                    border-color: {self._darken_color(bg_color, 0.2)};
                }}
                QPushButton:disabled {{
                    background-color: {self.colors.GRAY_300};
                    border-color: {self.colors.GRAY_300};
                    color: {self.colors.GRAY_500};
                }}
            """
    
    def get_card_style(self, shadow: bool = True, border: bool = True) -> str:
        """Generate Bootstrap-style card CSS."""
        
        shadow_style = """
            border: 1px solid rgba(0, 0, 0, 0.125);
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        """ if shadow else ""
        
        border_style = f"border: 1px solid {self.colors.BORDER_COLOR};" if border else "border: none;"
        
        return f"""
            QFrame {{
                background-color: {self.colors.WHITE};
                {border_style}
                border-radius: 8px;
                padding: {self.spacing.MD}px;
                {shadow_style}
            }}
            QFrame:hover {{
                border-color: {self.colors.GRAY_400};
            }}
        """
    
    def get_form_control_style(self, size: str = "md", state: str = "normal") -> str:
        """Generate Bootstrap-style form control CSS."""
        
        # Size mappings
        size_map = {
            "sm": ("6px 12px", "14px", "4px"),
            "md": ("8px 16px", "16px", "6px"),
            "lg": ("12px 20px", "18px", "8px"),
        }
        
        padding, font_size, border_radius = size_map.get(size, size_map["md"])
        
        # State colors
        border_color = self.colors.BORDER_COLOR
        if state == "focus":
            border_color = self.colors.PRIMARY
        elif state == "error":
            border_color = self.colors.DANGER
        elif state == "success":
            border_color = self.colors.SUCCESS
        
        return f"""
            QLineEdit, QTextEdit, QPlainTextEdit, QComboBox {{
                background-color: {self.colors.WHITE};
                border: 2px solid {self.colors.BORDER_COLOR};
                border-radius: {border_radius};
                color: {self.colors.TEXT_PRIMARY};
                font-size: {font_size};
                padding: {padding};
                min-height: 20px;
            }}
            QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus, QComboBox:focus {{
                border-color: {self.colors.PRIMARY};
                outline: none;
                box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
            }}
            QLineEdit:hover, QTextEdit:hover, QPlainTextEdit:hover, QComboBox:hover {{
                border-color: {self.colors.GRAY_400};
            }}
            QLineEdit:disabled, QTextEdit:disabled, QPlainTextEdit:disabled, QComboBox:disabled {{
                background-color: {self.colors.GRAY_200};
                border-color: {self.colors.GRAY_300};
                color: {self.colors.GRAY_600};
            }}
        """
    
    def get_table_style(self, striped: bool = True, hover: bool = True, bordered: bool = True) -> str:
        """Generate Bootstrap-style table CSS."""
        
        striped_style = f"alternate-background-color: {self.colors.GRAY_100};" if striped else ""
        
        return f"""
            QTableWidget {{
                background-color: {self.colors.WHITE};
                {striped_style}
                gridline-color: {self.colors.BORDER_COLOR};
                border: {'1px solid ' + self.colors.BORDER_COLOR if bordered else 'none'};
                border-radius: 8px;
                font-size: 14px;
                selection-background-color: rgba(13, 110, 253, 0.1);
            }}
            QTableWidget::item {{
                padding: 12px 8px;
                border-bottom: 1px solid {self.colors.BORDER_LIGHT};
            }}
            QTableWidget::item:selected {{
                background-color: rgba(13, 110, 253, 0.1);
                color: {self.colors.PRIMARY};
            }}
            {'QTableWidget::item:hover { background-color: ' + self.colors.GRAY_100 + '; }' if hover else ''}
            QHeaderView::section {{
                background-color: {self.colors.GRAY_100};
                padding: 12px 8px;
                border: none;
                border-bottom: 2px solid {self.colors.BORDER_COLOR};
                font-weight: 600;
                font-size: 14px;
                color: {self.colors.TEXT_PRIMARY};
            }}
        """
    
    def _darken_color(self, color: str, factor: float = 0.1) -> str:
        """Darken a hex color by a given factor."""
        # Simple color darkening - in a real implementation, you'd use proper color manipulation
        if color.startswith('#'):
            color = color[1:]
        
        # Convert to RGB
        r = int(color[0:2], 16)
        g = int(color[2:4], 16)
        b = int(color[4:6], 16)
        
        # Darken
        r = max(0, int(r * (1 - factor)))
        g = max(0, int(g * (1 - factor)))
        b = max(0, int(b * (1 - factor)))
        
        return f"#{r:02x}{g:02x}{b:02x}"


# Global instance
bootstrap_style = BootstrapStyleManager()
