const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipc<PERSON>enderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
    // App information
    getAppVersion: () => ipcRenderer.invoke('get-app-version'),
    
    // Server management
    getServerStatus: () => ipcRenderer.invoke('get-server-status'),
    restartServer: () => ipcRenderer.invoke('restart-server'),
    
    // System information
    platform: process.platform,
    
    // Utility functions
    openExternal: (url) => ipcRenderer.invoke('open-external', url),
    
    // Event listeners
    onServerStatusChange: (callback) => {
        ipcRenderer.on('server-status-changed', callback);
    },
    
    removeAllListeners: (channel) => {
        ipcRenderer.removeAllListeners(channel);
    }
});
