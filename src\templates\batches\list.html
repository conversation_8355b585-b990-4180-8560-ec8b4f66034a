{% extends "base.html" %}

{% block title %}Batch Management - Military Peer Review Assessment System{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">
                        <i class="bi bi-collection me-2 text-primary"></i>Batch Management
                    </h1>
                    <p class="text-muted mb-0">Manage student batches and course enrollments</p>
                </div>
                <div>
                    {% if current_user.role in ['super_admin', 'admin'] %}
                    <a href="{{ url_for('batches_create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Create New Batch
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-collection-fill text-primary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Total Batches</h6>
                            <h2 class="fw-bold text-primary mb-0">{{ batch_stats.total_batches }}</h2>
                            <small class="text-muted">All batches</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-check-circle-fill text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Active Batches</h6>
                            <h2 class="fw-bold text-success mb-0">{{ batch_stats.active_batches }}</h2>
                            <small class="text-muted">Currently running</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-people-fill text-info fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Total Students</h6>
                            <h2 class="fw-bold text-info mb-0">{{ batch_stats.total_students }}</h2>
                            <small class="text-muted">Enrolled students</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-person-check text-warning fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Active Students</h6>
                            <h2 class="fw-bold text-warning mb-0">{{ batch_stats.active_students }}</h2>
                            <small class="text-muted">Currently enrolled</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Batches Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="fw-semibold mb-0">
                            <i class="bi bi-table me-2 text-primary"></i>All Batches
                        </h5>
                        <div class="d-flex gap-2">
                            <div class="input-group" style="width: 300px;">
                                <input type="text" class="form-control" id="searchBatches" placeholder="Search batches...">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                            <select class="form-select" id="filterStatus" style="width: 150px;">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle" id="batchesTable">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-semibold">Batch Details</th>
                                    <th class="border-0 fw-semibold">Course</th>
                                    <th class="border-0 fw-semibold">Instructor</th>
                                    <th class="border-0 fw-semibold">Students</th>
                                    <th class="border-0 fw-semibold">Status</th>
                                    <th class="border-0 fw-semibold">Duration</th>
                                    <th class="border-0 fw-semibold text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for batch in batches %}
                                <tr data-status="{{ 'active' if batch.is_active else 'inactive' }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                                <i class="bi bi-collection text-primary"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-semibold">{{ batch.name }}</h6>
                                                <small class="text-muted">{{ batch.code }}</small>
                                                {% if batch.description %}
                                                <br><small class="text-muted">{{ batch.description[:50] }}{% if batch.description|length > 50 %}...{% endif %}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if batch.course_name %}
                                            <div class="fw-semibold">{{ batch.course_name }}</div>
                                            {% if batch.course_code %}
                                            <small class="text-muted">{{ batch.course_code }}</small>
                                            {% endif %}
                                        {% else %}
                                            <span class="text-muted">Not specified</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if batch.instructor %}
                                            <div class="fw-semibold">{{ batch.instructor.full_name }}</div>
                                            <small class="text-muted">{{ batch.instructor.username }}</small>
                                        {% else %}
                                            <span class="text-muted">Not assigned</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <span class="fw-semibold me-2">{{ batch.students|length if batch.students else 0 }}</span>
                                            <span class="text-muted">/ {{ batch.max_students }}</span>
                                        </div>
                                        <div class="progress mt-1" style="height: 4px;">
                                            {% set enrollment_pct = ((batch.students|length if batch.students else 0) / batch.max_students * 100) if batch.max_students > 0 else 0 %}
                                            <div class="progress-bar" style="width: {{ enrollment_pct }}%"></div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if batch.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                        <br>
                                        <small class="text-muted">{{ batch.academic_year }} - {{ batch.semester }}</small>
                                    </td>
                                    <td>
                                        {% if batch.start_date %}
                                            <div class="fw-semibold">{{ batch.start_date.strftime('%Y-%m-%d') }}</div>
                                        {% endif %}
                                        {% if batch.end_date %}
                                            <small class="text-muted">to {{ batch.end_date.strftime('%Y-%m-%d') }}</small>
                                        {% endif %}
                                        {% if not batch.start_date and not batch.end_date %}
                                            <span class="text-muted">Not specified</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('batch_students', batch_id=batch.id) }}" 
                                               class="btn btn-sm btn-outline-info" 
                                               title="Manage Students">
                                                <i class="bi bi-people"></i>
                                            </a>
                                            {% if current_user.role in ['super_admin', 'admin'] %}
                                            <a href="{{ url_for('batches_edit', batch_id=batch.id) }}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit Batch">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteBatch({{ batch.id }}, '{{ batch.name }}')"
                                                    title="Delete Batch">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Search functionality
    document.getElementById('searchBatches').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('#batchesTable tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    // Status filter functionality
    document.getElementById('filterStatus').addEventListener('change', function() {
        const selectedStatus = this.value;
        const rows = document.querySelectorAll('#batchesTable tbody tr');
        
        rows.forEach(row => {
            const status = row.getAttribute('data-status');
            row.style.display = (!selectedStatus || status === selectedStatus) ? '' : 'none';
        });
    });

    // Delete batch function
    function deleteBatch(batchId, batchName) {
        if (confirm(`Are you sure you want to delete batch "${batchName}"? This action cannot be undone and will affect all enrolled students.`)) {
            fetch(`/batches/${batchId}/delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-success alert-dismissible fade show';
                    alert.innerHTML = `
                        <i class="bi bi-check-circle-fill me-2"></i>
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.querySelector('.container-fluid').insertBefore(alert, document.querySelector('.row'));
                    
                    // Reload page to update statistics
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the batch.');
            });
        }
    }
</script>
{% endblock %}
