2025-05-29 17:38:16,830 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:38:16,861 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:38:16,862 - database.connection - ERROR - Database session error: Database not initialized
2025-05-29 17:38:16,862 - database.migrations - ERROR - Failed to create default admin user: Database operation failed: Database not initialized
2025-05-29 17:38:16,862 - database.migrations - ERROR - Initial database setup failed: Database operation failed: Database not initialized
2025-05-29 17:38:16,862 - config.database - WARNING - Initial setup warning: Database operation failed: Database not initialized
2025-05-29 17:38:16,863 - root - INFO - Database initialized successfully
2025-05-29 17:38:16,914 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:38:16,914 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 17:38:35,044 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 17:38:35,097 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "GET /login HTTP/1.1" 200 -
2025-05-29 17:38:35,364 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "GET /static/css/custom.css HTTP/1.1" 200 -
2025-05-29 17:38:35,374 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "GET /static/js/custom.js HTTP/1.1" 200 -
2025-05-29 17:38:35,961 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-29 17:45:19,880 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:45:19,909 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:45:19,917 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:45:19,923 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:45:20,008 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:45:20,013 - database.migrations - INFO - Sample users already exist
2025-05-29 17:45:20,014 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:45:20,017 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:45:20,019 - database.migrations - INFO - Sample users already exist
2025-05-29 17:45:20,019 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:45:20,019 - root - INFO - Database initialized successfully
2025-05-29 17:45:20,060 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:45:20,061 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 17:45:37,503 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:37] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 17:45:37,529 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:37] "GET /login HTTP/1.1" 200 -
2025-05-29 17:45:37,851 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:37] "GET /static/css/custom.css HTTP/1.1" 200 -
2025-05-29 17:45:37,871 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:37] "GET /static/js/custom.js HTTP/1.1" 200 -
2025-05-29 17:45:38,572 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:38] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-29 17:46:01,227 - database.connection - ERROR - Database session error: Invalid hash method ''.
2025-05-29 17:46:01,227 - root - ERROR - Authentication error: Database operation failed: Invalid hash method ''.
2025-05-29 17:46:01,228 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:46:01] "POST /login HTTP/1.1" 200 -
2025-05-29 17:46:01,537 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:46:01] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 17:46:01,588 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:46:01] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 17:50:04,438 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:50:04,470 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:50:04,473 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:50:04,478 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:50:04,537 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:50:04,543 - database.migrations - INFO - Sample users already exist
2025-05-29 17:50:04,544 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:50:04,545 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:50:04,546 - database.migrations - INFO - Sample users already exist
2025-05-29 17:50:04,546 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:50:04,546 - root - INFO - Database initialized successfully
2025-05-29 17:50:04,585 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:50:04,586 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 17:54:04,225 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:54:04,244 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:54:04,246 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:54:04,249 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:54:04,280 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:54:04,286 - database.migrations - INFO - Sample users already exist
2025-05-29 17:54:04,286 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:54:04,287 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:54:04,288 - database.migrations - INFO - Sample users already exist
2025-05-29 17:54:04,288 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:54:04,288 - root - INFO - Database initialized successfully
2025-05-29 17:54:04,314 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:54:04,314 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 17:54:11,337 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:11] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 17:54:11,371 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:11] "GET /login HTTP/1.1" 200 -
2025-05-29 17:54:11,691 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:11] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 17:54:11,709 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:11] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 17:54:27,473 - database.connection - ERROR - Database session error: Invalid hash method ''.
2025-05-29 17:54:27,473 - root - ERROR - Authentication error: Database operation failed: Invalid hash method ''.
2025-05-29 17:54:27,474 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:27] "POST /login HTTP/1.1" 200 -
2025-05-29 17:54:27,527 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:27] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 17:54:27,720 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:27] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 17:54:41,907 - database.connection - ERROR - Database session error: Invalid hash method ''.
2025-05-29 17:54:41,908 - root - ERROR - Authentication error: Database operation failed: Invalid hash method ''.
2025-05-29 17:54:41,910 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:41] "POST /login HTTP/1.1" 200 -
2025-05-29 17:54:41,952 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:41] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 17:54:42,153 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:42] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:02:03,130 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:02:03,252 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:02:03,287 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:02:03,291 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:02:03,376 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:02:03,385 - database.migrations - INFO - Sample users already exist
2025-05-29 18:02:03,385 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:02:03,386 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:02:03,388 - database.migrations - INFO - Sample users already exist
2025-05-29 18:02:03,388 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:02:03,388 - root - INFO - Database initialized successfully
2025-05-29 18:02:03,436 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 18:02:03,437 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 18:02:24,880 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:24] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 18:02:24,905 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:24] "GET /login HTTP/1.1" 200 -
2025-05-29 18:02:25,224 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:25] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:02:25,405 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:25] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:02:54,138 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:02:54,452 - root - INFO - Authentication successful for user: admin
2025-05-29 18:02:54,453 - root - ERROR - Login error: Instance <User at 0x2991a3c2c10> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:02:54,454 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:54] "POST /login HTTP/1.1" 200 -
2025-05-29 18:02:54,495 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:54] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:02:54,498 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:54] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:03:12,309 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:12] "GET /login HTTP/1.1" 200 -
2025-05-29 18:03:14,366 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:03:14,639 - root - INFO - Authentication successful for user: admin
2025-05-29 18:03:14,640 - root - ERROR - Login error: Instance <User at 0x2991a3a3ce0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:03:14,642 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:14] "POST /login HTTP/1.1" 200 -
2025-05-29 18:03:16,693 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:03:16,977 - root - WARNING - Authentication failed for user: admin - incorrect password
2025-05-29 18:03:16,979 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:16] "POST /login HTTP/1.1" 200 -
2025-05-29 18:03:19,034 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:19] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-05-29 18:03:43,517 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:43] "GET /login HTTP/1.1" 200 -
2025-05-29 18:03:45,535 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:03:45,805 - root - INFO - Authentication successful for user: admin
2025-05-29 18:03:45,805 - root - ERROR - Login error: Instance <User at 0x2991a49cef0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:03:45,806 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:45] "POST /login HTTP/1.1" 200 -
2025-05-29 18:03:47,858 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:03:48,126 - root - WARNING - Authentication failed for user: admin - incorrect password
2025-05-29 18:03:48,127 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:48] "POST /login HTTP/1.1" 200 -
2025-05-29 18:03:50,184 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:50] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-05-29 18:04:53,239 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:04:53,291 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:04:53,295 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:04:53,302 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:04:53,397 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:04:53,409 - database.migrations - INFO - Sample users already exist
2025-05-29 18:04:53,409 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:04:53,411 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:04:53,414 - database.migrations - INFO - Sample users already exist
2025-05-29 18:04:53,415 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:04:53,416 - root - INFO - Database initialized successfully
2025-05-29 18:04:53,479 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 18:04:53,480 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 18:05:17,883 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:05:17] "GET /login HTTP/1.1" 200 -
2025-05-29 18:05:19,917 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:05:20,189 - root - INFO - Authentication successful for user: admin
2025-05-29 18:05:20,201 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:05:20] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-05-29 18:05:22,239 - root - ERROR - Dashboard error: 'moment' is undefined
2025-05-29 18:05:22,260 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:05:22] "[35m[1mGET /dashboard HTTP/1.1[0m" 500 -
2025-05-29 18:05:24,313 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:05:24,623 - root - WARNING - Authentication failed for user: admin - incorrect password
2025-05-29 18:05:24,634 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:05:24] "[35m[1mPOST /login HTTP/1.1[0m" 500 -
2025-05-29 18:05:26,675 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:05:26] "[32mGET /logout HTTP/1.1[0m" 302 -
