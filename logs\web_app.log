2025-05-29 17:38:16,830 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:38:16,861 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:38:16,862 - database.connection - ERROR - Database session error: Database not initialized
2025-05-29 17:38:16,862 - database.migrations - ERROR - Failed to create default admin user: Database operation failed: Database not initialized
2025-05-29 17:38:16,862 - database.migrations - ERROR - Initial database setup failed: Database operation failed: Database not initialized
2025-05-29 17:38:16,862 - config.database - WARNING - Initial setup warning: Database operation failed: Database not initialized
2025-05-29 17:38:16,863 - root - INFO - Database initialized successfully
2025-05-29 17:38:16,914 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:38:16,914 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
