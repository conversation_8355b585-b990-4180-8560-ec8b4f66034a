2025-05-29 17:38:16,830 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:38:16,861 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:38:16,862 - database.connection - ERROR - Database session error: Database not initialized
2025-05-29 17:38:16,862 - database.migrations - ERROR - Failed to create default admin user: Database operation failed: Database not initialized
2025-05-29 17:38:16,862 - database.migrations - ERROR - Initial database setup failed: Database operation failed: Database not initialized
2025-05-29 17:38:16,862 - config.database - WARNING - Initial setup warning: Database operation failed: Database not initialized
2025-05-29 17:38:16,863 - root - INFO - Database initialized successfully
2025-05-29 17:38:16,914 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:38:16,914 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 17:38:35,044 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 17:38:35,097 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "GET /login HTTP/1.1" 200 -
2025-05-29 17:38:35,364 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "GET /static/css/custom.css HTTP/1.1" 200 -
2025-05-29 17:38:35,374 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "GET /static/js/custom.js HTTP/1.1" 200 -
2025-05-29 17:38:35,961 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-29 17:45:19,880 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:45:19,909 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:45:19,917 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:45:19,923 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:45:20,008 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:45:20,013 - database.migrations - INFO - Sample users already exist
2025-05-29 17:45:20,014 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:45:20,017 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:45:20,019 - database.migrations - INFO - Sample users already exist
2025-05-29 17:45:20,019 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:45:20,019 - root - INFO - Database initialized successfully
2025-05-29 17:45:20,060 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:45:20,061 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 17:45:37,503 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:37] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 17:45:37,529 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:37] "GET /login HTTP/1.1" 200 -
2025-05-29 17:45:37,851 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:37] "GET /static/css/custom.css HTTP/1.1" 200 -
2025-05-29 17:45:37,871 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:37] "GET /static/js/custom.js HTTP/1.1" 200 -
2025-05-29 17:45:38,572 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:38] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-29 17:46:01,227 - database.connection - ERROR - Database session error: Invalid hash method ''.
2025-05-29 17:46:01,227 - root - ERROR - Authentication error: Database operation failed: Invalid hash method ''.
2025-05-29 17:46:01,228 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:46:01] "POST /login HTTP/1.1" 200 -
2025-05-29 17:46:01,537 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:46:01] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 17:46:01,588 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:46:01] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 17:50:04,438 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:50:04,470 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:50:04,473 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:50:04,478 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:50:04,537 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:50:04,543 - database.migrations - INFO - Sample users already exist
2025-05-29 17:50:04,544 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:50:04,545 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:50:04,546 - database.migrations - INFO - Sample users already exist
2025-05-29 17:50:04,546 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:50:04,546 - root - INFO - Database initialized successfully
2025-05-29 17:50:04,585 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:50:04,586 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 17:54:04,225 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:54:04,244 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:54:04,246 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:54:04,249 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:54:04,280 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:54:04,286 - database.migrations - INFO - Sample users already exist
2025-05-29 17:54:04,286 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:54:04,287 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:54:04,288 - database.migrations - INFO - Sample users already exist
2025-05-29 17:54:04,288 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:54:04,288 - root - INFO - Database initialized successfully
2025-05-29 17:54:04,314 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:54:04,314 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 17:54:11,337 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:11] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 17:54:11,371 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:11] "GET /login HTTP/1.1" 200 -
2025-05-29 17:54:11,691 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:11] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 17:54:11,709 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:11] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 17:54:27,473 - database.connection - ERROR - Database session error: Invalid hash method ''.
2025-05-29 17:54:27,473 - root - ERROR - Authentication error: Database operation failed: Invalid hash method ''.
2025-05-29 17:54:27,474 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:27] "POST /login HTTP/1.1" 200 -
2025-05-29 17:54:27,527 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:27] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 17:54:27,720 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:27] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 17:54:41,907 - database.connection - ERROR - Database session error: Invalid hash method ''.
2025-05-29 17:54:41,908 - root - ERROR - Authentication error: Database operation failed: Invalid hash method ''.
2025-05-29 17:54:41,910 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:41] "POST /login HTTP/1.1" 200 -
2025-05-29 17:54:41,952 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:41] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 17:54:42,153 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:42] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:02:03,130 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:02:03,252 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:02:03,287 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:02:03,291 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:02:03,376 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:02:03,385 - database.migrations - INFO - Sample users already exist
2025-05-29 18:02:03,385 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:02:03,386 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:02:03,388 - database.migrations - INFO - Sample users already exist
2025-05-29 18:02:03,388 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:02:03,388 - root - INFO - Database initialized successfully
2025-05-29 18:02:03,436 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 18:02:03,437 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 18:02:24,880 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:24] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 18:02:24,905 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:24] "GET /login HTTP/1.1" 200 -
2025-05-29 18:02:25,224 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:25] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:02:25,405 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:25] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:02:54,138 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:02:54,452 - root - INFO - Authentication successful for user: admin
2025-05-29 18:02:54,453 - root - ERROR - Login error: Instance <User at 0x2991a3c2c10> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:02:54,454 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:54] "POST /login HTTP/1.1" 200 -
2025-05-29 18:02:54,495 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:54] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:02:54,498 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:02:54] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:03:12,309 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:12] "GET /login HTTP/1.1" 200 -
2025-05-29 18:03:14,366 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:03:14,639 - root - INFO - Authentication successful for user: admin
2025-05-29 18:03:14,640 - root - ERROR - Login error: Instance <User at 0x2991a3a3ce0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:03:14,642 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:14] "POST /login HTTP/1.1" 200 -
2025-05-29 18:03:16,693 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:03:16,977 - root - WARNING - Authentication failed for user: admin - incorrect password
2025-05-29 18:03:16,979 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:16] "POST /login HTTP/1.1" 200 -
2025-05-29 18:03:19,034 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:19] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-05-29 18:03:43,517 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:43] "GET /login HTTP/1.1" 200 -
2025-05-29 18:03:45,535 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:03:45,805 - root - INFO - Authentication successful for user: admin
2025-05-29 18:03:45,805 - root - ERROR - Login error: Instance <User at 0x2991a49cef0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:03:45,806 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:45] "POST /login HTTP/1.1" 200 -
2025-05-29 18:03:47,858 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:03:48,126 - root - WARNING - Authentication failed for user: admin - incorrect password
2025-05-29 18:03:48,127 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:48] "POST /login HTTP/1.1" 200 -
2025-05-29 18:03:50,184 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:03:50] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-05-29 18:04:53,239 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:04:53,291 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:04:53,295 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:04:53,302 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:04:53,397 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:04:53,409 - database.migrations - INFO - Sample users already exist
2025-05-29 18:04:53,409 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:04:53,411 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:04:53,414 - database.migrations - INFO - Sample users already exist
2025-05-29 18:04:53,415 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:04:53,416 - root - INFO - Database initialized successfully
2025-05-29 18:04:53,479 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 18:04:53,480 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 18:05:17,883 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:05:17] "GET /login HTTP/1.1" 200 -
2025-05-29 18:05:19,917 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:05:20,189 - root - INFO - Authentication successful for user: admin
2025-05-29 18:05:20,201 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:05:20] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-05-29 18:05:22,239 - root - ERROR - Dashboard error: 'moment' is undefined
2025-05-29 18:05:22,260 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:05:22] "[35m[1mGET /dashboard HTTP/1.1[0m" 500 -
2025-05-29 18:05:24,313 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:05:24,623 - root - WARNING - Authentication failed for user: admin - incorrect password
2025-05-29 18:05:24,634 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:05:24] "[35m[1mPOST /login HTTP/1.1[0m" 500 -
2025-05-29 18:05:26,675 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:05:26] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-05-29 18:06:09,712 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:06:09,774 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:06:09,785 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:06:09,792 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:06:09,844 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:06:09,853 - database.migrations - INFO - Sample users already exist
2025-05-29 18:06:09,854 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:06:09,855 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:06:09,856 - database.migrations - INFO - Sample users already exist
2025-05-29 18:06:09,856 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:06:09,857 - root - INFO - Database initialized successfully
2025-05-29 18:06:09,903 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 18:06:09,906 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 18:06:24,152 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:06:24] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 18:06:24,187 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:06:24] "GET /login HTTP/1.1" 200 -
2025-05-29 18:06:24,512 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:06:24] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:06:24,529 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:06:24] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:06:37,272 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:06:37] "GET /login HTTP/1.1" 200 -
2025-05-29 18:06:39,308 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:06:39,578 - root - INFO - Authentication successful for user: admin
2025-05-29 18:06:39,590 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:06:39] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-05-29 18:06:41,659 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:06:41] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:06:43,790 - root - ERROR - Users list error: 'UserService' object has no attribute 'get_user_statistics'
2025-05-29 18:06:43,792 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:06:43] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:06:45,844 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:06:45] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:06:47,878 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:06:48,145 - root - WARNING - Authentication failed for user: admin - incorrect password
2025-05-29 18:06:48,147 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:06:48] "POST /login HTTP/1.1" 200 -
2025-05-29 18:06:50,166 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:06:50] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-05-29 18:07:05,035 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:05] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 18:07:05,039 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:05] "GET /login HTTP/1.1" 200 -
2025-05-29 18:07:05,304 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:05] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:07:05,380 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:05] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:07:14,231 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:07:14,516 - root - INFO - Authentication successful for user: admin
2025-05-29 18:07:14,524 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:14] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-05-29 18:07:14,533 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:14] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:07:14,603 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:14] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:07:14,925 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:14] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:07:22,974 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:22] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:07:23,069 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:23] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:07:23,236 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:23] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:07:24,603 - root - ERROR - Users list error: 'UserService' object has no attribute 'get_user_statistics'
2025-05-29 18:07:24,604 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:24] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:07:24,911 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:24] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:07:25,272 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:25] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:07:25,272 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:07:25] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:09:39,704 - root - ERROR - Users list error: 'UserService' object has no attribute 'get_user_statistics'
2025-05-29 18:09:39,706 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:09:39] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:09:39,713 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:09:39] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:09:39,952 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:09:39] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:09:40,053 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:09:40] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:18:00,013 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:18:00,027 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:18:00,029 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:18:00,035 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:18:00,110 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:18:00,114 - database.migrations - INFO - Sample users already exist
2025-05-29 18:18:00,117 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:18:00,118 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:18:00,119 - database.migrations - INFO - Sample users already exist
2025-05-29 18:18:00,120 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:18:00,120 - root - INFO - Database initialized successfully
2025-05-29 18:18:00,158 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 18:18:00,159 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 18:18:20,852 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:18:20] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 18:18:20,885 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:18:20] "GET /login HTTP/1.1" 200 -
2025-05-29 18:18:21,169 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:18:21] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:18:21,336 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:18:21] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:18:25,173 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:18:25,509 - root - INFO - Authentication successful for user: admin
2025-05-29 18:18:25,521 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:18:25] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-05-29 18:18:25,555 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:18:25] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:18:25,614 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:18:25] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:18:25,921 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:18:25] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:18:31,305 - root - ERROR - Users list error: Instance <User at 0x1c44d349350> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:18:31,306 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:18:31] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:18:31,342 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:18:31] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:18:31,551 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:18:31] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:18:31,704 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:18:31] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:19:18,629 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:18] "GET /batches HTTP/1.1" 200 -
2025-05-29 18:19:18,891 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:18] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:19:18,988 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:18] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:19:22,069 - root - ERROR - Error loading teachers: 'UserService' object has no attribute 'get_users_by_role'
2025-05-29 18:19:22,075 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:22] "GET /batches/create HTTP/1.1" 200 -
2025-05-29 18:19:22,192 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:22] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:19:22,325 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:22] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:19:26,304 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:26] "GET /batches HTTP/1.1" 200 -
2025-05-29 18:19:26,603 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:26] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:19:26,656 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:26] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:19:44,585 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:44] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-05-29 18:19:44,905 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:44] "GET /login HTTP/1.1" 200 -
2025-05-29 18:19:45,254 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:45] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:19:45,259 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:45] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:19:52,737 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:19:53,070 - root - INFO - Authentication successful for user: admin
2025-05-29 18:19:53,077 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:53] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-05-29 18:19:53,089 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:53] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:19:53,453 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:53] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:19:53,454 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:19:53] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:23:49,164 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:23:49] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:23:49,214 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:23:49] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:23:49,410 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:23:49] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:23:52,494 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:23:52] "[32mGET /logout HTTP/1.1[0m" 302 -
2025-05-29 18:23:52,801 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:23:52] "GET /login HTTP/1.1" 200 -
2025-05-29 18:23:53,153 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:23:53] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:23:53,154 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:23:53] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:25:47,463 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:25:47,593 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:25:47,602 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:25:47,624 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:25:47,708 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:25:47,713 - database.migrations - INFO - Sample users already exist
2025-05-29 18:25:47,714 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:25:47,718 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:25:47,724 - database.migrations - INFO - Sample users already exist
2025-05-29 18:25:47,726 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:25:47,726 - root - INFO - Database initialized successfully
2025-05-29 18:25:47,796 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 18:25:47,883 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 18:26:08,044 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:26:08] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 18:26:08,095 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:26:08] "GET /login HTTP/1.1" 200 -
2025-05-29 18:26:08,381 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:26:08] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:26:08,455 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:26:08] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:26:35,458 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:26:35,769 - root - INFO - Authentication successful for user: admin
2025-05-29 18:26:35,778 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:26:35] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-05-29 18:26:35,807 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:26:35] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:26:35,922 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:26:35] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:26:36,236 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:26:36] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:26:39,014 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:26:39] "GET /batches HTTP/1.1" 200 -
2025-05-29 18:26:39,146 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:26:39] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:26:39,241 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:26:39] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:42:16,541 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:42:16,544 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:42:16,546 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 18:42:16,551 - config.database - INFO - Database migrations completed successfully
2025-05-29 18:42:16,640 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:42:16,650 - database.migrations - INFO - Sample users already exist
2025-05-29 18:42:16,650 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:42:16,652 - database.migrations - INFO - Super admin user already exists
2025-05-29 18:42:16,653 - database.migrations - INFO - Sample users already exist
2025-05-29 18:42:16,653 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 18:42:16,653 - root - INFO - Database initialized successfully
2025-05-29 18:42:16,753 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 18:42:16,829 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 18:43:39,460 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:43:39] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 18:43:39,535 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:43:39] "GET /login HTTP/1.1" 200 -
2025-05-29 18:43:39,754 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:43:39] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:43:39,985 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:43:39] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:43:40,336 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:43:40] "GET /static/images/favicon.ico HTTP/1.1" 200 -
2025-05-29 18:45:26,159 - root - INFO - Attempting authentication for user: admin
2025-05-29 18:45:26,507 - root - INFO - Authentication successful for user: admin
2025-05-29 18:45:26,523 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:26] "[32mPOST /login HTTP/1.1[0m" 302 -
2025-05-29 18:45:26,556 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:26] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:45:26,628 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:26] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:45:26,939 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:26] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:45:29,554 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:29] "GET /assessments HTTP/1.1" 200 -
2025-05-29 18:45:29,599 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:29] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:45:29,788 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:29] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:45:32,611 - database.connection - ERROR - Database session error: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:45:32,611 - services.reporting_service - ERROR - Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:45:32,611 - root - ERROR - Reports dashboard error: Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:45:32,612 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:32] "[32mGET /reports HTTP/1.1[0m" 302 -
2025-05-29 18:45:32,736 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:32] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:45:32,867 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:32] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:45:33,091 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:33] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:45:38,359 - database.connection - ERROR - Database session error: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:45:38,360 - services.reporting_service - ERROR - Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:45:38,360 - root - ERROR - Reports dashboard error: Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:45:38,361 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:38] "[32mGET /reports HTTP/1.1[0m" 302 -
2025-05-29 18:45:38,667 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:38] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:45:39,021 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:39] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:45:39,022 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:39] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:45:54,799 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:54] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:45:55,102 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:55] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:45:55,154 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:45:55] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:46:05,955 - root - ERROR - Users list error: Instance <User at 0x13c2c093a80> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:46:05,956 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:46:05] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:46:06,036 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:46:06] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:46:06,187 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:46:06] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:46:06,370 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:46:06] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:51:06,728 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:06] "GET /api/stats HTTP/1.1" 200 -
2025-05-29 18:51:06,975 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:06] "GET /api/stats HTTP/1.1" 200 -
2025-05-29 18:51:35,029 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:35] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:51:35,420 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:35] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:51:35,425 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:35] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:51:35,821 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:35] "[36mGET /static/images/favicon.ico HTTP/1.1[0m" 304 -
2025-05-29 18:51:38,346 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:38] "GET /assessments HTTP/1.1" 200 -
2025-05-29 18:51:38,652 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:38] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:51:38,721 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:38] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:51:42,194 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:42] "GET /assessments/create HTTP/1.1" 200 -
2025-05-29 18:51:42,277 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:42] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:51:42,456 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:42] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:51:44,911 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:44] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:51:45,220 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:45] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:51:45,278 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:45] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:51:47,323 - root - ERROR - Users list error: Instance <User at 0x13c2c0e1d00> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:51:47,324 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:47] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:51:47,389 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:47] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:51:47,572 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:47] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:51:47,719 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:47] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:51:50,084 - root - ERROR - Users list error: Instance <User at 0x13c2c25f2f0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:51:50,086 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:50] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:51:50,386 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:50] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:51:50,740 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:50] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:51:50,742 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:50] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:51:52,755 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:52] "GET /batches HTTP/1.1" 200 -
2025-05-29 18:51:53,036 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:53] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:51:53,104 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:53] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:51:55,505 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:55] "GET /assessments HTTP/1.1" 200 -
2025-05-29 18:51:55,587 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:55] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:51:55,754 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:55] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:51:56,921 - database.connection - ERROR - Database session error: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:51:56,922 - services.reporting_service - ERROR - Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:51:56,922 - root - ERROR - Reports dashboard error: Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:51:56,923 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:56] "[32mGET /reports HTTP/1.1[0m" 302 -
2025-05-29 18:51:57,219 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:57] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:51:57,569 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:57] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:51:57,570 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:51:57] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:52:00,038 - root - ERROR - Users list error: Instance <User at 0x13c2c2ca430> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:52:00,039 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:00] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:52:00,337 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:00] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:52:00,689 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:00] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:52:00,691 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:00] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:52:07,239 - root - ERROR - Users list error: Instance <User at 0x13c2c0c9f10> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:52:07,240 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:07] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:52:07,537 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:07] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:52:07,883 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:07] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:52:07,885 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:07] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:52:25,438 - database.connection - ERROR - Database session error: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:52:25,453 - services.reporting_service - ERROR - Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:52:25,481 - root - ERROR - Reports dashboard error: Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:52:25,488 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:25] "[32mGET /reports HTTP/1.1[0m" 302 -
2025-05-29 18:52:25,611 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:25] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:52:25,964 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:25] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:52:25,968 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:25] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:52:33,035 - database.connection - ERROR - Database session error: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:52:33,038 - services.reporting_service - ERROR - Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:52:33,038 - root - ERROR - Reports dashboard error: Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:52:33,040 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:33] "[32mGET /reports HTTP/1.1[0m" 302 -
2025-05-29 18:52:33,338 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:33] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:52:33,702 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:33] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:52:33,703 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:33] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:52:43,371 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:43] "GET /assessments HTTP/1.1" 200 -
2025-05-29 18:52:43,738 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:43] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:52:43,877 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:43] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:52:52,471 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:52] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:52:52,578 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:52] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:52:52,721 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:52:52] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:53:55,737 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:53:55] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:53:55,785 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:53:55] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:53:55,875 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:53:55] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:53:57,738 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:53:57] "GET /batches HTTP/1.1" 200 -
2025-05-29 18:53:57,833 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:53:57] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:53:57,992 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:53:57] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:53:59,655 - root - ERROR - Users list error: Instance <User at 0x13c2c0caa50> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:53:59,656 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:53:59] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:53:59,952 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:53:59] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:54:00,307 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:54:00] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:54:00,308 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:54:00] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:55:15,951 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:15] "GET /assessments HTTP/1.1" 200 -
2025-05-29 18:55:16,031 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:16] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:55:16,195 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:16] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:55:17,827 - root - ERROR - Users list error: Instance <User at 0x13c2c0ca5d0> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:55:17,828 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:17] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:55:17,905 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:17] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:55:18,085 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:18] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:55:18,271 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:18] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:55:19,813 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:19] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:55:20,124 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:20] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:55:20,163 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:20] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:55:20,508 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:20] "[36mGET /static/images/favicon.ico HTTP/1.1[0m" 304 -
2025-05-29 18:55:22,513 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:22] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:55:22,819 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:22] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:55:22,872 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:22] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:55:56,025 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:56] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:55:56,506 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:55:56] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-05-29 18:56:03,889 - root - ERROR - Users list error: Instance <User at 0x13c2c0cab10> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:56:03,890 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:56:03] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:56:04,170 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:56:04] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:56:04,549 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:56:04] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:56:04,551 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:56:04] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:56:04,562 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:56:04] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-05-29 18:56:08,333 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:56:08] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:56:08,657 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:56:08] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:56:08,708 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:56:08] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-05-29 18:56:08,711 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:56:08] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:58:07,543 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:07] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:58:07,741 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:07] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
2025-05-29 18:58:07,953 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:07] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:58:08,242 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:08] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:58:08,679 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:08] "[36mGET /static/images/favicon.ico HTTP/1.1[0m" 304 -
2025-05-29 18:58:10,787 - root - ERROR - Users list error: Instance <User at 0x13c2c0c9e50> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:58:10,789 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:10] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:58:11,086 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:11] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:58:11,437 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:11] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:58:11,438 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:11] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:58:14,917 - database.connection - ERROR - Database session error: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:58:14,918 - services.reporting_service - ERROR - Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:58:14,919 - root - ERROR - Reports dashboard error: Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:58:14,920 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:14] "[32mGET /reports HTTP/1.1[0m" 302 -
2025-05-29 18:58:15,219 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:15] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:58:15,569 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:15] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:58:15,574 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:15] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:58:18,713 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:18] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:58:19,018 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:19] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:58:19,089 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:19] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:58:57,359 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:57] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:58:57,422 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:57] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:58:57,750 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:57] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:58:58,136 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:58:58] "[36mGET /static/images/favicon.ico HTTP/1.1[0m" 304 -
2025-05-29 18:59:01,226 - root - ERROR - Users list error: Instance <User at 0x13c2c0cb110> is not bound to a Session; attribute refresh operation cannot proceed (Background on this error at: https://sqlalche.me/e/20/bhk3)
2025-05-29 18:59:01,228 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:59:01] "[32mGET /users HTTP/1.1[0m" 302 -
2025-05-29 18:59:01,520 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:59:01] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:59:01,873 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:59:01] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:59:01,874 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:59:01] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:59:04,155 - database.connection - ERROR - Database session error: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:59:04,155 - services.reporting_service - ERROR - Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:59:04,156 - root - ERROR - Reports dashboard error: Failed to get dashboard statistics: Database operation failed: type object 'Assessment' has no attribute 'assessment_type'
2025-05-29 18:59:04,157 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:59:04] "[32mGET /reports HTTP/1.1[0m" 302 -
2025-05-29 18:59:04,458 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:59:04] "GET /dashboard HTTP/1.1" 200 -
2025-05-29 18:59:04,819 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:59:04] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:59:04,820 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:59:04] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 18:59:05,426 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:59:05] "GET /assessments HTTP/1.1" 200 -
2025-05-29 18:59:05,757 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:59:05] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 18:59:05,890 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 18:59:05] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
