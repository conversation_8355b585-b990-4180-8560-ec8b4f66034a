2025-05-29 17:38:16,830 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:38:16,861 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:38:16,862 - database.connection - ERROR - Database session error: Database not initialized
2025-05-29 17:38:16,862 - database.migrations - ERROR - Failed to create default admin user: Database operation failed: Database not initialized
2025-05-29 17:38:16,862 - database.migrations - ERROR - Initial database setup failed: Database operation failed: Database not initialized
2025-05-29 17:38:16,862 - config.database - WARNING - Initial setup warning: Database operation failed: Database not initialized
2025-05-29 17:38:16,863 - root - INFO - Database initialized successfully
2025-05-29 17:38:16,914 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:38:16,914 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 17:38:35,044 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 17:38:35,097 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "GET /login HTTP/1.1" 200 -
2025-05-29 17:38:35,364 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "GET /static/css/custom.css HTTP/1.1" 200 -
2025-05-29 17:38:35,374 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "GET /static/js/custom.js HTTP/1.1" 200 -
2025-05-29 17:38:35,961 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:38:35] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-29 17:45:19,880 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:45:19,909 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:45:19,917 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:45:19,923 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:45:20,008 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:45:20,013 - database.migrations - INFO - Sample users already exist
2025-05-29 17:45:20,014 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:45:20,017 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:45:20,019 - database.migrations - INFO - Sample users already exist
2025-05-29 17:45:20,019 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:45:20,019 - root - INFO - Database initialized successfully
2025-05-29 17:45:20,060 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:45:20,061 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 17:45:37,503 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:37] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 17:45:37,529 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:37] "GET /login HTTP/1.1" 200 -
2025-05-29 17:45:37,851 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:37] "GET /static/css/custom.css HTTP/1.1" 200 -
2025-05-29 17:45:37,871 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:37] "GET /static/js/custom.js HTTP/1.1" 200 -
2025-05-29 17:45:38,572 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:45:38] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-05-29 17:46:01,227 - database.connection - ERROR - Database session error: Invalid hash method ''.
2025-05-29 17:46:01,227 - root - ERROR - Authentication error: Database operation failed: Invalid hash method ''.
2025-05-29 17:46:01,228 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:46:01] "POST /login HTTP/1.1" 200 -
2025-05-29 17:46:01,537 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:46:01] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 17:46:01,588 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:46:01] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 17:50:04,438 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:50:04,470 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:50:04,473 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:50:04,478 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:50:04,537 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:50:04,543 - database.migrations - INFO - Sample users already exist
2025-05-29 17:50:04,544 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:50:04,545 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:50:04,546 - database.migrations - INFO - Sample users already exist
2025-05-29 17:50:04,546 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:50:04,546 - root - INFO - Database initialized successfully
2025-05-29 17:50:04,585 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:50:04,586 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 17:54:04,225 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:54:04,244 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:54:04,246 - config.database - INFO - Database connection established: data\peer_review.db
2025-05-29 17:54:04,249 - config.database - INFO - Database migrations completed successfully
2025-05-29 17:54:04,280 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:54:04,286 - database.migrations - INFO - Sample users already exist
2025-05-29 17:54:04,286 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:54:04,287 - database.migrations - INFO - Super admin user already exists
2025-05-29 17:54:04,288 - database.migrations - INFO - Sample users already exist
2025-05-29 17:54:04,288 - database.migrations - INFO - Initial database setup completed successfully
2025-05-29 17:54:04,288 - root - INFO - Database initialized successfully
2025-05-29 17:54:04,314 - werkzeug - INFO - [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on http://127.0.0.1:5000
2025-05-29 17:54:04,314 - werkzeug - INFO - [33mPress CTRL+C to quit[0m
2025-05-29 17:54:11,337 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:11] "[32mGET / HTTP/1.1[0m" 302 -
2025-05-29 17:54:11,371 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:11] "GET /login HTTP/1.1" 200 -
2025-05-29 17:54:11,691 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:11] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 17:54:11,709 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:11] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 17:54:27,473 - database.connection - ERROR - Database session error: Invalid hash method ''.
2025-05-29 17:54:27,473 - root - ERROR - Authentication error: Database operation failed: Invalid hash method ''.
2025-05-29 17:54:27,474 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:27] "POST /login HTTP/1.1" 200 -
2025-05-29 17:54:27,527 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:27] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 17:54:27,720 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:27] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
2025-05-29 17:54:41,907 - database.connection - ERROR - Database session error: Invalid hash method ''.
2025-05-29 17:54:41,908 - root - ERROR - Authentication error: Database operation failed: Invalid hash method ''.
2025-05-29 17:54:41,910 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:41] "POST /login HTTP/1.1" 200 -
2025-05-29 17:54:41,952 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:41] "[36mGET /static/css/custom.css HTTP/1.1[0m" 304 -
2025-05-29 17:54:42,153 - werkzeug - INFO - 127.0.0.1 - - [29/May/2025 17:54:42] "[36mGET /static/js/custom.js HTTP/1.1[0m" 304 -
