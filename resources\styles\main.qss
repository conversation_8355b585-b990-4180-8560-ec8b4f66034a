/* Military Peer Review Assessment System - Bootstrap-Inspired Stylesheet */

/* Global Application Styles */
QApplication {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Robot<PERSON>, "Helvetica Neue", Arial, sans-serif;
    font-size: 16px;
    line-height: 1.5;
}

/* Main Window Styles */
QMainWindow {
    background-color: #f8f9fa;
    color: #212529;
}

/* Menu Bar Styles */
QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #e9ecef;
    padding: 4px 0px;
    font-size: 14px;
    font-weight: 500;
}

QMenuBar::item {
    padding: 10px 16px;
    margin: 0px 2px;
    border-radius: 6px;
    color: #374151;
}

QMenuBar::item:selected {
    background-color: #f3f4f6;
    color: #1f2937;
}

QMenuBar::item:pressed {
    background-color: #e5e7eb;
}

/* Menu Styles */
QMenu {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 8px 0px;
    font-size: 14px;
}

QMenu::item {
    padding: 10px 20px;
    margin: 2px 8px;
    border-radius: 4px;
}

QMenu::item:selected {
    background-color: #f3f4f6;
    color: #1f2937;
}

QMenu::separator {
    height: 1px;
    background-color: #e5e7eb;
    margin: 8px 16px;
}

/* Status Bar Styles */
QStatusBar {
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
    padding: 8px;
    font-size: 13px;
    color: #6b7280;
}

/* Bootstrap Button Styles */
QPushButton {
    background-color: #6c757d;
    border: 1px solid #6c757d;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 16px;
    font-weight: 400;
    color: #ffffff;
    min-height: 24px;
    text-align: center;
}

QPushButton:hover {
    background-color: #5c636a;
    border-color: #565e64;
}

QPushButton:pressed {
    background-color: #565e64;
    border-color: #51585e;
}

QPushButton:disabled {
    background-color: #6c757d;
    border-color: #6c757d;
    color: #ffffff;
    opacity: 0.65;
}

/* Primary Button */
QPushButton[class="btn-primary"] {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

QPushButton[class="btn-primary"]:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
}

QPushButton[class="btn-primary"]:pressed {
    background-color: #0a58ca;
    border-color: #0a53be;
}

/* Success Button */
QPushButton[class="btn-success"] {
    background-color: #198754;
    border-color: #198754;
}

QPushButton[class="btn-success"]:hover {
    background-color: #157347;
    border-color: #146c43;
}

/* Danger Button */
QPushButton[class="btn-danger"] {
    background-color: #dc3545;
    border-color: #dc3545;
}

QPushButton[class="btn-danger"]:hover {
    background-color: #c82333;
    border-color: #bd2130;
}

/* Warning Button */
QPushButton[class="btn-warning"] {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

QPushButton[class="btn-warning"]:hover {
    background-color: #e0a800;
    border-color: #d39e00;
}

/* Outline Buttons */
QPushButton[class="btn-outline-primary"] {
    background-color: transparent;
    border-color: #0d6efd;
    color: #0d6efd;
}

QPushButton[class="btn-outline-primary"]:hover {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: #ffffff;
}

/* Bootstrap Form Control Styles */
QLineEdit {
    background-color: #ffffff;
    border: 1px solid #ced4da;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 16px;
    color: #212529;
    min-height: 24px;
    line-height: 1.5;
}

QLineEdit:focus {
    border-color: #86b7fe;
    background-color: #ffffff;
    outline: none;
}

QLineEdit:hover {
    border-color: #b0b7c3;
}

QLineEdit:disabled {
    background-color: #e9ecef;
    border-color: #ced4da;
    color: #6c757d;
    opacity: 1;
}

/* Text Area Styles */
QTextEdit, QPlainTextEdit {
    background-color: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px;
    font-size: 14px;
    color: #1f2937;
}

QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #3b82f6;
    outline: none;
}

/* Combo Box Styles */
QComboBox {
    background-color: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 14px;
    color: #1f2937;
    min-height: 20px;
}

QComboBox:hover {
    border-color: #d1d5db;
}

QComboBox:focus {
    border-color: #3b82f6;
}

QComboBox::drop-down {
    border: none;
    width: 30px;
}

QComboBox::down-arrow {
    image: url(down_arrow.png);
    width: 12px;
    height: 12px;
}

/* Checkbox Styles */
QCheckBox {
    color: #374151;
    font-size: 14px;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border-radius: 4px;
    border: 2px solid #d1d5db;
    background-color: #ffffff;
}

QCheckBox::indicator:hover {
    border-color: #9ca3af;
}

QCheckBox::indicator:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
    image: url(check.png);
}

/* Radio Button Styles */
QRadioButton {
    color: #374151;
    font-size: 14px;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
    border-radius: 9px;
    border: 2px solid #d1d5db;
    background-color: #ffffff;
}

QRadioButton::indicator:hover {
    border-color: #9ca3af;
}

QRadioButton::indicator:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

/* Group Box Styles */
QGroupBox {
    font-weight: 600;
    font-size: 16px;
    color: #1f2937;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    margin-top: 12px;
    padding-top: 16px;
    background-color: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 16px;
    padding: 0 8px 0 8px;
    background-color: #ffffff;
}

/* Table Styles */
QTableWidget {
    background-color: #ffffff;
    alternate-background-color: #f9fafb;
    gridline-color: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    selection-background-color: #dbeafe;
}

QTableWidget::item {
    padding: 10px 8px;
    border-bottom: 1px solid #f3f4f6;
}

QTableWidget::item:selected {
    background-color: #dbeafe;
    color: #1e40af;
}

QHeaderView::section {
    background-color: #f9fafb;
    padding: 12px 8px;
    border: none;
    border-bottom: 2px solid #e5e7eb;
    font-weight: 600;
    font-size: 14px;
    color: #374151;
}

/* Scroll Bar Styles */
QScrollBar:vertical {
    background-color: #f9fafb;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #d1d5db;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9ca3af;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

/* Tab Widget Styles */
QTabWidget::pane {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #ffffff;
}

QTabBar::tab {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    padding: 10px 16px;
    margin-right: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

QTabBar::tab:selected {
    background-color: #ffffff;
    border-bottom-color: #ffffff;
}

QTabBar::tab:hover {
    background-color: #f3f4f6;
}

/* Progress Bar Styles */
QProgressBar {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #f9fafb;
    text-align: center;
    font-size: 12px;
    color: #374151;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3b82f6, stop:1 #2563eb);
    border-radius: 6px;
}

/* Tooltip Styles */
QToolTip {
    background-color: #1f2937;
    color: #ffffff;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
}
