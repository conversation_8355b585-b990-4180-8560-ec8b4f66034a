/* Military Peer Review Assessment System - Main Stylesheet */

/* Global Application Styles */
QApplication {
    font-family: "Segoe UI", "Arial", sans-serif;
    font-size: 14px;
}

/* Main Window Styles */
QMainWindow {
    background-color: #f8f9fa;
    color: #1f2937;
}

/* Menu Bar Styles */
QMenuBar {
    background-color: #ffffff;
    border-bottom: 1px solid #e9ecef;
    padding: 4px 0px;
    font-size: 14px;
    font-weight: 500;
}

QMenuBar::item {
    padding: 10px 16px;
    margin: 0px 2px;
    border-radius: 6px;
    color: #374151;
}

QMenuBar::item:selected {
    background-color: #f3f4f6;
    color: #1f2937;
}

QMenuBar::item:pressed {
    background-color: #e5e7eb;
}

/* Menu Styles */
QMenu {
    background-color: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    padding: 8px 0px;
    font-size: 14px;
}

QMenu::item {
    padding: 10px 20px;
    margin: 2px 8px;
    border-radius: 4px;
}

QMenu::item:selected {
    background-color: #f3f4f6;
    color: #1f2937;
}

QMenu::separator {
    height: 1px;
    background-color: #e5e7eb;
    margin: 8px 16px;
}

/* Status Bar Styles */
QStatusBar {
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
    padding: 8px;
    font-size: 13px;
    color: #6b7280;
}

/* Button Styles */
QPushButton {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #ffffff, stop:1 #f8f9fa);
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 14px;
    font-weight: 600;
    color: #374151;
    min-height: 20px;
}

QPushButton:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f9fafb, stop:1 #f3f4f6);
    border-color: #9ca3af;
    color: #1f2937;
}

QPushButton:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #f3f4f6, stop:1 #e5e7eb);
    border-color: #6b7280;
}

QPushButton:disabled {
    background-color: #f9fafb;
    border-color: #e5e7eb;
    color: #9ca3af;
}

/* Primary Button Styles */
QPushButton[class="primary"] {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3b82f6, stop:1 #2563eb);
    border: 1px solid #2563eb;
    color: #ffffff;
}

QPushButton[class="primary"]:hover {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #2563eb, stop:1 #1d4ed8);
    border-color: #1d4ed8;
}

QPushButton[class="primary"]:pressed {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #1d4ed8, stop:1 #1e40af);
}

/* Input Field Styles */
QLineEdit {
    background-color: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    color: #1f2937;
    min-height: 20px;
}

QLineEdit:focus {
    border-color: #3b82f6;
    background-color: #fefefe;
    outline: none;
}

QLineEdit:hover {
    border-color: #d1d5db;
}

QLineEdit:disabled {
    background-color: #f9fafb;
    border-color: #e5e7eb;
    color: #9ca3af;
}

/* Text Area Styles */
QTextEdit, QPlainTextEdit {
    background-color: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 12px;
    font-size: 14px;
    color: #1f2937;
}

QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #3b82f6;
    outline: none;
}

/* Combo Box Styles */
QComboBox {
    background-color: #ffffff;
    border: 2px solid #e5e7eb;
    border-radius: 8px;
    padding: 10px 16px;
    font-size: 14px;
    color: #1f2937;
    min-height: 20px;
}

QComboBox:hover {
    border-color: #d1d5db;
}

QComboBox:focus {
    border-color: #3b82f6;
}

QComboBox::drop-down {
    border: none;
    width: 30px;
}

QComboBox::down-arrow {
    image: url(down_arrow.png);
    width: 12px;
    height: 12px;
}

/* Checkbox Styles */
QCheckBox {
    color: #374151;
    font-size: 14px;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 18px;
    height: 18px;
    border-radius: 4px;
    border: 2px solid #d1d5db;
    background-color: #ffffff;
}

QCheckBox::indicator:hover {
    border-color: #9ca3af;
}

QCheckBox::indicator:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
    image: url(check.png);
}

/* Radio Button Styles */
QRadioButton {
    color: #374151;
    font-size: 14px;
    spacing: 8px;
}

QRadioButton::indicator {
    width: 18px;
    height: 18px;
    border-radius: 9px;
    border: 2px solid #d1d5db;
    background-color: #ffffff;
}

QRadioButton::indicator:hover {
    border-color: #9ca3af;
}

QRadioButton::indicator:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
}

/* Group Box Styles */
QGroupBox {
    font-weight: 600;
    font-size: 16px;
    color: #1f2937;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    margin-top: 12px;
    padding-top: 16px;
    background-color: #ffffff;
}

QGroupBox::title {
    subcontrol-origin: margin;
    left: 16px;
    padding: 0 8px 0 8px;
    background-color: #ffffff;
}

/* Table Styles */
QTableWidget {
    background-color: #ffffff;
    alternate-background-color: #f9fafb;
    gridline-color: #f3f4f6;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    font-size: 14px;
    selection-background-color: #dbeafe;
}

QTableWidget::item {
    padding: 10px 8px;
    border-bottom: 1px solid #f3f4f6;
}

QTableWidget::item:selected {
    background-color: #dbeafe;
    color: #1e40af;
}

QHeaderView::section {
    background-color: #f9fafb;
    padding: 12px 8px;
    border: none;
    border-bottom: 2px solid #e5e7eb;
    font-weight: 600;
    font-size: 14px;
    color: #374151;
}

/* Scroll Bar Styles */
QScrollBar:vertical {
    background-color: #f9fafb;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #d1d5db;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #9ca3af;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

/* Tab Widget Styles */
QTabWidget::pane {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #ffffff;
}

QTabBar::tab {
    background-color: #f9fafb;
    border: 1px solid #e5e7eb;
    padding: 10px 16px;
    margin-right: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

QTabBar::tab:selected {
    background-color: #ffffff;
    border-bottom-color: #ffffff;
}

QTabBar::tab:hover {
    background-color: #f3f4f6;
}

/* Progress Bar Styles */
QProgressBar {
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    background-color: #f9fafb;
    text-align: center;
    font-size: 12px;
    color: #374151;
}

QProgressBar::chunk {
    background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
        stop:0 #3b82f6, stop:1 #2563eb);
    border-radius: 6px;
}

/* Tooltip Styles */
QToolTip {
    background-color: #1f2937;
    color: #ffffff;
    border: 1px solid #374151;
    border-radius: 6px;
    padding: 8px 12px;
    font-size: 13px;
}
