# 🎖️ Military Peer Review Assessment System - CLI Version

## 📋 **OVERVIEW**

The Military Peer Review Assessment System has been transformed into a comprehensive **Command-Line Interface (CLI)** application, providing all the functionality of the original web version through an efficient, terminal-based interface suitable for military environments.

## ✨ **KEY FEATURES**

### **🔐 Authentication & Security**
- Secure user authentication with role-based access control
- Password encryption and session management
- Multi-role support: Super Admin, Admin, Teacher, Student

### **👥 User Management**
- Complete CRUD operations for user accounts
- Role assignment and management
- User search and filtering
- Bulk user operations

### **📚 Batch Management**
- Create and manage training batches
- Student enrollment and management
- Academic year and semester tracking
- Instructor assignment

### **📝 Assessment System**
- Multiple assessment types (Peer Evaluation, Self Assessment, Instructor Review)
- Comprehensive scoring rubrics (Leadership, Teamwork, Communication, Technical, Professionalism)
- Assessment scheduling and status management
- Anonymous feedback options

### **📊 Reporting & Analytics**
- Dashboard overview with real-time statistics
- Detailed assessment reports
- Student performance tracking
- Batch analytics and comparisons
- Data export to CSV format

### **💾 Data Management**
- SQLite database for offline operation
- Automatic database initialization
- Data integrity and validation
- Backup and export capabilities

## 🚀 **QUICK START**

### **Option 1: Direct CLI Execution**
```bash
# Navigate to project directory
cd peer-review-system-2

# Run CLI application directly
python src/cli_app.py
```

### **Option 2: Desktop Application**
```bash
# Install dependencies
npm install

# Start CLI desktop application
npm run start-cli
```

### **Option 3: Build Desktop Installer**
```bash
# Build desktop application with installer
python build_cli_desktop.py

# Or build for specific platform
python build_cli_desktop.py --platform windows
```

## 🔑 **DEFAULT LOGIN CREDENTIALS**

- **Username:** `admin`
- **Password:** `Admin@123`
- **Role:** Super Admin

## 📖 **USER GUIDE**

### **Navigation**
- Use numbered menu options to navigate
- Press `Ctrl+C` to cancel operations
- Follow on-screen prompts for input
- Use `Enter` to confirm selections

### **Main Menu Structure**

#### **Super Admin / Admin Users:**
1. **👥 User Management**
   - List all users
   - Create new users
   - Edit user details
   - Delete users
   - Search users
   - View user statistics

2. **📚 Batch Management**
   - List all batches
   - Create new batches
   - Edit batch details
   - Delete batches
   - Manage student enrollments
   - View batch statistics

3. **📝 Assessment Management**
   - List all assessments
   - Create new assessments
   - Edit assessment details
   - Delete assessments
   - View assessment results

4. **📊 Reports & Analytics**
   - Dashboard overview
   - Assessment reports
   - Student reports
   - Batch reports
   - System statistics
   - Data export

#### **Teacher Users:**
1. **📚 Batch Management** (Limited to assigned batches)
2. **📝 Assessment Management** (Create and manage assessments)
3. **📊 Reports & Analytics** (View reports for their assessments)

#### **Student Users:**
1. **📝 My Assessments** (Participate in peer evaluations)
2. **📊 My Performance** (View personal performance data)

### **Common Operations**

#### **Creating a Peer Evaluation Assessment**
1. Navigate to Assessment Management → Create New Assessment
2. Enter assessment title and description
3. Select "Peer Evaluation" as assessment type
4. Choose target batch
5. Set start and end dates
6. Configure scoring parameters
7. Set evaluation options (anonymous feedback, self-evaluation)
8. Add instructions for students

#### **Submitting Peer Evaluations (Students)**
1. Navigate to My Assessments → Submit Peer Evaluation
2. Select available assessment
3. Choose student to evaluate
4. Rate on 5 criteria (1-10 scale):
   - Leadership
   - Teamwork
   - Communication
   - Technical Skills
   - Professionalism
5. Add optional comments and feedback
6. Submit evaluation

#### **Viewing Reports**
1. Navigate to Reports & Analytics
2. Select report type (Assessment, Student, Batch)
3. Choose specific item to analyze
4. View statistics and performance data
5. Export to CSV if needed

## 🛠️ **TECHNICAL REQUIREMENTS**

### **System Requirements**
- **Operating System:** Windows 10+, macOS 10.14+, or Linux
- **Python:** 3.8 or higher
- **Memory:** 4GB RAM minimum
- **Storage:** 500MB available space
- **Display:** Terminal/Command prompt access

### **Python Dependencies**
```
sqlalchemy>=1.4.0
bcrypt>=3.2.0
python-dateutil>=2.8.0
```

### **Installation**
```bash
# Install Python dependencies
pip install -r requirements.txt

# Run application
python src/cli_app.py
```

## 🏗️ **ARCHITECTURE**

### **Project Structure**
```
peer-review-system-2/
├── src/
│   ├── cli_app.py              # Main CLI application
│   ├── cli/                    # CLI modules
│   │   ├── user_management.py  # User management CLI
│   │   ├── batch_management.py # Batch management CLI
│   │   ├── assessment_management.py # Assessment CLI
│   │   ├── student_assessments.py # Student evaluation CLI
│   │   └── reports.py          # Reporting CLI
│   ├── services/               # Business logic services
│   ├── models/                 # Database models
│   ├── config/                 # Configuration
│   ├── database/               # Database management
│   └── core/                   # Core utilities
├── desktop/                    # Desktop application files
├── data/                       # SQLite database
├── exports/                    # Exported reports
├── logs/                       # Application logs
└── requirements.txt            # Python dependencies
```

### **Key Components**

#### **CLI Application (`cli_app.py`)**
- Main application entry point
- User authentication and session management
- Menu system and navigation
- Color-coded output and formatting

#### **CLI Modules**
- **User Management:** Complete user CRUD operations
- **Batch Management:** Batch and student management
- **Assessment Management:** Assessment lifecycle management
- **Student Assessments:** Peer evaluation interface
- **Reports:** Analytics and data export

#### **Service Layer**
- **UserService:** User management operations
- **BatchService:** Batch and student operations
- **AssessmentService:** Assessment and evaluation operations
- **ReportingService:** Analytics and reporting

## 🖥️ **DESKTOP APPLICATION**

### **Features**
- **Embedded Terminal:** Professional terminal interface
- **Native Experience:** Desktop application with proper OS integration
- **Custom Branding:** Military-themed icons and styling
- **Offline Operation:** No internet connection required
- **Cross-Platform:** Windows, macOS, and Linux support

### **Building Desktop Application**
```bash
# Install Node.js dependencies
npm install

# Build for current platform
python build_cli_desktop.py

# Build for specific platform
python build_cli_desktop.py --platform windows

# Create portable version
python build_cli_desktop.py --no-portable
```

### **Desktop Application Structure**
```
desktop/
├── main_cli.js          # Main Electron process for CLI
├── terminal.html        # Terminal interface
├── assets/              # Application icons
└── installer.nsh        # Windows installer configuration
```

## 📊 **REPORTING CAPABILITIES**

### **Available Reports**
1. **Dashboard Overview:** System-wide statistics and trends
2. **Assessment Reports:** Detailed assessment analysis and results
3. **Student Reports:** Individual student performance tracking
4. **Batch Reports:** Cohort performance and comparisons
5. **System Statistics:** User, batch, and assessment metrics

### **Export Formats**
- **CSV:** Raw data export for further analysis
- **Text:** Formatted reports for documentation
- **Terminal Display:** Real-time formatted output

### **Report Data**
- Performance scores by criteria
- Participation rates and engagement
- Trend analysis over time
- Comparative statistics
- Detailed evaluation feedback

## 🔧 **TROUBLESHOOTING**

### **Common Issues**

#### **Application Won't Start**
- Ensure Python 3.8+ is installed
- Install required dependencies: `pip install -r requirements.txt`
- Check database permissions in `data/` directory

#### **Login Issues**
- Use default credentials: admin/Admin@123
- Check if database is properly initialized
- Verify user account status

#### **Performance Issues**
- Close other applications to free memory
- Check available disk space
- Restart the application

#### **Database Issues**
- Delete `data/peer_review.db` to reset database
- Application will recreate database on next startup
- Backup important data before resetting

### **Getting Help**
1. Check application logs in `logs/` directory
2. Review error messages in terminal
3. Restart application if unresponsive
4. Contact system administrator for persistent issues

## 📝 **CHANGELOG**

### **Version 1.0.0 - CLI Release**
- ✅ Complete CLI interface implementation
- ✅ All web functionality ported to command-line
- ✅ Desktop application with embedded terminal
- ✅ Comprehensive reporting and analytics
- ✅ Data export capabilities
- ✅ Professional military-themed design
- ✅ Cross-platform compatibility
- ✅ Offline operation support

## 👥 **CREDITS**

- **Author:** Maj. Sachin Kumar Singh
- **Developer:** Hrishikesh Mohite
- **Company:** Ajinkyacreatiion PVT. LTD.
- **Version:** 1.0.0 CLI
- **License:** MIT

## 🎯 **CONCLUSION**

The Military Peer Review Assessment System CLI version provides a powerful, efficient, and user-friendly command-line interface for managing peer evaluations in military training environments. With comprehensive functionality, professional design, and robust architecture, it serves as an ideal solution for organizations requiring a reliable, offline-capable assessment system.

**Ready for deployment in military training programs worldwide.** 🚀
