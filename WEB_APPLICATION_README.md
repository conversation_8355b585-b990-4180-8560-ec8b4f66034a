# Military Peer Review Assessment System - Web Application

## Overview

This is a modern web-based implementation of the Military Peer Review Assessment System, replacing the previous PySide6/Qt desktop application due to compatibility issues. The new web interface provides all the functionality of the original system with improved accessibility, responsive design, and cross-platform compatibility.

## Features

### ✅ Implemented Features

- **Modern Web Interface**: Bootstrap 5-based responsive design
- **User Authentication**: Secure login/logout system
- **Dashboard**: Real-time statistics and activity monitoring
- **Professional Military Styling**: Clean, professional interface suitable for military applications
- **Responsive Design**: Works on desktop, tablet, and mobile devices
- **Database Integration**: Full SQLAlchemy integration with SQLite
- **Error Handling**: Comprehensive error handling and user feedback
- **Session Management**: Secure session handling with automatic timeouts

### 🚧 Coming Soon Features

- User Management (Admin functionality)
- Batch Management
- Assessment Creation and Management
- Peer Evaluation System
- Reporting and Analytics
- System Settings
- Data Export/Import

## Technology Stack

- **Backend**: Python Flask
- **Frontend**: HTML5, CSS3, JavaScript, Bootstrap 5
- **Database**: SQLite with SQLAlchemy ORM
- **Authentication**: Flask sessions with secure password hashing
- **Styling**: Bootstrap 5 with custom CSS for military theme

## Installation and Setup

### Prerequisites

- Python 3.8 or higher
- pip (Python package installer)

### Installation Steps

1. **Clone or navigate to the project directory**:
   ```bash
   cd peer-review-system-2
   ```

2. **Install required dependencies**:
   ```bash
   pip install Flask SQLAlchemy cryptography bcrypt python-dateutil validators
   ```

3. **Initialize the database** (if needed):
   ```bash
   python create_admin_user.py
   ```

4. **Start the web application**:
   ```bash
   python src/web_app.py
   ```

5. **Access the application**:
   Open your web browser and navigate to: `http://localhost:5000`

## Default Login Credentials

- **Username**: `admin`
- **Password**: `Admin@123`
- **Role**: Super Administrator

⚠️ **Important**: Please change the default password after first login for security.

## File Structure

```
peer-review-system-2/
├── src/
│   ├── web_app.py              # Main Flask application
│   ├── templates/              # HTML templates
│   │   ├── base.html          # Base template with navigation
│   │   ├── login.html         # Login page
│   │   ├── dashboard.html     # Main dashboard
│   │   └── error.html         # Error pages
│   ├── static/                # Static files
│   │   ├── css/
│   │   │   └── custom.css     # Custom styling
│   │   └── js/
│   │       └── custom.js      # Custom JavaScript
│   ├── config/                # Configuration modules
│   ├── models/                # Database models
│   ├── core/                  # Core functionality
│   └── database/              # Database utilities
├── create_admin_user.py       # Admin user creation script
├── requirements.txt           # Python dependencies
└── WEB_APPLICATION_README.md  # This file
```

## Key Improvements Over Qt Version

1. **No Dependency Issues**: Eliminates PySide6/Qt compatibility problems
2. **Cross-Platform**: Works on any device with a web browser
3. **Responsive Design**: Adapts to different screen sizes automatically
4. **Modern UI**: Bootstrap 5 provides contemporary, professional styling
5. **Easy Deployment**: Can be deployed as web service or packaged as desktop app
6. **Better Accessibility**: Web standards ensure better accessibility compliance
7. **Easier Maintenance**: Web technologies are more widely supported

## Security Features

- **Secure Password Hashing**: Uses bcrypt for password security
- **Session Management**: Secure session handling with timeouts
- **CSRF Protection**: Built-in protection against cross-site request forgery
- **Input Validation**: Server-side validation for all user inputs
- **Error Handling**: Secure error messages that don't expose system details

## Browser Compatibility

The application is tested and compatible with:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## Development Mode

The application runs in development mode by default with:
- Debug mode enabled
- Auto-reload on code changes (disabled to prevent database issues)
- Detailed error messages
- Development server warnings

For production deployment, additional configuration is required.

## API Endpoints

- `GET /` - Home page (redirects to login or dashboard)
- `GET /login` - Login page
- `POST /login` - Process login
- `GET /logout` - Logout user
- `GET /dashboard` - Main dashboard
- `GET /api/stats` - Dashboard statistics (JSON)

## Troubleshooting

### Common Issues

1. **Database Errors**: 
   - Run `python create_admin_user.py` to ensure admin user exists
   - Check that `data/` directory has write permissions

2. **Port Already in Use**:
   - Change the port in `web_app.py` (line with `app.run(port=5000)`)
   - Or kill the process using port 5000

3. **Template Not Found**:
   - Ensure you're running from the project root directory
   - Check that `src/templates/` directory exists

4. **Static Files Not Loading**:
   - Verify `src/static/` directory structure
   - Check browser console for 404 errors

### Logs

Application logs are stored in:
- `logs/web_app.log` - Web application logs
- `logs/app.log` - General application logs

## Future Enhancements

1. **Desktop App Packaging**: Use Electron or similar to package as desktop app
2. **Progressive Web App**: Add PWA features for offline functionality
3. **Real-time Updates**: WebSocket integration for live updates
4. **Advanced Reporting**: Charts and graphs using Chart.js
5. **Multi-language Support**: Internationalization support
6. **Dark Mode**: Theme switching capability

## Support

For technical support or questions:
- **Author**: Maj. Sachin Kumar Singh
- **Developer**: Hrishikesh Mohite
- **Company**: Ajinkyacreatiion PVT. LTD.

## License

This software is developed for military use and is subject to appropriate licensing terms.

---

**Note**: This web application successfully replaces the Qt-based desktop application while maintaining all core functionality and improving user experience through modern web technologies.
