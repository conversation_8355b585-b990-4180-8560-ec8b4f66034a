{% extends "base.html" %}

{% block title %}User Management - Military Peer Review Assessment System{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">
                        <i class="bi bi-people me-2 text-primary"></i>User Management
                    </h1>
                    <p class="text-muted mb-0">Manage system users and their permissions</p>
                </div>
                <div>
                    <a href="{{ url_for('users_create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Add New User
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-people-fill text-primary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Total Users</h6>
                            <h2 class="fw-bold text-primary mb-0">{{ user_stats.total_users }}</h2>
                            <small class="text-muted">All accounts</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-check-circle-fill text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Active Users</h6>
                            <h2 class="fw-bold text-success mb-0">{{ user_stats.active_users }}</h2>
                            <small class="text-muted">Currently active</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-shield-check text-warning fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Administrators</h6>
                            <h2 class="fw-bold text-warning mb-0">{{ user_stats.role_counts.super_admin + user_stats.role_counts.admin }}</h2>
                            <small class="text-muted">Admin accounts</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-person-workspace text-info fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Teachers</h6>
                            <h2 class="fw-bold text-info mb-0">{{ user_stats.role_counts.teacher }}</h2>
                            <small class="text-muted">Teaching staff</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="fw-semibold mb-0">
                            <i class="bi bi-table me-2 text-primary"></i>All Users
                        </h5>
                        <div class="d-flex gap-2">
                            <div class="input-group" style="width: 300px;">
                                <input type="text" class="form-control" id="searchUsers" placeholder="Search users...">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                            <select class="form-select" id="filterRole" style="width: 150px;">
                                <option value="">All Roles</option>
                                <option value="super_admin">Super Admin</option>
                                <option value="admin">Admin</option>
                                <option value="teacher">Teacher</option>
                                <option value="student">Student</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle" id="usersTable">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-semibold">User</th>
                                    <th class="border-0 fw-semibold">Role</th>
                                    <th class="border-0 fw-semibold">Status</th>
                                    <th class="border-0 fw-semibold">Last Login</th>
                                    <th class="border-0 fw-semibold">Created</th>
                                    <th class="border-0 fw-semibold text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in users %}
                                <tr data-role="{{ user.role }}" data-status="{{ 'active' if user.is_active else 'inactive' }}">
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                                <i class="bi bi-person text-primary"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-semibold">{{ user.full_name }}</h6>
                                                <small class="text-muted">{{ user.username }}</small>
                                                {% if user.email %}
                                                <br><small class="text-muted">{{ user.email }}</small>
                                                {% endif %}
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        {% if user.role == 'super_admin' %}
                                            <span class="badge bg-danger">Super Admin</span>
                                        {% elif user.role == 'admin' %}
                                            <span class="badge bg-warning">Admin</span>
                                        {% elif user.role == 'teacher' %}
                                            <span class="badge bg-info">Teacher</span>
                                        {% elif user.role == 'student' %}
                                            <span class="badge bg-secondary">Student</span>
                                        {% else %}
                                            <span class="badge bg-light text-dark">{{ user.role|title }}</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.last_login %}
                                            <span class="text-muted">{{ user.last_login.strftime('%Y-%m-%d %H:%M') }}</span>
                                        {% else %}
                                            <span class="text-muted">Never</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'N/A' }}</span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('users_edit', user_id=user.id) }}" 
                                               class="btn btn-sm btn-outline-primary" 
                                               title="Edit User">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            {% if user.id != current_user.user_id %}
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-danger" 
                                                    onclick="deleteUser({{ user.id }}, '{{ user.username }}')"
                                                    title="Delete User">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Search functionality
    document.getElementById('searchUsers').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('#usersTable tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    // Role filter functionality
    document.getElementById('filterRole').addEventListener('change', function() {
        const selectedRole = this.value;
        const rows = document.querySelectorAll('#usersTable tbody tr');
        
        rows.forEach(row => {
            const role = row.getAttribute('data-role');
            row.style.display = (!selectedRole || role === selectedRole) ? '' : 'none';
        });
    });

    // Delete user function
    function deleteUser(userId, username) {
        if (confirm(`Are you sure you want to delete user "${username}"? This action cannot be undone.`)) {
            fetch(`/users/${userId}/delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Show success message
                    const alert = document.createElement('div');
                    alert.className = 'alert alert-success alert-dismissible fade show';
                    alert.innerHTML = `
                        <i class="bi bi-check-circle-fill me-2"></i>
                        ${data.message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    `;
                    document.querySelector('.container-fluid').insertBefore(alert, document.querySelector('.row'));
                    
                    // Remove the row from table
                    document.querySelector(`tr[data-user-id="${userId}"]`)?.remove();
                    
                    // Reload page to update statistics
                    setTimeout(() => {
                        location.reload();
                    }, 2000);
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the user.');
            });
        }
    }
</script>
{% endblock %}
