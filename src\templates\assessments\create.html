{% extends "base.html" %}

{% block title %}Create Assessment - Military Peer Review Assessment System{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">
                        <i class="bi bi-plus-circle me-2 text-primary"></i>Create Assessment
                    </h1>
                    <p class="text-muted mb-0">Create a new peer review assessment</p>
                </div>
                <div>
                    <a href="{{ url_for('assessments_list') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Assessments
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Create Assessment Form -->
    <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-form me-2 text-primary"></i>Assessment Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('assessments_create') }}" novalidate>
                        <div class="row g-3">
                            <!-- Basic Information -->
                            <div class="col-12">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-info-circle me-2"></i>Basic Information
                                </h6>
                            </div>

                            <!-- Assessment Title -->
                            <div class="col-md-8">
                                <label for="title" class="form-label fw-semibold">
                                    <i class="bi bi-card-text me-2"></i>Assessment Title <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="title" 
                                       name="title" 
                                       placeholder="Enter assessment title"
                                       required>
                            </div>

                            <!-- Assessment Type -->
                            <div class="col-md-4">
                                <label for="assessment_type" class="form-label fw-semibold">
                                    <i class="bi bi-tag me-2"></i>Assessment Type <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="assessment_type" name="assessment_type" required>
                                    <option value="">Select type</option>
                                    <option value="peer_evaluation">Peer Evaluation</option>
                                    <option value="self_assessment">Self Assessment</option>
                                    <option value="instructor_review">Instructor Review</option>
                                    <option value="final_assessment">Final Assessment</option>
                                </select>
                            </div>

                            <!-- Description -->
                            <div class="col-12">
                                <label for="description" class="form-label fw-semibold">
                                    <i class="bi bi-text-paragraph me-2"></i>Description
                                </label>
                                <textarea class="form-control" 
                                          id="description" 
                                          name="description" 
                                          rows="3" 
                                          placeholder="Enter assessment description"></textarea>
                            </div>

                            <!-- Batch Selection -->
                            <div class="col-12 mt-4">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-people me-2"></i>Target Batch
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <label for="batch_id" class="form-label fw-semibold">
                                    <i class="bi bi-collection me-2"></i>Batch <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="batch_id" name="batch_id" required>
                                    <option value="">Select batch</option>
                                    {% for batch in batches %}
                                    <option value="{{ batch.id }}">
                                        {{ batch.name }} ({{ batch.academic_year }} - Semester {{ batch.semester }})
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Schedule -->
                            <div class="col-12 mt-4">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-calendar me-2"></i>Schedule
                                </h6>
                            </div>

                            <!-- Start Date -->
                            <div class="col-md-6">
                                <label for="start_date" class="form-label fw-semibold">
                                    <i class="bi bi-calendar-event me-2"></i>Start Date
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="start_date" 
                                       name="start_date">
                            </div>

                            <!-- End Date -->
                            <div class="col-md-6">
                                <label for="end_date" class="form-label fw-semibold">
                                    <i class="bi bi-calendar-check me-2"></i>End Date
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="end_date" 
                                       name="end_date">
                            </div>

                            <!-- Scoring Configuration -->
                            <div class="col-12 mt-4">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-award me-2"></i>Scoring Configuration
                                </h6>
                            </div>

                            <!-- Max Score -->
                            <div class="col-md-6">
                                <label for="max_score" class="form-label fw-semibold">
                                    <i class="bi bi-trophy me-2"></i>Maximum Score
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="max_score" 
                                       name="max_score" 
                                       value="100"
                                       min="1"
                                       max="1000">
                            </div>

                            <!-- Passing Score -->
                            <div class="col-md-6">
                                <label for="passing_score" class="form-label fw-semibold">
                                    <i class="bi bi-check-circle me-2"></i>Passing Score
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="passing_score" 
                                       name="passing_score" 
                                       value="60"
                                       min="1"
                                       max="1000">
                            </div>

                            <!-- Assessment Settings -->
                            <div class="col-12 mt-4">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-gear me-2"></i>Assessment Settings
                                </h6>
                            </div>

                            <!-- Settings Checkboxes -->
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="allow_self_evaluation" 
                                           name="allow_self_evaluation">
                                    <label class="form-check-label fw-semibold" for="allow_self_evaluation">
                                        <i class="bi bi-person-check me-2"></i>Allow Self Evaluation
                                    </label>
                                    <div class="form-text">Allow students to evaluate themselves</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="anonymous_feedback" 
                                           name="anonymous_feedback"
                                           checked>
                                    <label class="form-check-label fw-semibold" for="anonymous_feedback">
                                        <i class="bi bi-eye-slash me-2"></i>Anonymous Feedback
                                    </label>
                                    <div class="form-text">Keep evaluator identities anonymous</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active"
                                           checked>
                                    <label class="form-check-label fw-semibold" for="is_active">
                                        <i class="bi bi-check-circle me-2"></i>Active Assessment
                                    </label>
                                    <div class="form-text">Make assessment available to students</div>
                                </div>
                            </div>

                            <!-- Instructions -->
                            <div class="col-12">
                                <label for="instructions" class="form-label fw-semibold">
                                    <i class="bi bi-list-ul me-2"></i>Instructions for Students
                                </label>
                                <textarea class="form-control" 
                                          id="instructions" 
                                          name="instructions" 
                                          rows="4" 
                                          placeholder="Enter detailed instructions for students on how to complete this assessment"></textarea>
                            </div>

                            <!-- Notes -->
                            <div class="col-12">
                                <label for="notes" class="form-label fw-semibold">
                                    <i class="bi bi-sticky me-2"></i>Internal Notes
                                </label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3" 
                                          placeholder="Internal notes for instructors (not visible to students)"></textarea>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-3 justify-content-end">
                                    <a href="{{ url_for('assessments_list') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Create Assessment
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Form validation
    (function() {
        'use strict';
        
        const form = document.querySelector('form');
        
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    })();

    // Auto-focus title field
    document.getElementById('title').focus();

    // Validate end date is after start date
    function validateDates() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const endDateField = document.getElementById('end_date');
        
        if (startDate && endDate && new Date(endDate) <= new Date(startDate)) {
            endDateField.setCustomValidity('End date must be after start date');
        } else {
            endDateField.setCustomValidity('');
        }
    }

    document.getElementById('start_date').addEventListener('change', validateDates);
    document.getElementById('end_date').addEventListener('change', validateDates);

    // Update passing score when max score changes
    document.getElementById('max_score').addEventListener('change', function() {
        const maxScore = parseInt(this.value);
        const passingScoreField = document.getElementById('passing_score');
        const currentPassing = parseInt(passingScoreField.value);
        
        if (currentPassing > maxScore) {
            passingScoreField.value = Math.floor(maxScore * 0.6); // 60% of max score
        }
        
        passingScoreField.max = maxScore;
    });
</script>
{% endblock %}
