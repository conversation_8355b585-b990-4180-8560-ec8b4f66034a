["tests/test_basic.py::test_imports", "tests/test_basic.py::test_password_hashing", "tests/test_basic.py::test_settings", "tests/test_basic.py::test_validators", "tests/test_improvements.py::test_bootstrap_style_manager", "tests/test_improvements.py::test_database_connection_manager", "tests/test_improvements.py::test_database_migrations", "tests/test_improvements.py::test_improved_error_handling", "tests/test_improvements.py::test_main_application_setup", "tests/test_improvements.py::test_quick_action_button_bootstrap_styling", "tests/test_improvements.py::test_responsive_layout", "tests/test_improvements.py::test_settings_functionality", "tests/test_improvements.py::test_stat_card_bootstrap_styling"]