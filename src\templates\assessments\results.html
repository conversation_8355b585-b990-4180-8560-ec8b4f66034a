{% extends "base.html" %}

{% block title %}{{ assessment.title }} - Results{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">
                        <i class="bi bi-bar-chart me-2 text-primary"></i>Assessment Results
                    </h1>
                    <p class="text-muted mb-0">
                        <strong>{{ assessment.title }}</strong> - {{ assessment.batch.name if assessment.batch else 'No Batch' }}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('assessment_details', assessment_id=assessment.id) }}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-arrow-left me-2"></i>Back to Assessment
                    </a>
                    <a href="{{ url_for('reports_assessment', assessment_id=assessment.id) }}" class="btn btn-primary">
                        <i class="bi bi-download me-2"></i>Export Results
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-2">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-people text-primary fs-1 mb-2"></i>
                    <h3 class="fw-bold text-primary mb-0">{{ results_stats.total_students }}</h3>
                    <small class="text-muted">Total Students</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-check-circle text-success fs-1 mb-2"></i>
                    <h3 class="fw-bold text-success mb-0">{{ results_stats.completed_evaluations }}</h3>
                    <small class="text-muted">Evaluations</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-percent text-info fs-1 mb-2"></i>
                    <h3 class="fw-bold text-info mb-0">{{ "%.1f"|format(results_stats.participation_rate) }}%</h3>
                    <small class="text-muted">Participation</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-star text-warning fs-1 mb-2"></i>
                    <h3 class="fw-bold text-warning mb-0">{{ "%.1f"|format(results_stats.average_overall_score) }}</h3>
                    <small class="text-muted">Avg Score</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-trophy text-success fs-1 mb-2"></i>
                    <h3 class="fw-bold text-success mb-0">{{ results_stats.highest_score }}</h3>
                    <small class="text-muted">Highest</small>
                </div>
            </div>
        </div>
        <div class="col-md-2">
            <div class="card border-0 shadow-sm">
                <div class="card-body text-center">
                    <i class="bi bi-arrow-down text-danger fs-1 mb-2"></i>
                    <h3 class="fw-bold text-danger mb-0">{{ results_stats.lowest_score }}</h3>
                    <small class="text-muted">Lowest</small>
                </div>
            </div>
        </div>
    </div>

    <!-- Score Distribution Chart -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-bar-chart me-2 text-primary"></i>Score Distribution by Criteria
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="criteriaChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-pie-chart me-2 text-primary"></i>Overall Score Distribution
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="distributionChart" width="400" height="300"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Individual Student Results -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="fw-semibold mb-0">
                            <i class="bi bi-table me-2 text-primary"></i>Individual Results
                        </h5>
                        <div class="d-flex gap-2">
                            <div class="input-group" style="width: 300px;">
                                <input type="text" class="form-control" id="searchResults" placeholder="Search students...">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                            <select class="form-select" id="sortResults" style="width: 200px;">
                                <option value="name">Sort by Name</option>
                                <option value="overall_desc">Overall Score (High to Low)</option>
                                <option value="overall_asc">Overall Score (Low to High)</option>
                                <option value="evaluations">Number of Evaluations</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    {% if student_results %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle" id="resultsTable">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-semibold">Student</th>
                                    <th class="border-0 fw-semibold text-center">Overall Score</th>
                                    <th class="border-0 fw-semibold text-center">Leadership</th>
                                    <th class="border-0 fw-semibold text-center">Teamwork</th>
                                    <th class="border-0 fw-semibold text-center">Communication</th>
                                    <th class="border-0 fw-semibold text-center">Technical</th>
                                    <th class="border-0 fw-semibold text-center">Professionalism</th>
                                    <th class="border-0 fw-semibold text-center">Evaluations</th>
                                    <th class="border-0 fw-semibold text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for result in student_results %}
                                <tr>
                                    <td>
                                        <div class="d-flex align-items-center">
                                            <div class="avatar-sm bg-primary bg-opacity-10 rounded-circle d-flex align-items-center justify-content-center me-3">
                                                <i class="bi bi-person text-primary"></i>
                                            </div>
                                            <div>
                                                <h6 class="mb-0 fw-semibold">{{ result.student_name }}</h6>
                                                <small class="text-muted">{{ result.student_id }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <div class="d-flex flex-column align-items-center">
                                            <span class="h5 fw-bold mb-0 
                                                {% if result.overall_score >= 8 %}text-success
                                                {% elif result.overall_score >= 6 %}text-warning
                                                {% else %}text-danger{% endif %}">
                                                {{ "%.1f"|format(result.overall_score) }}
                                            </span>
                                            <small class="text-muted">/ 10</small>
                                        </div>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-{{ 'success' if result.leadership_score >= 7 else 'warning' if result.leadership_score >= 5 else 'danger' }}">
                                            {{ "%.1f"|format(result.leadership_score) }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-{{ 'success' if result.teamwork_score >= 7 else 'warning' if result.teamwork_score >= 5 else 'danger' }}">
                                            {{ "%.1f"|format(result.teamwork_score) }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-{{ 'success' if result.communication_score >= 7 else 'warning' if result.communication_score >= 5 else 'danger' }}">
                                            {{ "%.1f"|format(result.communication_score) }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-{{ 'success' if result.technical_score >= 7 else 'warning' if result.technical_score >= 5 else 'danger' }}">
                                            {{ "%.1f"|format(result.technical_score) }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="badge bg-{{ 'success' if result.professionalism_score >= 7 else 'warning' if result.professionalism_score >= 5 else 'danger' }}">
                                            {{ "%.1f"|format(result.professionalism_score) }}
                                        </span>
                                    </td>
                                    <td class="text-center">
                                        <span class="fw-semibold">{{ result.evaluation_count }}</span>
                                        <br><small class="text-muted">evaluations</small>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-primary" 
                                                    onclick="viewStudentDetail({{ result.student_id }}, '{{ result.student_name }}')"
                                                    title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </button>
                                            <button type="button" 
                                                    class="btn btn-sm btn-outline-success" 
                                                    onclick="exportStudentReport({{ result.student_id }})"
                                                    title="Export Report">
                                                <i class="bi bi-download"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <div class="mb-3">
                            <i class="bi bi-bar-chart text-muted" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="text-muted">No Results Available</h5>
                        <p class="text-muted">No evaluations have been completed for this assessment yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Student Detail Modal -->
<div class="modal fade" id="studentDetailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-person me-2"></i>Student Evaluation Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="studentDetailContent">
                <!-- Content loaded dynamically -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Criteria Chart
    const criteriaCtx = document.getElementById('criteriaChart').getContext('2d');
    new Chart(criteriaCtx, {
        type: 'radar',
        data: {
            labels: ['Leadership', 'Teamwork', 'Communication', 'Technical', 'Professionalism'],
            datasets: [{
                label: 'Average Scores',
                data: [
                    {{ results_stats.average_leadership_score or 0 }},
                    {{ results_stats.average_teamwork_score or 0 }},
                    {{ results_stats.average_communication_score or 0 }},
                    {{ results_stats.average_technical_score or 0 }},
                    {{ results_stats.average_professionalism_score or 0 }}
                ],
                backgroundColor: 'rgba(54, 162, 235, 0.2)',
                borderColor: 'rgba(54, 162, 235, 1)',
                borderWidth: 2
            }]
        },
        options: {
            responsive: true,
            scales: {
                r: {
                    beginAtZero: true,
                    max: 10
                }
            }
        }
    });

    // Distribution Chart
    const distributionCtx = document.getElementById('distributionChart').getContext('2d');
    new Chart(distributionCtx, {
        type: 'doughnut',
        data: {
            labels: ['Excellent (8-10)', 'Good (6-8)', 'Average (4-6)', 'Below Average (0-4)'],
            datasets: [{
                data: [
                    {{ results_stats.excellent_count or 0 }},
                    {{ results_stats.good_count or 0 }},
                    {{ results_stats.average_count or 0 }},
                    {{ results_stats.below_average_count or 0 }}
                ],
                backgroundColor: [
                    '#28a745',
                    '#ffc107',
                    '#17a2b8',
                    '#dc3545'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Search functionality
    document.getElementById('searchResults').addEventListener('input', function() {
        const searchTerm = this.value.toLowerCase();
        const rows = document.querySelectorAll('#resultsTable tbody tr');
        
        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            row.style.display = text.includes(searchTerm) ? '' : 'none';
        });
    });

    // Sort functionality
    document.getElementById('sortResults').addEventListener('change', function() {
        const sortBy = this.value;
        const tbody = document.querySelector('#resultsTable tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        
        rows.sort((a, b) => {
            switch(sortBy) {
                case 'name':
                    return a.querySelector('h6').textContent.localeCompare(b.querySelector('h6').textContent);
                case 'overall_desc':
                    return parseFloat(b.cells[1].querySelector('.h5').textContent) - parseFloat(a.cells[1].querySelector('.h5').textContent);
                case 'overall_asc':
                    return parseFloat(a.cells[1].querySelector('.h5').textContent) - parseFloat(b.cells[1].querySelector('.h5').textContent);
                case 'evaluations':
                    return parseInt(b.cells[7].querySelector('.fw-semibold').textContent) - parseInt(a.cells[7].querySelector('.fw-semibold').textContent);
                default:
                    return 0;
            }
        });
        
        rows.forEach(row => tbody.appendChild(row));
    });

    function viewStudentDetail(studentId, studentName) {
        document.getElementById('studentDetailContent').innerHTML = '<div class="text-center"><div class="spinner-border" role="status"></div></div>';
        
        const modal = new bootstrap.Modal(document.getElementById('studentDetailModal'));
        modal.show();
        
        fetch(`/api/assessments/{{ assessment.id }}/student/${studentId}/details`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    document.getElementById('studentDetailContent').innerHTML = data.html;
                } else {
                    document.getElementById('studentDetailContent').innerHTML = '<div class="alert alert-danger">Error loading student details.</div>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('studentDetailContent').innerHTML = '<div class="alert alert-danger">Error loading student details.</div>';
            });
    }

    function exportStudentReport(studentId) {
        window.open(`/reports/student/${studentId}/assessment/{{ assessment.id }}`, '_blank');
    }
</script>
{% endblock %}
