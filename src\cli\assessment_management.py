"""
Assessment Management CLI Module

Provides command-line interface for assessment management operations.
"""

from typing import List, Optional
from datetime import datetime, date
from core.exceptions import ValidationError, DatabaseError


class AssessmentManagementCLI:
    """CLI for assessment management operations."""
    
    def __init__(self, main_app):
        self.app = main_app
        self.assessment_service = main_app.assessment_service
        self.batch_service = main_app.batch_service
    
    def show_menu(self):
        """Show assessment management menu."""
        while True:
            self.app.clear_screen()
            print(f"\n{self.app.CLIColors.BOLD}📝 ASSESSMENT MANAGEMENT{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            menu_options = [
                "📋 List All Assessments",
                "➕ Create New Assessment",
                "✏️ Edit Assessment",
                "🗑️ Delete Assessment",
                "📊 Assessment Results",
                "🔙 Back to Main Menu"
            ]
            
            choice = self.app.get_choice("Select an option:", menu_options)
            
            if choice == -1 or choice == 5:  # Back or cancelled
                break
            elif choice == 0:  # List Assessments
                self.list_assessments()
            elif choice == 1:  # Create Assessment
                self.create_assessment()
            elif choice == 2:  # Edit Assessment
                self.edit_assessment()
            elif choice == 3:  # Delete Assessment
                self.delete_assessment()
            elif choice == 4:  # Assessment Results
                self.view_assessment_results()
    
    def list_assessments(self):
        """List all assessments."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}📋 ASSESSMENT LIST{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            assessments = self.assessment_service.get_all_assessments(include_inactive=True)
            
            if not assessments:
                self.app.print_info("No assessments found.")
                self.app.wait_for_enter()
                return
            
            # Display assessments in table format
            headers = ["ID", "Title", "Type", "Batch", "Start Date", "End Date", "Status"]
            widths = [5, 25, 15, 20, 12, 12, 8]
            
            self.app.print_table_header(headers, widths)
            
            for assessment in assessments:
                batch_name = assessment.batch.name if assessment.batch else "No Batch"
                start_date = assessment.start_date.strftime('%Y-%m-%d') if assessment.start_date else "Not set"
                end_date = assessment.end_date.strftime('%Y-%m-%d') if assessment.end_date else "Not set"
                
                values = [
                    str(assessment.id),
                    assessment.title,
                    assessment.assessment_type.replace('_', ' ').title(),
                    batch_name,
                    start_date,
                    end_date,
                    "Active" if assessment.is_active else "Inactive"
                ]
                self.app.print_table_row(values, widths)
            
            print(f"\nTotal assessments: {len(assessments)}")
            
        except Exception as e:
            self.app.print_error(f"Failed to list assessments: {e}")
        
        self.app.wait_for_enter()
    
    def create_assessment(self):
        """Create a new assessment."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}➕ CREATE NEW ASSESSMENT{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # Get assessment information
            title = self.app.get_input("Assessment Title: ")
            if not title:
                return
            
            description = self.app.get_input("Description (optional): ", required=False)
            
            # Assessment type selection
            assessment_types = ["peer_evaluation", "self_assessment", "instructor_review", "final_assessment"]
            type_choice = self.app.get_choice(
                "Select Assessment Type:",
                [t.replace('_', ' ').title() for t in assessment_types]
            )
            if type_choice == -1:
                return
            
            assessment_type = assessment_types[type_choice]
            
            # Batch selection
            batches = self.batch_service.get_all_batches()
            if not batches:
                self.app.print_error("No batches available. Please create a batch first.")
                self.app.wait_for_enter()
                return
            
            batch_options = [f"{b.name} ({b.code})" for b in batches]
            batch_choice = self.app.get_choice("Select Batch:", batch_options)
            if batch_choice == -1:
                return
            
            batch_id = batches[batch_choice].id
            
            # Dates
            start_date_input = self.app.get_input("Start Date (YYYY-MM-DD, optional): ", required=False)
            end_date_input = self.app.get_input("End Date (YYYY-MM-DD, optional): ", required=False)
            
            start_date = None
            end_date = None
            
            if start_date_input:
                try:
                    start_date = datetime.strptime(start_date_input, '%Y-%m-%d').date()
                except ValueError:
                    self.app.print_error("Invalid start date format. Use YYYY-MM-DD.")
                    self.app.wait_for_enter()
                    return
            
            if end_date_input:
                try:
                    end_date = datetime.strptime(end_date_input, '%Y-%m-%d').date()
                except ValueError:
                    self.app.print_error("Invalid end date format. Use YYYY-MM-DD.")
                    self.app.wait_for_enter()
                    return
            
            # Scoring configuration
            max_score_input = self.app.get_input("Maximum Score [100]: ", required=False)
            max_score = 100
            if max_score_input:
                try:
                    max_score = int(max_score_input)
                except ValueError:
                    self.app.print_error("Invalid maximum score.")
                    self.app.wait_for_enter()
                    return
            
            passing_score_input = self.app.get_input(f"Passing Score [{int(max_score * 0.6)}]: ", required=False)
            passing_score = int(max_score * 0.6)
            if passing_score_input:
                try:
                    passing_score = int(passing_score_input)
                except ValueError:
                    self.app.print_error("Invalid passing score.")
                    self.app.wait_for_enter()
                    return
            
            # Settings
            allow_self = self.app.confirm_action("Allow self evaluation?")
            anonymous = self.app.confirm_action("Anonymous feedback?")
            
            instructions = self.app.get_input("Instructions for students (optional): ", required=False)
            notes = self.app.get_input("Internal notes (optional): ", required=False)
            
            # Create assessment data
            assessment_data = {
                'title': title,
                'description': description if description else None,
                'assessment_type': assessment_type,
                'batch_id': batch_id,
                'start_date': start_date,
                'end_date': end_date,
                'max_score': max_score,
                'passing_score': passing_score,
                'allow_self_evaluation': allow_self,
                'anonymous_feedback': anonymous,
                'is_active': True,
                'instructions': instructions if instructions else None,
                'notes': notes if notes else None,
                'created_by_id': self.app.current_user.id
            }
            
            # Create assessment
            assessment = self.assessment_service.create_assessment(assessment_data)
            self.app.print_success(f"Assessment '{assessment.title}' created successfully!")
            
        except ValidationError as e:
            self.app.print_error(f"Validation error: {e}")
        except Exception as e:
            self.app.print_error(f"Failed to create assessment: {e}")
        
        self.app.wait_for_enter()
    
    def edit_assessment(self):
        """Edit an existing assessment."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}✏️ EDIT ASSESSMENT{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # Get assessment to edit
            assessment_id = self.app.get_input("Enter Assessment ID to edit: ")
            if not assessment_id:
                return
            
            try:
                assessment_id = int(assessment_id)
            except ValueError:
                self.app.print_error("Invalid Assessment ID.")
                self.app.wait_for_enter()
                return
            
            assessment = self.assessment_service.get_assessment_by_id(assessment_id)
            if not assessment:
                self.app.print_error("Assessment not found.")
                self.app.wait_for_enter()
                return
            
            print(f"\nEditing assessment: {assessment.title}")
            print("-" * 40)
            
            # Get updated information
            title = self.app.get_input(f"Title [{assessment.title}]: ", required=False)
            description = self.app.get_input(f"Description [{assessment.description or 'Not set'}]: ", required=False)
            
            # Dates
            start_date_input = self.app.get_input(
                f"Start Date [{assessment.start_date.strftime('%Y-%m-%d') if assessment.start_date else 'Not set'}]: ",
                required=False
            )
            end_date_input = self.app.get_input(
                f"End Date [{assessment.end_date.strftime('%Y-%m-%d') if assessment.end_date else 'Not set'}]: ",
                required=False
            )
            
            start_date = None
            end_date = None
            
            if start_date_input:
                try:
                    start_date = datetime.strptime(start_date_input, '%Y-%m-%d').date()
                except ValueError:
                    self.app.print_error("Invalid start date format. Use YYYY-MM-DD.")
                    self.app.wait_for_enter()
                    return
            
            if end_date_input:
                try:
                    end_date = datetime.strptime(end_date_input, '%Y-%m-%d').date()
                except ValueError:
                    self.app.print_error("Invalid end date format. Use YYYY-MM-DD.")
                    self.app.wait_for_enter()
                    return
            
            # Scoring
            max_score_input = self.app.get_input(f"Maximum Score [{assessment.max_score}]: ", required=False)
            max_score = None
            if max_score_input:
                try:
                    max_score = int(max_score_input)
                except ValueError:
                    self.app.print_error("Invalid maximum score.")
                    self.app.wait_for_enter()
                    return
            
            passing_score_input = self.app.get_input(f"Passing Score [{assessment.passing_score}]: ", required=False)
            passing_score = None
            if passing_score_input:
                try:
                    passing_score = int(passing_score_input)
                except ValueError:
                    self.app.print_error("Invalid passing score.")
                    self.app.wait_for_enter()
                    return
            
            # Status change
            status_options = ["Active", "Inactive", "Keep Current"]
            status_choice = self.app.get_choice(
                f"Status (current: {'Active' if assessment.is_active else 'Inactive'}):",
                status_options
            )
            
            new_status = None
            if status_choice == 0:
                new_status = True
            elif status_choice == 1:
                new_status = False
            
            instructions = self.app.get_input(f"Instructions [{assessment.instructions or 'Not set'}]: ", required=False)
            notes = self.app.get_input(f"Notes [{assessment.notes or 'Not set'}]: ", required=False)
            
            # Build update data
            update_data = {}
            if title:
                update_data['title'] = title
            if description:
                update_data['description'] = description
            if start_date is not None:
                update_data['start_date'] = start_date
            if end_date is not None:
                update_data['end_date'] = end_date
            if max_score is not None:
                update_data['max_score'] = max_score
            if passing_score is not None:
                update_data['passing_score'] = passing_score
            if new_status is not None:
                update_data['is_active'] = new_status
            if instructions:
                update_data['instructions'] = instructions
            if notes:
                update_data['notes'] = notes
            
            if not update_data:
                self.app.print_info("No changes made.")
                self.app.wait_for_enter()
                return
            
            # Confirm changes
            print(f"\nChanges to be made:")
            for key, value in update_data.items():
                print(f"  {key.replace('_', ' ').title()}: {value}")
            
            if self.app.confirm_action("Apply these changes?"):
                self.assessment_service.update_assessment(assessment_id, update_data)
                self.app.print_success("Assessment updated successfully!")
            else:
                self.app.print_info("Changes cancelled.")
            
        except ValidationError as e:
            self.app.print_error(f"Validation error: {e}")
        except Exception as e:
            self.app.print_error(f"Failed to edit assessment: {e}")
        
        self.app.wait_for_enter()
    
    def delete_assessment(self):
        """Delete an assessment."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}🗑️ DELETE ASSESSMENT{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # Get assessment to delete
            assessment_id = self.app.get_input("Enter Assessment ID to delete: ")
            if not assessment_id:
                return
            
            try:
                assessment_id = int(assessment_id)
            except ValueError:
                self.app.print_error("Invalid Assessment ID.")
                self.app.wait_for_enter()
                return
            
            assessment = self.assessment_service.get_assessment_by_id(assessment_id)
            if not assessment:
                self.app.print_error("Assessment not found.")
                self.app.wait_for_enter()
                return
            
            print(f"\nAssessment to delete:")
            print(f"  ID: {assessment.id}")
            print(f"  Title: {assessment.title}")
            print(f"  Type: {assessment.assessment_type.replace('_', ' ').title()}")
            print(f"  Batch: {assessment.batch.name if assessment.batch else 'No Batch'}")
            
            if self.app.confirm_action("Are you sure you want to delete this assessment?"):
                self.assessment_service.delete_assessment(assessment_id, soft_delete=True)
                self.app.print_success("Assessment deleted successfully!")
            else:
                self.app.print_info("Deletion cancelled.")
            
        except Exception as e:
            self.app.print_error(f"Failed to delete assessment: {e}")
        
        self.app.wait_for_enter()
    
    def view_assessment_results(self):
        """View assessment results."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}📊 ASSESSMENT RESULTS{self.app.CLIColors.ENDC}")
            print("-" * 40)
            
            # Get assessment
            assessment_id = self.app.get_input("Enter Assessment ID: ")
            if not assessment_id:
                return
            
            try:
                assessment_id = int(assessment_id)
            except ValueError:
                self.app.print_error("Invalid Assessment ID.")
                self.app.wait_for_enter()
                return
            
            assessment = self.assessment_service.get_assessment_by_id(assessment_id)
            if not assessment:
                self.app.print_error("Assessment not found.")
                self.app.wait_for_enter()
                return
            
            # Get assessment statistics
            stats = self.assessment_service.get_assessment_statistics(assessment_id)
            
            print(f"\nAssessment: {assessment.title}")
            print(f"Type: {assessment.assessment_type.replace('_', ' ').title()}")
            print(f"Batch: {assessment.batch.name if assessment.batch else 'No Batch'}")
            print("-" * 40)
            
            print(f"Total Students: {stats.get('total_students', 0)}")
            print(f"Total Evaluations: {stats.get('total_evaluations', 0)}")
            print(f"Unique Evaluators: {stats.get('unique_evaluators', 0)}")
            print(f"Participation Rate: {stats.get('participation_rate', 0):.1f}%")
            
            avg_scores = stats.get('average_scores', {})
            if avg_scores.get('overall', 0) > 0:
                print("\nAverage Scores:")
                print(f"  Overall: {avg_scores.get('overall', 0):.1f}")
                print(f"  Leadership: {avg_scores.get('leadership', 0):.1f}")
                print(f"  Teamwork: {avg_scores.get('teamwork', 0):.1f}")
                print(f"  Communication: {avg_scores.get('communication', 0):.1f}")
                print(f"  Technical: {avg_scores.get('technical', 0):.1f}")
                print(f"  Professionalism: {avg_scores.get('professionalism', 0):.1f}")
            
            # Get detailed results
            results_data = self.assessment_service.get_assessment_results_summary(assessment_id)
            student_results = results_data.get('student_results', [])
            
            if student_results:
                print(f"\nStudent Results:")
                print("-" * 40)
                
                headers = ["Student", "Overall", "Leadership", "Teamwork", "Communication", "Technical", "Professional", "Evals"]
                widths = [20, 8, 10, 8, 12, 9, 12, 6]
                
                self.app.print_table_header(headers, widths)
                
                for result in student_results:
                    values = [
                        result.get('student_name', 'Unknown'),
                        f"{result.get('overall_score', 0):.1f}",
                        f"{result.get('leadership_score', 0):.1f}",
                        f"{result.get('teamwork_score', 0):.1f}",
                        f"{result.get('communication_score', 0):.1f}",
                        f"{result.get('technical_score', 0):.1f}",
                        f"{result.get('professionalism_score', 0):.1f}",
                        str(result.get('evaluation_count', 0))
                    ]
                    self.app.print_table_row(values, widths)
            
        except Exception as e:
            self.app.print_error(f"Failed to view assessment results: {e}")
        
        self.app.wait_for_enter()
