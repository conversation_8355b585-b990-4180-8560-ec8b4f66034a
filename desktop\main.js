const { app, BrowserWindow, <PERSON>u, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const log = require('electron-log');

// Configure logging
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

class MilitaryPeerReviewApp {
    constructor() {
        this.mainWindow = null;
        this.pythonProcess = null;
        this.serverPort = 5000;
        this.serverUrl = `http://localhost:${this.serverPort}`;
        this.isQuitting = false;
        
        // Set app properties
        app.setName('Military Peer Review Assessment System');
        
        // Initialize app
        this.initializeApp();
    }
    
    initializeApp() {
        // Handle app ready
        app.whenReady().then(() => {
            this.createMainWindow();
            this.setupMenu();
            this.startPythonServer();
            
            app.on('activate', () => {
                if (BrowserWindow.getAllWindows().length === 0) {
                    this.createMainWindow();
                }
            });
        });
        
        // Handle app quit
        app.on('before-quit', (event) => {
            this.isQuitting = true;
            if (this.pythonProcess) {
                event.preventDefault();
                this.stopPythonServer().then(() => {
                    app.quit();
                });
            }
        });
        
        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });
        
        // Handle IPC messages
        this.setupIPC();
    }
    
    createMainWindow() {
        // Create the browser window
        this.mainWindow = new BrowserWindow({
            width: 1400,
            height: 900,
            minWidth: 1200,
            minHeight: 800,
            icon: this.getIconPath(),
            webPreferences: {
                nodeIntegration: false,
                contextIsolation: true,
                enableRemoteModule: false,
                preload: path.join(__dirname, 'preload.js')
            },
            show: false,
            titleBarStyle: 'default',
            autoHideMenuBar: false
        });
        
        // Set window title
        this.mainWindow.setTitle('Military Peer Review Assessment System');
        
        // Load the loading page first
        this.mainWindow.loadFile(path.join(__dirname, 'loading.html'));
        
        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();
            
            // Focus the window
            if (process.platform === 'darwin') {
                app.dock.show();
            }
            this.mainWindow.focus();
        });
        
        // Handle window closed
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });
        
        // Handle external links
        this.mainWindow.webContents.setWindowOpenHandler(({ url }) => {
            shell.openExternal(url);
            return { action: 'deny' };
        });
        
        // Prevent navigation to external sites
        this.mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
            const parsedUrl = new URL(navigationUrl);
            
            if (parsedUrl.origin !== this.serverUrl) {
                event.preventDefault();
            }
        });
        
        // Development tools
        if (process.env.NODE_ENV === 'development') {
            this.mainWindow.webContents.openDevTools();
        }
    }
    
    getIconPath() {
        const iconName = process.platform === 'win32' ? 'icon.ico' : 
                        process.platform === 'darwin' ? 'icon.icns' : 'icon.png';
        return path.join(__dirname, 'assets', iconName);
    }
    
    setupMenu() {
        const template = [
            {
                label: 'File',
                submenu: [
                    {
                        label: 'Refresh',
                        accelerator: 'CmdOrCtrl+R',
                        click: () => {
                            if (this.mainWindow) {
                                this.mainWindow.reload();
                            }
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'Exit',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: 'View',
                submenu: [
                    { role: 'reload' },
                    { role: 'forceReload' },
                    { role: 'toggleDevTools' },
                    { type: 'separator' },
                    { role: 'resetZoom' },
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' }
                ]
            },
            {
                label: 'Window',
                submenu: [
                    { role: 'minimize' },
                    { role: 'close' }
                ]
            },
            {
                label: 'Help',
                submenu: [
                    {
                        label: 'About',
                        click: () => {
                            this.showAboutDialog();
                        }
                    },
                    {
                        label: 'User Manual',
                        click: () => {
                            shell.openExternal('https://github.com/your-repo/wiki');
                        }
                    }
                ]
            }
        ];
        
        if (process.platform === 'darwin') {
            template.unshift({
                label: app.getName(),
                submenu: [
                    { role: 'about' },
                    { type: 'separator' },
                    { role: 'services' },
                    { type: 'separator' },
                    { role: 'hide' },
                    { role: 'hideOthers' },
                    { role: 'unhide' },
                    { type: 'separator' },
                    { role: 'quit' }
                ]
            });
        }
        
        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }
    
    setupIPC() {
        ipcMain.handle('get-app-version', () => {
            return app.getVersion();
        });
        
        ipcMain.handle('get-server-status', () => {
            return {
                running: this.pythonProcess !== null,
                url: this.serverUrl
            };
        });
        
        ipcMain.handle('restart-server', async () => {
            try {
                await this.stopPythonServer();
                await this.startPythonServer();
                return { success: true };
            } catch (error) {
                log.error('Failed to restart server:', error);
                return { success: false, error: error.message };
            }
        });
    }
    
    async startPythonServer() {
        return new Promise((resolve, reject) => {
            try {
                log.info('Starting Python server...');
                
                // Determine Python executable path
                const pythonPath = this.getPythonPath();
                const scriptPath = path.join(__dirname, '..', 'src', 'web_app.py');
                
                // Start Python process
                this.pythonProcess = spawn(pythonPath, [scriptPath], {
                    cwd: path.join(__dirname, '..'),
                    stdio: ['pipe', 'pipe', 'pipe']
                });
                
                // Handle process output
                this.pythonProcess.stdout.on('data', (data) => {
                    const output = data.toString();
                    log.info('Python stdout:', output);
                    
                    // Check if server is ready
                    if (output.includes('Running on') || output.includes('Access the application')) {
                        log.info('Python server started successfully');
                        setTimeout(() => {
                            this.loadMainApplication();
                        }, 2000);
                        resolve();
                    }
                });
                
                this.pythonProcess.stderr.on('data', (data) => {
                    const error = data.toString();
                    log.error('Python stderr:', error);
                });
                
                this.pythonProcess.on('close', (code) => {
                    log.info(`Python process exited with code ${code}`);
                    this.pythonProcess = null;
                    
                    if (!this.isQuitting) {
                        this.showServerError();
                    }
                });
                
                this.pythonProcess.on('error', (error) => {
                    log.error('Failed to start Python process:', error);
                    this.pythonProcess = null;
                    reject(error);
                });
                
            } catch (error) {
                log.error('Error starting Python server:', error);
                reject(error);
            }
        });
    }
    
    async stopPythonServer() {
        return new Promise((resolve) => {
            if (this.pythonProcess) {
                log.info('Stopping Python server...');
                
                this.pythonProcess.on('close', () => {
                    log.info('Python server stopped');
                    this.pythonProcess = null;
                    resolve();
                });
                
                // Try graceful shutdown first
                this.pythonProcess.kill('SIGTERM');
                
                // Force kill after timeout
                setTimeout(() => {
                    if (this.pythonProcess) {
                        this.pythonProcess.kill('SIGKILL');
                        this.pythonProcess = null;
                        resolve();
                    }
                }, 5000);
            } else {
                resolve();
            }
        });
    }
    
    getPythonPath() {
        // Try to find Python executable
        const possiblePaths = [
            'python',
            'python3',
            path.join(process.resourcesPath, 'python-runtime', 'python.exe'),
            path.join(__dirname, '..', 'python-runtime', 'python.exe')
        ];
        
        // For now, use system Python
        return process.platform === 'win32' ? 'python' : 'python3';
    }
    
    loadMainApplication() {
        if (this.mainWindow) {
            log.info('Loading main application...');
            this.mainWindow.loadURL(this.serverUrl);
        }
    }
    
    showServerError() {
        if (this.mainWindow) {
            dialog.showErrorBox(
                'Server Error',
                'The Python server has stopped unexpectedly. Please restart the application.'
            );
        }
    }
    
    showAboutDialog() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'About Military Peer Review Assessment System',
            message: 'Military Peer Review Assessment System',
            detail: `Version: ${app.getVersion()}\n\nAuthor: Maj. Sachin Kumar Singh\nDeveloper: Hrishikesh Mohite\nCompany: Ajinkyacreatiion PVT. LTD.\n\nA comprehensive peer evaluation system for military training programs.`,
            buttons: ['OK']
        });
    }
}

// Create and start the application
new MilitaryPeerReviewApp();
