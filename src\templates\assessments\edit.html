{% extends "base.html" %}

{% block title %}Edit Assessment - Military Peer Review Assessment System{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">
                        <i class="bi bi-pencil-square me-2 text-primary"></i>Edit Assessment
                    </h1>
                    <p class="text-muted mb-0">Update assessment information and settings</p>
                </div>
                <div>
                    <a href="{{ url_for('assessment_details', assessment_id=assessment.id) }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Assessment
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Assessment Form -->
    <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-form me-2 text-primary"></i>Assessment Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('assessments_edit', assessment_id=assessment.id) }}" novalidate>
                        <div class="row g-3">
                            <!-- Basic Information -->
                            <div class="col-12">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-info-circle me-2"></i>Basic Information
                                </h6>
                            </div>

                            <!-- Assessment Title -->
                            <div class="col-md-8">
                                <label for="title" class="form-label fw-semibold">
                                    <i class="bi bi-card-text me-2"></i>Assessment Title <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="title" 
                                       name="title" 
                                       value="{{ assessment.title }}"
                                       placeholder="Enter assessment title"
                                       required>
                            </div>

                            <!-- Assessment Type (Read-only) -->
                            <div class="col-md-4">
                                <label for="assessment_type" class="form-label fw-semibold">
                                    <i class="bi bi-tag me-2"></i>Assessment Type
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="assessment_type" 
                                       value="{{ assessment.assessment_type.replace('_', ' ').title() }}"
                                       readonly>
                                <div class="form-text">Assessment type cannot be changed after creation.</div>
                            </div>

                            <!-- Description -->
                            <div class="col-12">
                                <label for="description" class="form-label fw-semibold">
                                    <i class="bi bi-text-paragraph me-2"></i>Description
                                </label>
                                <textarea class="form-control" 
                                          id="description" 
                                          name="description" 
                                          rows="3" 
                                          placeholder="Enter assessment description">{{ assessment.description or '' }}</textarea>
                            </div>

                            <!-- Batch Information (Read-only) -->
                            <div class="col-12 mt-4">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-people me-2"></i>Target Batch
                                </h6>
                            </div>

                            <div class="col-md-6">
                                <label for="batch_name" class="form-label fw-semibold">
                                    <i class="bi bi-collection me-2"></i>Batch
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="batch_name" 
                                       value="{{ assessment.batch.name if assessment.batch else 'No Batch Assigned' }}"
                                       readonly>
                                <div class="form-text">Batch assignment cannot be changed after creation.</div>
                            </div>

                            <!-- Schedule -->
                            <div class="col-12 mt-4">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-calendar me-2"></i>Schedule
                                </h6>
                            </div>

                            <!-- Start Date -->
                            <div class="col-md-6">
                                <label for="start_date" class="form-label fw-semibold">
                                    <i class="bi bi-calendar-event me-2"></i>Start Date
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="start_date" 
                                       name="start_date"
                                       value="{{ assessment.start_date.strftime('%Y-%m-%d') if assessment.start_date else '' }}">
                            </div>

                            <!-- End Date -->
                            <div class="col-md-6">
                                <label for="end_date" class="form-label fw-semibold">
                                    <i class="bi bi-calendar-check me-2"></i>End Date
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="end_date" 
                                       name="end_date"
                                       value="{{ assessment.end_date.strftime('%Y-%m-%d') if assessment.end_date else '' }}">
                            </div>

                            <!-- Scoring Configuration -->
                            <div class="col-12 mt-4">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-award me-2"></i>Scoring Configuration
                                </h6>
                            </div>

                            <!-- Max Score -->
                            <div class="col-md-6">
                                <label for="max_score" class="form-label fw-semibold">
                                    <i class="bi bi-trophy me-2"></i>Maximum Score
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="max_score" 
                                       name="max_score" 
                                       value="{{ assessment.max_score }}"
                                       min="1"
                                       max="1000">
                            </div>

                            <!-- Passing Score -->
                            <div class="col-md-6">
                                <label for="passing_score" class="form-label fw-semibold">
                                    <i class="bi bi-check-circle me-2"></i>Passing Score
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="passing_score" 
                                       name="passing_score" 
                                       value="{{ assessment.passing_score }}"
                                       min="1"
                                       max="1000">
                            </div>

                            <!-- Assessment Settings -->
                            <div class="col-12 mt-4">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-gear me-2"></i>Assessment Settings
                                </h6>
                            </div>

                            <!-- Settings Checkboxes -->
                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="allow_self_evaluation" 
                                           name="allow_self_evaluation"
                                           {% if assessment.allow_self_evaluation %}checked{% endif %}>
                                    <label class="form-check-label fw-semibold" for="allow_self_evaluation">
                                        <i class="bi bi-person-check me-2"></i>Allow Self Evaluation
                                    </label>
                                    <div class="form-text">Allow students to evaluate themselves</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="anonymous_feedback" 
                                           name="anonymous_feedback"
                                           {% if assessment.anonymous_feedback %}checked{% endif %}>
                                    <label class="form-check-label fw-semibold" for="anonymous_feedback">
                                        <i class="bi bi-eye-slash me-2"></i>Anonymous Feedback
                                    </label>
                                    <div class="form-text">Keep evaluator identities anonymous</div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active"
                                           {% if assessment.is_active %}checked{% endif %}>
                                    <label class="form-check-label fw-semibold" for="is_active">
                                        <i class="bi bi-check-circle me-2"></i>Active Assessment
                                    </label>
                                    <div class="form-text">Make assessment available to students</div>
                                </div>
                            </div>

                            <!-- Instructions -->
                            <div class="col-12">
                                <label for="instructions" class="form-label fw-semibold">
                                    <i class="bi bi-list-ul me-2"></i>Instructions for Students
                                </label>
                                <textarea class="form-control" 
                                          id="instructions" 
                                          name="instructions" 
                                          rows="4" 
                                          placeholder="Enter detailed instructions for students on how to complete this assessment">{{ assessment.instructions or '' }}</textarea>
                            </div>

                            <!-- Notes -->
                            <div class="col-12">
                                <label for="notes" class="form-label fw-semibold">
                                    <i class="bi bi-sticky me-2"></i>Internal Notes
                                </label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3" 
                                          placeholder="Internal notes for instructors (not visible to students)">{{ assessment.notes or '' }}</textarea>
                            </div>

                            <!-- Assessment Information Display -->
                            <div class="col-12">
                                <hr class="my-4">
                                <h6 class="fw-semibold mb-3">
                                    <i class="bi bi-info-circle me-2"></i>Assessment Information
                                </h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">Created</small>
                                        <span class="fw-semibold">{{ assessment.created_at.strftime('%Y-%m-%d %H:%M') if assessment.created_at else 'N/A' }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">Last Modified</small>
                                        <span class="fw-semibold">{{ assessment.modified_at.strftime('%Y-%m-%d %H:%M') if assessment.modified_at else 'N/A' }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">Created By</small>
                                        <span class="fw-semibold">{{ assessment.created_by.full_name if assessment.created_by else 'Unknown' }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">Assessment ID</small>
                                        <span class="fw-semibold">#{{ assessment.id }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-3 justify-content-end">
                                    <a href="{{ url_for('assessment_details', assessment_id=assessment.id) }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Update Assessment
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Form validation
    (function() {
        'use strict';
        
        const form = document.querySelector('form');
        
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    })();

    // Auto-focus title field
    document.getElementById('title').focus();

    // Validate end date is after start date
    function validateDates() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const endDateField = document.getElementById('end_date');
        
        if (startDate && endDate && new Date(endDate) <= new Date(startDate)) {
            endDateField.setCustomValidity('End date must be after start date');
        } else {
            endDateField.setCustomValidity('');
        }
    }

    document.getElementById('start_date').addEventListener('change', validateDates);
    document.getElementById('end_date').addEventListener('change', validateDates);

    // Update passing score when max score changes
    document.getElementById('max_score').addEventListener('change', function() {
        const maxScore = parseInt(this.value);
        const passingScoreField = document.getElementById('passing_score');
        const currentPassing = parseInt(passingScoreField.value);
        
        if (currentPassing > maxScore) {
            passingScoreField.value = Math.floor(maxScore * 0.6); // 60% of max score
        }
        
        passingScoreField.max = maxScore;
    });
</script>
{% endblock %}
