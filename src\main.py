#!/usr/bin/env python3
"""
Military Peer Review Assessment System
Main application entry point

Author: Maj<PERSON> <PERSON><PERSON>
Developer: Hrishikesh Mohite
Company: Ajinkyacreatiion PVT. LTD.
"""

import sys
import os
import logging
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt, QDir
from PySide6.QtGui import QIcon

# Add src directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.settings import AppSettings
from config.database import DatabaseManager
from ui.main_window import MainWindow
from core.exceptions import DatabaseError, AuthenticationError


def setup_logging():
    """Configure application logging."""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "app.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )


def setup_application():
    """Initialize the Qt application with proper settings."""
    app = QApplication(sys.argv)

    # Set application properties
    app.setApplicationName("Military Peer Review System")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Ajinkyacreatiion PVT. LTD.")
    app.setOrganizationDomain("ajinkyacreatiion.com")

    # Set application icon
    icon_path = Path("resources/icons/app_icon.png")
    if icon_path.exists():
        app.setWindowIcon(QIcon(str(icon_path)))

    # Enable high DPI scaling (Qt 6 handles this automatically)
    # These attributes are deprecated in Qt 6 and handled automatically

    # Apply application-wide styling
    try:
        from utils.style_manager import style_manager
        style_manager.apply_theme(app, "main")
    except Exception as e:
        logging.warning(f"Failed to apply theme: {e}")

    return app


def initialize_database():
    """Initialize the database and run migrations."""
    try:
        db_manager = DatabaseManager()
        db_manager.initialize()
        logging.info("Database initialized successfully")
        return True
    except DatabaseError as e:
        logging.error(f"Database initialization failed: {e}")
        QMessageBox.critical(
            None,
            "Database Error",
            f"Failed to initialize database:\n{e}\n\nPlease check the logs for more details."
        )
        return False


def main():
    """Main application entry point."""
    # Setup logging first
    setup_logging()
    logging.info("Starting Military Peer Review Assessment System")

    try:
        # Initialize Qt application
        app = setup_application()

        # Initialize settings
        settings = AppSettings()

        # Initialize database
        if not initialize_database():
            return 1

        # Create and show main window
        main_window = MainWindow()
        main_window.show()

        logging.info("Application started successfully")

        # Start the event loop
        return app.exec()

    except Exception as e:
        logging.error(f"Critical error during startup: {e}", exc_info=True)

        # Show error dialog if possible
        try:
            QMessageBox.critical(
                None,
                "Critical Error",
                f"A critical error occurred during startup:\n{e}\n\nPlease check the logs for more details."
            )
        except:
            print(f"Critical error: {e}")

        return 1


if __name__ == "__main__":
    sys.exit(main())
