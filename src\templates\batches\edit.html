{% extends "base.html" %}

{% block title %}Edit Batch - Military Peer Review Assessment System{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">
                        <i class="bi bi-pencil-square me-2 text-primary"></i>Edit Batch
                    </h1>
                    <p class="text-muted mb-0">Update batch information and settings</p>
                </div>
                <div>
                    <a href="{{ url_for('batches_list') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Batches
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Batch Form -->
    <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-form me-2 text-primary"></i>Batch Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('batches_edit', batch_id=batch.id) }}" novalidate>
                        <div class="row g-3">
                            <!-- Basic Information -->
                            <div class="col-12">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-info-circle me-2"></i>Basic Information
                                </h6>
                            </div>

                            <!-- Batch Name -->
                            <div class="col-md-6">
                                <label for="name" class="form-label fw-semibold">
                                    <i class="bi bi-collection me-2"></i>Batch Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="name" 
                                       name="name" 
                                       value="{{ batch.name }}"
                                       placeholder="Enter batch name"
                                       required>
                            </div>

                            <!-- Batch Code (Read-only) -->
                            <div class="col-md-6">
                                <label for="code" class="form-label fw-semibold">
                                    <i class="bi bi-tag me-2"></i>Batch Code
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="code" 
                                       value="{{ batch.code }}"
                                       readonly>
                                <div class="form-text">Batch code cannot be changed after creation.</div>
                            </div>

                            <!-- Description -->
                            <div class="col-12">
                                <label for="description" class="form-label fw-semibold">
                                    <i class="bi bi-text-paragraph me-2"></i>Description
                                </label>
                                <textarea class="form-control" 
                                          id="description" 
                                          name="description" 
                                          rows="3" 
                                          placeholder="Enter batch description">{{ batch.description or '' }}</textarea>
                            </div>

                            <!-- Academic Information -->
                            <div class="col-12 mt-4">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-calendar-academic me-2"></i>Academic Information
                                </h6>
                            </div>

                            <!-- Academic Year -->
                            <div class="col-md-4">
                                <label for="academic_year" class="form-label fw-semibold">
                                    <i class="bi bi-calendar me-2"></i>Academic Year <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="academic_year" 
                                       name="academic_year" 
                                       value="{{ batch.academic_year }}"
                                       required>
                            </div>

                            <!-- Semester -->
                            <div class="col-md-4">
                                <label for="semester" class="form-label fw-semibold">
                                    <i class="bi bi-calendar2 me-2"></i>Semester <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="semester" name="semester" required>
                                    <option value="">Select semester</option>
                                    <option value="1" {% if batch.semester == '1' %}selected{% endif %}>Semester 1</option>
                                    <option value="2" {% if batch.semester == '2' %}selected{% endif %}>Semester 2</option>
                                    <option value="Summer" {% if batch.semester == 'Summer' %}selected{% endif %}>Summer</option>
                                    <option value="Winter" {% if batch.semester == 'Winter' %}selected{% endif %}>Winter</option>
                                </select>
                            </div>

                            <!-- Max Students -->
                            <div class="col-md-4">
                                <label for="max_students" class="form-label fw-semibold">
                                    <i class="bi bi-people me-2"></i>Max Students
                                </label>
                                <input type="number" 
                                       class="form-control" 
                                       id="max_students" 
                                       name="max_students" 
                                       value="{{ batch.max_students }}"
                                       min="1"
                                       max="200">
                            </div>

                            <!-- Course Information -->
                            <div class="col-12 mt-4">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-book me-2"></i>Course Information
                                </h6>
                            </div>

                            <!-- Course Name -->
                            <div class="col-md-6">
                                <label for="course_name" class="form-label fw-semibold">
                                    <i class="bi bi-book-half me-2"></i>Course Name
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="course_name" 
                                       name="course_name" 
                                       value="{{ batch.course_name or '' }}"
                                       placeholder="Enter course name">
                            </div>

                            <!-- Course Code -->
                            <div class="col-md-6">
                                <label for="course_code" class="form-label fw-semibold">
                                    <i class="bi bi-hash me-2"></i>Course Code
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="course_code" 
                                       name="course_code" 
                                       value="{{ batch.course_code or '' }}"
                                       placeholder="Enter course code">
                            </div>

                            <!-- Instructor -->
                            <div class="col-md-6">
                                <label for="instructor_id" class="form-label fw-semibold">
                                    <i class="bi bi-person-workspace me-2"></i>Instructor
                                </label>
                                <select class="form-select" id="instructor_id" name="instructor_id">
                                    <option value="">Select instructor</option>
                                    {% for teacher in teachers %}
                                    <option value="{{ teacher.id }}" {% if batch.instructor_id == teacher.id %}selected{% endif %}>
                                        {{ teacher.full_name }} ({{ teacher.username }})
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>

                            <!-- Duration -->
                            <div class="col-12 mt-4">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-clock me-2"></i>Duration
                                </h6>
                            </div>

                            <!-- Start Date -->
                            <div class="col-md-6">
                                <label for="start_date" class="form-label fw-semibold">
                                    <i class="bi bi-calendar-event me-2"></i>Start Date
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="start_date" 
                                       name="start_date"
                                       value="{{ batch.start_date.strftime('%Y-%m-%d') if batch.start_date else '' }}">
                            </div>

                            <!-- End Date -->
                            <div class="col-md-6">
                                <label for="end_date" class="form-label fw-semibold">
                                    <i class="bi bi-calendar-check me-2"></i>End Date
                                </label>
                                <input type="date" 
                                       class="form-control" 
                                       id="end_date" 
                                       name="end_date"
                                       value="{{ batch.end_date.strftime('%Y-%m-%d') if batch.end_date else '' }}">
                            </div>

                            <!-- Settings -->
                            <div class="col-12 mt-4">
                                <h6 class="fw-semibold text-primary mb-3">
                                    <i class="bi bi-gear me-2"></i>Settings
                                </h6>
                            </div>

                            <!-- Active Status -->
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active" 
                                           {% if batch.is_active %}checked{% endif %}>
                                    <label class="form-check-label fw-semibold" for="is_active">
                                        <i class="bi bi-check-circle me-2"></i>Active Batch
                                    </label>
                                    <div class="form-text">Inactive batches are not visible to students and cannot be used for assessments.</div>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="col-12">
                                <label for="notes" class="form-label fw-semibold">
                                    <i class="bi bi-sticky me-2"></i>Notes
                                </label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3" 
                                          placeholder="Additional notes about this batch">{{ batch.notes or '' }}</textarea>
                            </div>

                            <!-- Batch Information Display -->
                            <div class="col-12">
                                <hr class="my-4">
                                <h6 class="fw-semibold mb-3">
                                    <i class="bi bi-info-circle me-2"></i>Batch Information
                                </h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">Created</small>
                                        <span class="fw-semibold">{{ batch.created_at.strftime('%Y-%m-%d %H:%M') if batch.created_at else 'N/A' }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">Last Modified</small>
                                        <span class="fw-semibold">{{ batch.modified_at.strftime('%Y-%m-%d %H:%M') if batch.modified_at else 'N/A' }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">Enrolled Students</small>
                                        <span class="fw-semibold">{{ batch.students|length if batch.students else 0 }} / {{ batch.max_students }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">Status</small>
                                        {% if batch.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-3 justify-content-end">
                                    <a href="{{ url_for('batches_list') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Update Batch
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Form validation
    (function() {
        'use strict';
        
        const form = document.querySelector('form');
        
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    })();

    // Auto-focus batch name field
    document.getElementById('name').focus();

    // Validate end date is after start date
    function validateDates() {
        const startDate = document.getElementById('start_date').value;
        const endDate = document.getElementById('end_date').value;
        const endDateField = document.getElementById('end_date');
        
        if (startDate && endDate && new Date(endDate) <= new Date(startDate)) {
            endDateField.setCustomValidity('End date must be after start date');
        } else {
            endDateField.setCustomValidity('');
        }
    }

    document.getElementById('start_date').addEventListener('change', validateDates);
    document.getElementById('end_date').addEventListener('change', validateDates);
</script>
{% endblock %}
