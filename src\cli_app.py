#!/usr/bin/env python3
"""
Military Peer Review Assessment System - Command Line Interface

A comprehensive CLI application for managing peer review assessments
in military training environments.

Author: Maj<PERSON> <PERSON><PERSON>
Developer: Hrishikesh Mohite
Company: Ajinkyacreatiion PVT. LTD.
"""

import os
import sys
import logging
from datetime import datetime
from typing import Optional, Dict, Any, List
import getpass
import json

# Add the src directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from config.database import DatabaseManager
from database.connection import db_connection
from database.migrations import initialize_database
from services.user_service import UserService
from services.batch_service import BatchService
from services.assessment_service import AssessmentService
from services.reporting_service import ReportingService
from core.exceptions import ValidationError, DatabaseError, AuthenticationError
from models.user import User

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/cli_app.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class CLIColors:
    """ANSI color codes for terminal output."""
    HEADER = '\033[95m'
    OKBLUE = '\033[94m'
    OKCYAN = '\033[96m'
    OKGREEN = '\033[92m'
    WARNING = '\033[93m'
    FAIL = '\033[91m'
    ENDC = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

class MilitaryPeerReviewCLI:
    """Main CLI application class."""

    def __init__(self):
        self.current_user: Optional[User] = None
        self.user_service = UserService()
        self.batch_service = BatchService()
        self.assessment_service = AssessmentService()
        self.reporting_service = ReportingService()
        self.running = True

        # Initialize logging
        self.logger = logging.getLogger(__name__)

        # Create logs directory if it doesn't exist
        os.makedirs('logs', exist_ok=True)

    def print_header(self):
        """Print application header."""
        print(f"\n{CLIColors.HEADER}{CLIColors.BOLD}")
        print("=" * 80)
        print("🎖️  MILITARY PEER REVIEW ASSESSMENT SYSTEM")
        print("=" * 80)
        print(f"Author: Maj. Sachin Kumar Singh")
        print(f"Developer: Hrishikesh Mohite")
        print(f"Company: Ajinkyacreatiion PVT. LTD.")
        print("=" * 80)
        print(f"{CLIColors.ENDC}")

    def print_success(self, message: str):
        """Print success message."""
        print(f"{CLIColors.OKGREEN}✓ {message}{CLIColors.ENDC}")

    def print_error(self, message: str):
        """Print error message."""
        print(f"{CLIColors.FAIL}✗ {message}{CLIColors.ENDC}")

    def print_warning(self, message: str):
        """Print warning message."""
        print(f"{CLIColors.WARNING}⚠ {message}{CLIColors.ENDC}")

    def print_info(self, message: str):
        """Print info message."""
        print(f"{CLIColors.OKBLUE}ℹ {message}{CLIColors.ENDC}")

    def print_table_header(self, headers: List[str], widths: List[int]):
        """Print formatted table header."""
        print(f"\n{CLIColors.BOLD}", end="")
        for i, (header, width) in enumerate(zip(headers, widths)):
            print(f"{header:<{width}}", end=" | " if i < len(headers) - 1 else "")
        print(f"{CLIColors.ENDC}")
        print("-" * (sum(widths) + 3 * (len(widths) - 1)))

    def print_table_row(self, values: List[str], widths: List[int]):
        """Print formatted table row."""
        for i, (value, width) in enumerate(zip(values, widths)):
            # Truncate long values
            display_value = value[:width-2] + ".." if len(value) > width else value
            print(f"{display_value:<{width}}", end=" | " if i < len(values) - 1 else "")
        print()

    def get_input(self, prompt: str, required: bool = True) -> str:
        """Get user input with validation."""
        while True:
            try:
                value = input(f"{CLIColors.OKCYAN}{prompt}{CLIColors.ENDC}").strip()
                if required and not value:
                    self.print_error("This field is required. Please enter a value.")
                    continue
                return value
            except KeyboardInterrupt:
                print(f"\n{CLIColors.WARNING}Operation cancelled.{CLIColors.ENDC}")
                return ""
            except EOFError:
                print(f"\n{CLIColors.WARNING}Input terminated.{CLIColors.ENDC}")
                return ""

    def get_password(self, prompt: str) -> str:
        """Get password input securely."""
        try:
            return getpass.getpass(f"{CLIColors.OKCYAN}{prompt}{CLIColors.ENDC}")
        except KeyboardInterrupt:
            print(f"\n{CLIColors.WARNING}Operation cancelled.{CLIColors.ENDC}")
            return ""

    def get_choice(self, prompt: str, choices: List[str]) -> int:
        """Get user choice from a list of options."""
        while True:
            try:
                print(f"\n{CLIColors.OKCYAN}{prompt}{CLIColors.ENDC}")
                for i, choice in enumerate(choices, 1):
                    print(f"{i}. {choice}")

                choice_input = input(f"{CLIColors.OKCYAN}Enter your choice (1-{len(choices)}): {CLIColors.ENDC}").strip()

                if not choice_input:
                    continue

                choice_num = int(choice_input)
                if 1 <= choice_num <= len(choices):
                    return choice_num - 1
                else:
                    self.print_error(f"Please enter a number between 1 and {len(choices)}")
            except ValueError:
                self.print_error("Please enter a valid number")
            except KeyboardInterrupt:
                print(f"\n{CLIColors.WARNING}Operation cancelled.{CLIColors.ENDC}")
                return -1

    def confirm_action(self, message: str) -> bool:
        """Get user confirmation for an action."""
        while True:
            try:
                response = input(f"{CLIColors.WARNING}{message} (y/N): {CLIColors.ENDC}").strip().lower()
                if response in ['y', 'yes']:
                    return True
                elif response in ['n', 'no', '']:
                    return False
                else:
                    self.print_error("Please enter 'y' for yes or 'n' for no")
            except KeyboardInterrupt:
                print(f"\n{CLIColors.WARNING}Operation cancelled.{CLIColors.ENDC}")
                return False

    def wait_for_enter(self, message: str = "Press Enter to continue..."):
        """Wait for user to press Enter."""
        try:
            input(f"\n{CLIColors.OKCYAN}{message}{CLIColors.ENDC}")
        except KeyboardInterrupt:
            pass

    def clear_screen(self):
        """Clear the terminal screen."""
        os.system('cls' if os.name == 'nt' else 'clear')

    def initialize_system(self) -> bool:
        """Initialize the database and system."""
        try:
            self.print_info("Initializing Military Peer Review Assessment System...")

            # Initialize database
            db_manager = DatabaseManager()
            db_manager.initialize()

            # Run migrations
            initialize_database()

            self.print_success("System initialized successfully!")
            return True

        except Exception as e:
            self.print_error(f"Failed to initialize system: {e}")
            self.logger.error(f"System initialization error: {e}")
            return False

    def authenticate_user(self) -> bool:
        """Handle user authentication."""
        max_attempts = 3
        attempts = 0

        while attempts < max_attempts:
            try:
                print(f"\n{CLIColors.BOLD}🔐 USER AUTHENTICATION{CLIColors.ENDC}")
                print("-" * 40)

                username = self.get_input("Username: ")
                if not username:
                    return False

                password = self.get_password("Password: ")
                if not password:
                    return False

                # Authenticate user
                user = self.user_service.authenticate_user(username, password)
                if user:
                    self.current_user = user
                    self.print_success(f"Welcome, {user.full_name}!")
                    self.print_info(f"Role: {user.role.replace('_', ' ').title()}")

                    # Update last login
                    self.user_service.update_last_login(user.id)

                    return True
                else:
                    attempts += 1
                    remaining = max_attempts - attempts
                    if remaining > 0:
                        self.print_error(f"Invalid credentials. {remaining} attempts remaining.")
                    else:
                        self.print_error("Maximum login attempts exceeded.")

            except AuthenticationError as e:
                attempts += 1
                remaining = max_attempts - attempts
                if remaining > 0:
                    self.print_error(f"Authentication failed: {e}. {remaining} attempts remaining.")
                else:
                    self.print_error("Maximum login attempts exceeded.")
            except Exception as e:
                self.print_error(f"Login error: {e}")
                return False

        return False

    def show_main_menu(self):
        """Display the main menu."""
        while self.running:
            try:
                self.clear_screen()
                self.print_header()

                if self.current_user:
                    print(f"{CLIColors.OKGREEN}Logged in as: {self.current_user.full_name} ({self.current_user.role.replace('_', ' ').title()}){CLIColors.ENDC}")

                print(f"\n{CLIColors.BOLD}📋 MAIN MENU{CLIColors.ENDC}")
                print("-" * 40)

                menu_options = []

                # Role-based menu options
                if self.current_user.role in ['super_admin', 'admin']:
                    menu_options.extend([
                        "👥 User Management",
                        "📚 Batch Management",
                        "📝 Assessment Management",
                        "📊 Reports & Analytics"
                    ])
                elif self.current_user.role == 'teacher':
                    menu_options.extend([
                        "📚 Batch Management",
                        "📝 Assessment Management",
                        "📊 Reports & Analytics"
                    ])
                elif self.current_user.role == 'student':
                    menu_options.extend([
                        "📝 My Assessments",
                        "📊 My Performance"
                    ])

                menu_options.extend([
                    "⚙️ Settings",
                    "🚪 Logout"
                ])

                choice = self.get_choice("Select an option:", menu_options)

                if choice == -1:  # Cancelled
                    continue

                selected_option = menu_options[choice]

                if "User Management" in selected_option:
                    from cli.user_management import UserManagementCLI
                    user_cli = UserManagementCLI(self)
                    user_cli.show_menu()
                elif "Batch Management" in selected_option:
                    from cli.batch_management import BatchManagementCLI
                    batch_cli = BatchManagementCLI(self)
                    batch_cli.show_menu()
                elif "Assessment Management" in selected_option:
                    from cli.assessment_management import AssessmentManagementCLI
                    assessment_cli = AssessmentManagementCLI(self)
                    assessment_cli.show_menu()
                elif "My Assessments" in selected_option:
                    from cli.student_assessments import StudentAssessmentsCLI
                    student_cli = StudentAssessmentsCLI(self)
                    student_cli.show_menu()
                elif "Reports" in selected_option or "My Performance" in selected_option:
                    from cli.reports import ReportsCLI
                    reports_cli = ReportsCLI(self)
                    reports_cli.show_menu()
                elif "Settings" in selected_option:
                    self.show_settings_menu()
                elif "Logout" in selected_option:
                    if self.confirm_action("Are you sure you want to logout?"):
                        self.current_user = None
                        self.print_success("Logged out successfully!")
                        break

            except KeyboardInterrupt:
                if self.confirm_action("\nAre you sure you want to exit the application?"):
                    self.running = False
                    break
            except Exception as e:
                self.print_error(f"An error occurred: {e}")
                self.logger.error(f"Main menu error: {e}")
                self.wait_for_enter()

    def show_settings_menu(self):
        """Show settings menu."""
        while True:
            self.clear_screen()
            print(f"\n{CLIColors.BOLD}⚙️ SETTINGS{CLIColors.ENDC}")
            print("-" * 40)

            settings_options = [
                "🔑 Change Password",
                "👤 Update Profile",
                "📊 System Information",
                "🔙 Back to Main Menu"
            ]

            choice = self.get_choice("Select an option:", settings_options)

            if choice == -1 or choice == 3:  # Back or cancelled
                break
            elif choice == 0:  # Change Password
                self.change_password()
            elif choice == 1:  # Update Profile
                self.update_profile()
            elif choice == 2:  # System Information
                self.show_system_info()

    def change_password(self):
        """Change user password."""
        try:
            print(f"\n{CLIColors.BOLD}🔑 CHANGE PASSWORD{CLIColors.ENDC}")
            print("-" * 40)

            current_password = self.get_password("Current Password: ")
            if not current_password:
                return

            # Verify current password
            if not self.user_service.authenticate_user(self.current_user.username, current_password):
                self.print_error("Current password is incorrect.")
                self.wait_for_enter()
                return

            new_password = self.get_password("New Password: ")
            if not new_password:
                return

            confirm_password = self.get_password("Confirm New Password: ")
            if not confirm_password:
                return

            if new_password != confirm_password:
                self.print_error("Passwords do not match.")
                self.wait_for_enter()
                return

            # Update password
            self.user_service.update_user(self.current_user.id, {'password': new_password})
            self.print_success("Password changed successfully!")

        except Exception as e:
            self.print_error(f"Failed to change password: {e}")

        self.wait_for_enter()

    def update_profile(self):
        """Update user profile."""
        try:
            print(f"\n{CLIColors.BOLD}👤 UPDATE PROFILE{CLIColors.ENDC}")
            print("-" * 40)

            print(f"Current Name: {self.current_user.full_name}")
            print(f"Current Email: {self.current_user.email or 'Not set'}")
            print(f"Current Phone: {self.current_user.phone or 'Not set'}")

            full_name = self.get_input(f"New Full Name [{self.current_user.full_name}]: ", required=False)
            email = self.get_input(f"New Email [{self.current_user.email or 'Not set'}]: ", required=False)
            phone = self.get_input(f"New Phone [{self.current_user.phone or 'Not set'}]: ", required=False)

            update_data = {}
            if full_name:
                update_data['full_name'] = full_name
            if email:
                update_data['email'] = email
            if phone:
                update_data['phone'] = phone

            if update_data:
                self.user_service.update_user(self.current_user.id, update_data)
                # Refresh current user data
                self.current_user = self.user_service.get_user_by_id(self.current_user.id)
                self.print_success("Profile updated successfully!")
            else:
                self.print_info("No changes made.")

        except Exception as e:
            self.print_error(f"Failed to update profile: {e}")

        self.wait_for_enter()

    def show_system_info(self):
        """Show system information."""
        try:
            print(f"\n{CLIColors.BOLD}📊 SYSTEM INFORMATION{CLIColors.ENDC}")
            print("-" * 40)

            # Get system statistics
            user_stats = self.user_service.get_user_statistics()

            print(f"Application Version: 1.0.0")
            print(f"Database: SQLite")
            print(f"Total Users: {user_stats.get('total_users', 0)}")
            print(f"Active Users: {user_stats.get('active_users', 0)}")
            print(f"System Uptime: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

        except Exception as e:
            self.print_error(f"Failed to get system information: {e}")

        self.wait_for_enter()

    def run(self):
        """Main application entry point."""
        try:
            # Initialize system
            if not self.initialize_system():
                return

            # Main application loop
            while self.running:
                if not self.current_user:
                    self.clear_screen()
                    self.print_header()

                    if not self.authenticate_user():
                        if self.confirm_action("Exit application?"):
                            break
                        continue

                self.show_main_menu()

            self.print_info("Thank you for using Military Peer Review Assessment System!")

        except KeyboardInterrupt:
            print(f"\n{CLIColors.WARNING}Application terminated by user.{CLIColors.ENDC}")
        except Exception as e:
            self.print_error(f"Application error: {e}")
            self.logger.error(f"Application error: {e}")
        finally:
            # Cleanup
            try:
                db_connection.close()
            except:
                pass


def main():
    """Main entry point."""
    app = MilitaryPeerReviewCLI()
    app.run()


if __name__ == "__main__":
    main()
