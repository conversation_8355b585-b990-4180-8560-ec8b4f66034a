#!/usr/bin/env python3
"""
Debug script to check user accounts and password hashes
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from database.connection import db_connection
from models.user import User
from core.encryption import password_hasher

def check_users():
    """Check all users in the database."""
    try:
        with db_connection.get_session() as session:
            users = session.query(User).all()
            print(f"Found {len(users)} users in database:")
            print("-" * 80)
            
            for user in users:
                print(f"ID: {user.id}")
                print(f"Username: {user.username}")
                print(f"Full Name: {user.full_name}")
                print(f"Role: {user.role}")
                print(f"Active: {user.is_active}")
                print(f"Password Hash: {repr(user.password_hash)}")
                print(f"Hash Length: {len(user.password_hash) if user.password_hash else 0}")
                
                # Test password verification
                if user.password_hash:
                    test_passwords = ['Admin@123', 'Teacher@123', 'admin', 'password']
                    for test_pwd in test_passwords:
                        try:
                            result = password_hasher.verify_password(test_pwd, user.password_hash)
                            if result:
                                print(f"✅ Password '{test_pwd}' works for {user.username}")
                                break
                        except Exception as e:
                            print(f"❌ Error testing password '{test_pwd}': {e}")
                else:
                    print("❌ No password hash found!")
                
                print("-" * 80)
                
    except Exception as e:
        print(f"Error checking users: {e}")
        import traceback
        traceback.print_exc()

def test_password_hashing():
    """Test password hashing functionality."""
    print("Testing password hashing...")
    try:
        test_password = "Admin@123"
        hashed = password_hasher.hash_password(test_password)
        print(f"Original password: {test_password}")
        print(f"Hashed password: {hashed}")
        print(f"Hash length: {len(hashed)}")
        
        # Test verification
        result = password_hasher.verify_password(test_password, hashed)
        print(f"Verification result: {result}")
        
        # Test wrong password
        wrong_result = password_hasher.verify_password("wrong", hashed)
        print(f"Wrong password result: {wrong_result}")
        
    except Exception as e:
        print(f"Error testing password hashing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=== User Database Debug ===")
    check_users()
    print("\n=== Password Hashing Test ===")
    test_password_hashing()
