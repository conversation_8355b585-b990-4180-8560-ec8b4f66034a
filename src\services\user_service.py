"""
User Service

This module provides business logic for user management operations.
"""

import logging
from datetime import datetime
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError
from sqlalchemy import and_, or_

from models.user import User
from core.encryption import password_hasher
from core.exceptions import ValidationError, DatabaseError
from database.connection import db_connection
# from utils.validators import validate_email, validate_username

def validate_email(email):
    """Simple email validation."""
    import re
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_username(username):
    """Simple username validation."""
    import re
    # Username should be 3-50 characters, alphanumeric and underscores
    pattern = r'^[a-zA-Z0-9_]{3,50}$'
    return re.match(pattern, username) is not None


class UserService:
    """Service class for user management operations."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def create_user(self, user_data: Dict[str, Any]) -> User:
        """Create a new user."""
        try:
            # Validate input data
            self._validate_user_data(user_data)

            # Hash password
            password_hash = password_hasher.hash_password(user_data["password"])

            def _create_user_transaction(session: Session) -> User:
                user = User(
                    username=user_data["username"],
                    password_hash=password_hash,
                    full_name=user_data["full_name"],
                    email=user_data.get("email"),
                    phone=user_data.get("phone"),
                    role=user_data.get("role", "teacher"),
                    is_active=user_data.get("is_active", True),
                    notes=user_data.get("notes")
                )

                session.add(user)
                session.flush()  # Get the ID
                return user

            user = db_connection.execute_transaction(_create_user_transaction)
            self.logger.info(f"User created successfully: {user.username}")
            return user

        except IntegrityError as e:
            self.logger.error(f"User creation failed - integrity error: {e}")
            if "username" in str(e):
                raise ValidationError("Username already exists")
            elif "email" in str(e):
                raise ValidationError("Email already exists")
            else:
                raise ValidationError("User creation failed due to data conflict")
        except Exception as e:
            self.logger.error(f"User creation failed: {e}")
            raise DatabaseError(f"Failed to create user: {e}")

    def get_user_by_id(self, user_id: int) -> Optional[User]:
        """Get a user by ID."""
        try:
            def _get_user(session: Session) -> Optional[User]:
                return session.query(User).filter(User.id == user_id).first()

            return db_connection.execute_transaction(_get_user)

        except Exception as e:
            self.logger.error(f"Failed to get user by ID {user_id}: {e}")
            raise DatabaseError(f"Failed to retrieve user: {e}")

    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get a user by username."""
        try:
            def _get_user(session: Session) -> Optional[User]:
                return session.query(User).filter(User.username == username).first()

            return db_connection.execute_transaction(_get_user)

        except Exception as e:
            self.logger.error(f"Failed to get user by username {username}: {e}")
            raise DatabaseError(f"Failed to retrieve user: {e}")

    def get_all_users(self, include_inactive: bool = False) -> List[User]:
        """Get all users."""
        try:
            def _get_users(session: Session) -> List[User]:
                query = session.query(User)
                if not include_inactive:
                    query = query.filter(User.is_active == True)
                return query.order_by(User.full_name).all()

            return db_connection.execute_transaction(_get_users)

        except Exception as e:
            self.logger.error(f"Failed to get users: {e}")
            raise DatabaseError(f"Failed to retrieve users: {e}")

    def update_user(self, user_id: int, user_data: Dict[str, Any]) -> User:
        """Update an existing user."""
        try:
            # Validate input data (excluding password if not provided)
            if "password" in user_data:
                self._validate_user_data(user_data, is_update=True)
            else:
                self._validate_user_data({**user_data, "password": "dummy"}, is_update=True)

            def _update_user_transaction(session: Session) -> User:
                user = session.query(User).filter(User.id == user_id).first()
                if not user:
                    raise ValidationError("User not found")

                # Update fields
                if "username" in user_data:
                    user.username = user_data["username"]
                if "full_name" in user_data:
                    user.full_name = user_data["full_name"]
                if "email" in user_data:
                    user.email = user_data["email"]
                if "phone" in user_data:
                    user.phone = user_data["phone"]
                if "role" in user_data:
                    user.role = user_data["role"]
                if "is_active" in user_data:
                    user.is_active = user_data["is_active"]
                if "notes" in user_data:
                    user.notes = user_data["notes"]

                # Update password if provided
                if "password" in user_data:
                    user.password_hash = password_hasher.hash_password(user_data["password"])

                user.modified_at = datetime.utcnow()
                return user

            user = db_connection.execute_transaction(_update_user_transaction)
            self.logger.info(f"User updated successfully: {user.username}")
            return user

        except ValidationError:
            raise
        except IntegrityError as e:
            self.logger.error(f"User update failed - integrity error: {e}")
            if "username" in str(e):
                raise ValidationError("Username already exists")
            elif "email" in str(e):
                raise ValidationError("Email already exists")
            else:
                raise ValidationError("User update failed due to data conflict")
        except Exception as e:
            self.logger.error(f"User update failed: {e}")
            raise DatabaseError(f"Failed to update user: {e}")

    def delete_user(self, user_id: int) -> bool:
        """Delete a user (soft delete by setting is_active to False)."""
        try:
            def _delete_user_transaction(session: Session) -> bool:
                user = session.query(User).filter(User.id == user_id).first()
                if not user:
                    raise ValidationError("User not found")

                # Soft delete
                user.is_active = False
                user.modified_at = datetime.utcnow()
                return True

            result = db_connection.execute_transaction(_delete_user_transaction)
            self.logger.info(f"User deleted successfully: {user_id}")
            return result

        except ValidationError:
            raise
        except Exception as e:
            self.logger.error(f"User deletion failed: {e}")
            raise DatabaseError(f"Failed to delete user: {e}")

    def update_last_login(self, user_id: int) -> None:
        """Update the last login timestamp for a user."""
        try:
            def _update_last_login(session: Session) -> None:
                user = session.query(User).filter(User.id == user_id).first()
                if user:
                    user.last_login = datetime.utcnow()

            db_connection.execute_transaction(_update_last_login)

        except Exception as e:
            self.logger.error(f"Failed to update last login for user {user_id}: {e}")
            # Don't raise exception for this non-critical operation

    def change_password(self, user_id: int, old_password: str, new_password: str) -> bool:
        """Change a user's password."""
        try:
            def _change_password_transaction(session: Session) -> bool:
                user = session.query(User).filter(User.id == user_id).first()
                if not user:
                    raise ValidationError("User not found")

                # Verify old password
                if not password_hasher.verify_password(old_password, user.password_hash):
                    raise ValidationError("Current password is incorrect")

                # Validate new password
                is_strong, issues = password_hasher.is_strong_password(new_password)
                if not is_strong:
                    raise ValidationError(f"Password requirements not met: {', '.join(issues)}")

                # Update password
                user.password_hash = password_hasher.hash_password(new_password)
                user.modified_at = datetime.utcnow()
                return True

            result = db_connection.execute_transaction(_change_password_transaction)
            self.logger.info(f"Password changed successfully for user: {user_id}")
            return result

        except ValidationError:
            raise
        except Exception as e:
            self.logger.error(f"Password change failed: {e}")
            raise DatabaseError(f"Failed to change password: {e}")

    def _validate_user_data(self, user_data: Dict[str, Any], is_update: bool = False) -> None:
        """Validate user data."""
        required_fields = ["username", "full_name", "password"]
        if is_update:
            required_fields = []  # For updates, no fields are strictly required

        # Check required fields
        for field in required_fields:
            if field not in user_data or not user_data[field]:
                raise ValidationError(f"Field '{field}' is required")

        # Validate username
        if "username" in user_data:
            if not validate_username(user_data["username"]):
                raise ValidationError("Invalid username format")

        # Validate email
        if "email" in user_data and user_data["email"]:
            if not validate_email(user_data["email"]):
                raise ValidationError("Invalid email format")

        # Validate password strength
        if "password" in user_data:
            is_strong, issues = password_hasher.is_strong_password(user_data["password"])
            if not is_strong:
                raise ValidationError(f"Password requirements not met: {', '.join(issues)}")

        # Validate role
        if "role" in user_data:
            valid_roles = ["super_admin", "admin", "teacher", "student"]
            if user_data["role"] not in valid_roles:
                raise ValidationError(f"Invalid role. Must be one of: {', '.join(valid_roles)}")

        # Validate full name
        if "full_name" in user_data:
            if len(user_data["full_name"].strip()) < 2:
                raise ValidationError("Full name must be at least 2 characters long")

    def get_user_statistics(self) -> Dict[str, Any]:
        """Get user statistics."""
        try:
            def _get_stats_transaction(session: Session) -> Dict[str, Any]:
                total_users = session.query(User).count()
                active_users = session.query(User).filter(User.is_active == True).count()

                # Count by role
                role_counts = {}
                roles = ['super_admin', 'admin', 'teacher', 'student']
                for role in roles:
                    count = session.query(User).filter(
                        and_(User.role == role, User.is_active == True)
                    ).count()
                    role_counts[role] = count

                return {
                    'total_users': total_users,
                    'active_users': active_users,
                    'inactive_users': total_users - active_users,
                    'role_counts': role_counts
                }

            return db_connection.execute_transaction(_get_stats_transaction)

        except Exception as e:
            self.logger.error(f"Failed to get user statistics: {e}")
            raise DatabaseError(f"Failed to get user statistics: {e}")

    def get_users_by_role(self, role: str, include_inactive: bool = False) -> List[User]:
        """Get users by role."""
        try:
            def _get_users_by_role_transaction(session: Session) -> List[User]:
                query = session.query(User).filter(User.role == role)
                if not include_inactive:
                    query = query.filter(User.is_active == True)
                return query.order_by(User.full_name).all()

            return db_connection.execute_transaction(_get_users_by_role_transaction)
        except Exception as e:
            self.logger.error(f"Failed to get users by role {role}: {e}")
            raise DatabaseError(f"Failed to retrieve users: {e}")

    def authenticate_user(self, username: str, password: str) -> Optional[User]:
        """Authenticate a user with username and password."""
        try:
            def _authenticate_transaction(session: Session) -> Optional[User]:
                user = session.query(User).filter(
                    User.username == username,
                    User.is_active == True
                ).first()

                if user and password_hasher.verify_password(password, user.password_hash):
                    return user
                return None

            user = db_connection.execute_transaction(_authenticate_transaction)
            if user:
                self.logger.info(f"Authentication successful for user: {username}")
            else:
                self.logger.warning(f"Authentication failed for user: {username}")

            return user

        except Exception as e:
            self.logger.error(f"Authentication error for user {username}: {e}")
            raise DatabaseError(f"Authentication failed: {e}")

    def search_users(self, search_term: str) -> List[User]:
        """Search users by username, full name, or email."""
        try:
            def _search_users_transaction(session: Session) -> List[User]:
                search_pattern = f"%{search_term}%"
                return session.query(User).filter(
                    or_(
                        User.username.ilike(search_pattern),
                        User.full_name.ilike(search_pattern),
                        User.email.ilike(search_pattern)
                    )
                ).filter(User.is_active == True).order_by(User.full_name).all()

            return db_connection.execute_transaction(_search_users_transaction)

        except Exception as e:
            self.logger.error(f"User search failed: {e}")
            raise DatabaseError(f"Failed to search users: {e}")
