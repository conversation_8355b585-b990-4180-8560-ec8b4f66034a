#!/usr/bin/env python3
"""
Application Icon Generator

This script creates military-appropriate application icons for the
Military Peer Review Assessment System in multiple formats and sizes.
"""

import os
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont
import io

class IconGenerator:
    def __init__(self):
        self.assets_dir = Path("assets")
        self.icons_dir = self.assets_dir / "icons"
        self.desktop_assets_dir = Path("desktop/assets")

        # Create directories
        self.assets_dir.mkdir(exist_ok=True)
        self.icons_dir.mkdir(exist_ok=True)
        self.desktop_assets_dir.mkdir(parents=True, exist_ok=True)

        # Military color scheme
        self.colors = {
            'primary': '#1e3c72',      # Navy blue
            'secondary': '#2a5298',    # Lighter blue
            'accent': '#ffd700',       # Gold
            'white': '#ffffff',
            'dark': '#1a1a1a'
        }

    def create_base_icon(self, size):
        """Create base icon design."""
        # Create image with transparent background
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)

        # Calculate dimensions
        margin = size // 8
        inner_size = size - (2 * margin)
        center = size // 2

        # Draw background circle with gradient effect
        for i in range(5):
            radius = inner_size // 2 - i
            alpha = 255 - (i * 30)
            color = (*self._hex_to_rgb(self.colors['primary']), alpha)
            draw.ellipse([
                center - radius, center - radius,
                center + radius, center + radius
            ], fill=color)

        # Draw military star/badge shape
        star_size = inner_size // 3
        self._draw_military_star(draw, center, center, star_size)

        # Add text for larger sizes
        if size >= 48:
            try:
                # Try to use a system font
                font_size = max(8, size // 8)
                font = ImageFont.truetype("arial.ttf", font_size)
            except:
                font = ImageFont.load_default()

            # Add "PRS" text
            text = "PRS"
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]

            text_x = center - text_width // 2
            text_y = center + star_size // 2 + 5

            # Draw text with outline
            for dx in [-1, 0, 1]:
                for dy in [-1, 0, 1]:
                    if dx != 0 or dy != 0:
                        draw.text((text_x + dx, text_y + dy), text,
                                font=font, fill=self.colors['dark'])

            draw.text((text_x, text_y), text, font=font, fill=self.colors['accent'])

        return img

    def _hex_to_rgb(self, hex_color):
        """Convert hex color to RGB tuple."""
        hex_color = hex_color.lstrip('#')
        return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))

    def _draw_military_star(self, draw, center_x, center_y, size):
        """Draw a military-style star."""
        # Draw a 5-pointed star
        import math

        points = []
        for i in range(10):
            angle = (i * math.pi) / 5
            if i % 2 == 0:
                # Outer points
                radius = size
            else:
                # Inner points
                radius = size * 0.4

            x = center_x + radius * math.cos(angle - math.pi/2)
            y = center_y + radius * math.sin(angle - math.pi/2)
            points.append((x, y))

        # Draw star with gradient effect
        draw.polygon(points, fill=self.colors['accent'])

        # Add inner circle
        inner_radius = size * 0.3
        draw.ellipse([
            center_x - inner_radius, center_y - inner_radius,
            center_x + inner_radius, center_y + inner_radius
        ], fill=self.colors['white'])

    def create_ico_file(self, output_path):
        """Create Windows ICO file with multiple sizes."""
        sizes = [16, 32, 48, 64, 128, 256]
        images = []

        for size in sizes:
            img = self.create_base_icon(size)
            images.append(img)

        # Save as ICO
        images[0].save(output_path, format='ICO', sizes=[(img.width, img.height) for img in images])
        print(f"Created ICO file: {output_path}")

    def create_png_icons(self):
        """Create PNG icons in various sizes."""
        sizes = [16, 32, 48, 64, 128, 256, 512, 1024]

        for size in sizes:
            img = self.create_base_icon(size)
            output_path = self.icons_dir / f"icon_{size}x{size}.png"
            img.save(output_path, format='PNG')
            print(f"Created PNG icon: {output_path}")

        # Create a large PNG for general use
        large_img = self.create_base_icon(512)
        large_img.save(self.icons_dir / "icon.png", format='PNG')
        print(f"Created general PNG icon: {self.icons_dir / 'icon.png'}")

    def create_desktop_assets(self):
        """Create assets for desktop application."""
        # Windows ICO
        ico_path = self.desktop_assets_dir / "icon.ico"
        self.create_ico_file(ico_path)

        # macOS ICNS (simplified - just copy large PNG)
        large_png = self.create_base_icon(1024)
        icns_path = self.desktop_assets_dir / "icon.icns"
        large_png.save(icns_path.with_suffix('.png'), format='PNG')
        print(f"Created macOS icon base: {icns_path.with_suffix('.png')}")

        # Linux PNG
        linux_png = self.create_base_icon(256)
        linux_path = self.desktop_assets_dir / "icon.png"
        linux_png.save(linux_path, format='PNG')
        print(f"Created Linux icon: {linux_path}")

    def create_web_assets(self):
        """Create web application assets."""
        web_dir = Path("src/static/images")
        web_dir.mkdir(parents=True, exist_ok=True)

        # Favicon
        favicon = self.create_base_icon(32)
        favicon.save(web_dir / "favicon.ico", format='ICO')

        # Logo for web interface
        logo = self.create_base_icon(128)
        logo.save(web_dir / "logo.png", format='PNG')

        # Large logo for splash screens
        large_logo = self.create_base_icon(256)
        large_logo.save(web_dir / "logo_large.png", format='PNG')

        print(f"Created web assets in: {web_dir}")

    def generate_all_icons(self):
        """Generate all icon formats and sizes."""
        print("Generating Military Peer Review Assessment System Icons...")
        print("=" * 60)

        try:
            self.create_png_icons()
            self.create_desktop_assets()
            self.create_web_assets()

            print("\n" + "=" * 60)
            print("Icon generation completed successfully!")
            print("\nGenerated files:")
            print(f"  - PNG icons: {self.icons_dir}")
            print(f"  - Desktop assets: {self.desktop_assets_dir}")
            print(f"  - Web assets: src/static/images")

        except Exception as e:
            print(f"Error generating icons: {e}")
            print("Note: PIL (Pillow) library is required for icon generation.")
            print("Install with: pip install Pillow")

def main():
    """Main function."""
    generator = IconGenerator()
    generator.generate_all_icons()

if __name__ == "__main__":
    main()
