{% extends "base.html" %}

{% block title %}Dashboard - Military Peer Review Assessment System{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">Welcome back, {{ user.full_name }}!</h1>
                    <p class="text-muted mb-0">
                        <i class="bi bi-calendar3 me-2"></i>
                        <span id="currentDateTime"></span>
                    </p>
                </div>
                <div class="text-end">
                    <span class="badge bg-primary fs-6 px-3 py-2">{{ user.role|title }}</span>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-12">
            <h3 class="h4 fw-semibold mb-3">
                <i class="bi bi-graph-up me-2 text-primary"></i>System Overview
            </h3>
        </div>
    </div>

    <div class="row g-4 mb-5">
        <!-- Total Users Card -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-primary bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-people-fill text-primary fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem; letter-spacing: 0.5px;">Total Users</h6>
                            <h2 class="fw-bold text-primary mb-0" id="totalUsers">{{ stats.total_users or 0 }}</h2>
                            <small class="text-muted">Active accounts</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Student Batches Card -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-success bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-collection-fill text-success fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem; letter-spacing: 0.5px;">Student Batches</h6>
                            <h2 class="fw-bold text-success mb-0" id="totalBatches">{{ stats.total_batches or 0 }}</h2>
                            <small class="text-muted">Active batches</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Active Assessments Card -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-warning bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-clipboard-check-fill text-warning fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem; letter-spacing: 0.5px;">Active Assessments</h6>
                            <h2 class="fw-bold text-warning mb-0" id="activeAssessments">{{ stats.active_assessments or 0 }}</h2>
                            <small class="text-muted">In progress</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Completed Assessments Card -->
        <div class="col-xl-3 col-lg-6 col-md-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-shrink-0">
                            <div class="bg-info bg-opacity-10 rounded-3 p-3">
                                <i class="bi bi-check-circle-fill text-info fs-4"></i>
                            </div>
                        </div>
                        <div class="flex-grow-1 ms-3">
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem; letter-spacing: 0.5px;">Completed</h6>
                            <h2 class="fw-bold text-info mb-0" id="completedAssessments">{{ stats.completed_assessments or 0 }}</h2>
                            <small class="text-muted">This month</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row g-4">
        <!-- Quick Actions -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-lightning-charge me-2 text-primary"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        {% if user.role in ['super_admin', 'admin'] %}
                        <div class="col-6">
                            <button class="btn btn-outline-primary w-100 py-3" onclick="showComingSoon()">
                                <i class="bi bi-people fs-4 d-block mb-2"></i>
                                <span class="fw-semibold">Manage Users</span>
                            </button>
                        </div>
                        {% endif %}
                        
                        <div class="col-6">
                            <button class="btn btn-outline-success w-100 py-3" onclick="showComingSoon()">
                                <i class="bi bi-collection fs-4 d-block mb-2"></i>
                                <span class="fw-semibold">Manage Batches</span>
                            </button>
                        </div>
                        
                        <div class="col-6">
                            <button class="btn btn-outline-warning w-100 py-3" onclick="showComingSoon()">
                                <i class="bi bi-plus-circle fs-4 d-block mb-2"></i>
                                <span class="fw-semibold">Create Assessment</span>
                            </button>
                        </div>
                        
                        <div class="col-6">
                            <button class="btn btn-outline-info w-100 py-3" onclick="showComingSoon()">
                                <i class="bi bi-graph-up fs-4 d-block mb-2"></i>
                                <span class="fw-semibold">View Reports</span>
                            </button>
                        </div>
                        
                        {% if user.role in ['super_admin'] %}
                        <div class="col-6">
                            <button class="btn btn-outline-secondary w-100 py-3" onclick="showComingSoon()">
                                <i class="bi bi-gear fs-4 d-block mb-2"></i>
                                <span class="fw-semibold">System Settings</span>
                            </button>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-clock-history me-2 text-primary"></i>Recent Activity
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-semibold">Time</th>
                                    <th class="border-0 fw-semibold">Action</th>
                                    <th class="border-0 fw-semibold">User</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for activity in recent_activity %}
                                <tr>
                                    <td class="text-muted">{{ activity.time }}</td>
                                    <td>{{ activity.action }}</td>
                                    <td>
                                        <span class="badge bg-light text-dark">{{ activity.user }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Update current date and time
    function updateDateTime() {
        const now = new Date();
        const options = { 
            weekday: 'long', 
            year: 'numeric', 
            month: 'long', 
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        };
        document.getElementById('currentDateTime').textContent = now.toLocaleDateString('en-US', options);
    }

    // Update time immediately and then every minute
    updateDateTime();
    setInterval(updateDateTime, 60000);

    // Refresh statistics every 5 minutes
    function refreshStats() {
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                if (!data.error) {
                    document.getElementById('totalUsers').textContent = data.total_users || 0;
                    document.getElementById('totalBatches').textContent = data.total_batches || 0;
                    document.getElementById('activeAssessments').textContent = data.active_assessments || 0;
                    document.getElementById('completedAssessments').textContent = data.completed_assessments || 0;
                }
            })
            .catch(error => console.log('Stats refresh failed:', error));
    }

    // Refresh stats every 5 minutes
    setInterval(refreshStats, 300000);
</script>
{% endblock %}
