"""
Main Application Window

This module defines the main window of the peer review system application.
"""

import logging
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QStackedWidget,
    QMenuBar, QStatusBar, QMessageBox, QApplication
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QAction, QIcon

from ui.login_dialog import LoginDialog
from ui.dashboard import Dashboard
from core.auth import auth_manager
from config.settings import app_settings


class MainWindow(QMainWindow):
    """Main application window with navigation and content areas."""

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)

        # Initialize UI components
        self.central_widget = None
        self.stacked_widget = None
        self.dashboard = None

        # Session management
        self.session_timer = QTimer()
        self.session_timer.timeout.connect(self._check_session)

        self._setup_ui()
        self._setup_menu_bar()
        self._setup_status_bar()
        self._apply_settings()

        # Show login dialog on startup
        self._show_login()

    def _setup_ui(self):
        """Set up the main UI layout."""
        # Set window properties with better sizing
        self.setWindowTitle("Military Peer Review Assessment System")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)  # Default size

        # Window styling is now handled by global stylesheet

        # Create central widget and layout
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        layout = QVBoxLayout(self.central_widget)
        layout.setContentsMargins(0, 0, 0, 0)
        layout.setSpacing(0)

        # Create stacked widget for different views
        self.stacked_widget = QStackedWidget()
        layout.addWidget(self.stacked_widget)

        # Create dashboard (will be shown after login)
        self.dashboard = Dashboard()
        self.stacked_widget.addWidget(self.dashboard)

    def _setup_menu_bar(self):
        """Set up the application menu bar."""
        menubar = self.menuBar()

        # File menu
        file_menu = menubar.addMenu("&File")

        # Settings action
        settings_action = QAction("&Settings", self)
        settings_action.setShortcut("Ctrl+,")
        settings_action.triggered.connect(self._show_settings)
        file_menu.addAction(settings_action)

        file_menu.addSeparator()

        # Exit action
        exit_action = QAction("E&xit", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # View menu
        view_menu = menubar.addMenu("&View")

        # Dashboard action
        dashboard_action = QAction("&Dashboard", self)
        dashboard_action.setShortcut("Ctrl+D")
        dashboard_action.triggered.connect(self._show_dashboard)
        view_menu.addAction(dashboard_action)

        # Tools menu
        tools_menu = menubar.addMenu("&Tools")

        # Backup action
        backup_action = QAction("&Backup Database", self)
        backup_action.triggered.connect(self._backup_database)
        tools_menu.addAction(backup_action)

        # Help menu
        help_menu = menubar.addMenu("&Help")

        # About action
        about_action = QAction("&About", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)

        # Initially disable menu items (enabled after login)
        self._set_menu_enabled(False)

    def _setup_status_bar(self):
        """Set up the status bar."""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

    def _apply_settings(self):
        """Apply saved settings to the window."""
        ui_settings = app_settings.get_ui_settings()

        # Set window size
        self.resize(ui_settings["window_width"], ui_settings["window_height"])

        # Center the window
        self._center_window()

    def _center_window(self):
        """Center the window on the screen."""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)

    def _show_login(self):
        """Show the login dialog."""
        login_dialog = LoginDialog(self)

        if login_dialog.exec() == LoginDialog.Accepted:
            # Login successful
            self._on_login_success()
        else:
            # Login cancelled or failed
            self.close()

    def _on_login_success(self):
        """Handle successful login."""
        self.logger.info("User logged in successfully")

        # Enable menu items
        self._set_menu_enabled(True)

        # Start session monitoring
        self.session_timer.start(60000)  # Check every minute

        # Update status bar
        user = auth_manager.get_current_user()
        if user:
            self.status_bar.showMessage(f"Logged in as: {user.full_name} ({user.role})")

        # Show dashboard
        self._show_dashboard()

    def _show_dashboard(self):
        """Show the main dashboard."""
        if auth_manager.is_authenticated():
            self.stacked_widget.setCurrentWidget(self.dashboard)
            self.dashboard.refresh_data()
        else:
            self._show_login()

    def _show_settings(self):
        """Show the settings dialog."""
        # TODO: Implement settings dialog
        QMessageBox.information(self, "Settings", "Settings dialog not yet implemented.")

    def _backup_database(self):
        """Create a database backup."""
        try:
            from config.database import db_manager
            backup_path = db_manager.backup_database()
            QMessageBox.information(
                self,
                "Backup Complete",
                f"Database backup created successfully:\n{backup_path}"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "Backup Failed",
                f"Failed to create database backup:\n{e}"
            )

    def _show_about(self):
        """Show the about dialog."""
        about_text = """
        <h3>Military Peer Review Assessment System</h3>
        <p>Version 1.0.0</p>
        <p>A comprehensive desktop application for military personnel evaluation using peer assessment methodology.</p>
        <p><b>Author:</b> Maj. Sachin Kumar Singh</p>
        <p><b>Developer:</b> Hrishikesh Mohite</p>
        <p><b>Company:</b> Ajinkyacreatiion PVT. LTD.</p>
        <p><b>Website:</b> <a href="https://www.ajinkyacreatiion.com">www.ajinkyacreatiion.com</a></p>
        """

        QMessageBox.about(self, "About", about_text)

    def _set_menu_enabled(self, enabled: bool):
        """Enable or disable menu items based on authentication status."""
        # Get all menus and actions
        for action in self.menuBar().actions():
            menu = action.menu()
            if menu and menu.title() != "&Help":  # Keep help menu always enabled
                menu.setEnabled(enabled)

    def _check_session(self):
        """Check if the user session is still valid."""
        if not auth_manager.is_authenticated():
            self.session_timer.stop()
            self._on_session_expired()

    def _on_session_expired(self):
        """Handle session expiration."""
        QMessageBox.warning(
            self,
            "Session Expired",
            "Your session has expired. Please log in again."
        )

        # Disable menu items
        self._set_menu_enabled(False)

        # Show login dialog
        self._show_login()

    def logout(self):
        """Log out the current user."""
        auth_manager.logout()
        self.session_timer.stop()

        # Disable menu items
        self._set_menu_enabled(False)

        # Update status bar
        self.status_bar.showMessage("Logged out")

        # Show login dialog
        self._show_login()

    def closeEvent(self, event):
        """Handle window close event."""
        # Save window settings
        ui_settings = app_settings.get_ui_settings()
        if ui_settings["remember_window_state"]:
            app_settings.set("ui/window_width", self.width())
            app_settings.set("ui/window_height", self.height())

        # Log out user
        if auth_manager.is_authenticated():
            auth_manager.logout()

        # Accept the close event
        event.accept()
