"""
Main Dashboard

This module provides the main dashboard interface showing system overview
and quick access to key features.
"""

import logging
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QFrame, QScrollArea, QGroupBox, QProgressBar, QTableWidget, QTableWidgetItem,
    QHeaderView, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QPixmap, QIcon

from core.auth import auth_manager, Permission
from config.settings import app_settings
from utils.bootstrap_style import bootstrap_style, BootstrapColors, BootstrapSpacing
from ui.layouts.responsive import ResponsiveContainer, create_card_container


class StatCard(QFrame):
    """A Bootstrap-style card widget for displaying statistics."""

    def __init__(self, title: str, value: str, subtitle: str = "", color: str = "#0d6efd"):
        super().__init__()
        self.setFixedSize(300, 160)  # Bootstrap card proportions

        # Apply Bootstrap card styling (without box-shadow as Qt doesn't support it)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: #ffffff;
                border: 1px solid rgba(0, 0, 0, 0.125);
                border-radius: 8px;
                border-left: 4px solid {color};
            }}
            QFrame:hover {{
                border-color: rgba(0, 0, 0, 0.175);
                background-color: #f8f9fa;
            }}
        """)

        layout = QVBoxLayout(self)
        layout.setContentsMargins(24, 20, 24, 20)  # Bootstrap card padding
        layout.setSpacing(12)

        # Title with Bootstrap typography
        title_label = QLabel(title)
        title_label.setStyleSheet("""
            color: #6c757d;
            font-size: 14px;
            font-weight: 500;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 8px;
        """)
        layout.addWidget(title_label)

        # Value with Bootstrap display styling
        value_label = QLabel(value)
        value_font = QFont()
        value_font.setPointSize(36)
        value_font.setBold(True)
        value_font.setFamily("-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif")
        value_label.setFont(value_font)
        value_label.setStyleSheet(f"""
            color: {color};
            margin: 8px 0px;
            line-height: 1.2;
        """)
        layout.addWidget(value_label)

        # Subtitle with Bootstrap text-muted styling
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setStyleSheet("""
                color: #6c757d;
                font-size: 14px;
                font-weight: 400;
                margin-top: 4px;
            """)
            layout.addWidget(subtitle_label)

        layout.addStretch()


class QuickActionButton(QPushButton):
    """A Bootstrap-style button for quick actions."""

    def __init__(self, text: str, variant: str = "outline-primary"):
        super().__init__(text)
        self.setFixedSize(200, 140)  # Bootstrap card-like proportions

        # Apply Bootstrap button styling based on variant
        if variant == "outline-primary":
            self.setStyleSheet("""
                QPushButton {
                    background-color: transparent;
                    border: 2px solid #0d6efd;
                    border-radius: 8px;
                    color: #0d6efd;
                    font-size: 16px;
                    font-weight: 500;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                    text-align: center;
                    padding: 20px;
                }
                QPushButton:hover {
                    background-color: #0d6efd;
                    border-color: #0d6efd;
                    color: #ffffff;
                }
                QPushButton:pressed {
                    background-color: #0a58ca;
                    border-color: #0a53be;
                    color: #ffffff;
                }
            """)
        else:
            # Default primary button
            self.setStyleSheet("""
                QPushButton {
                    background-color: #0d6efd;
                    border: 2px solid #0d6efd;
                    border-radius: 8px;
                    color: #ffffff;
                    font-size: 16px;
                    font-weight: 500;
                    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
                    text-align: center;
                    padding: 20px;
                }
                QPushButton:hover {
                    background-color: #0b5ed7;
                    border-color: #0a58ca;
                }
                QPushButton:pressed {
                    background-color: #0a58ca;
                    border-color: #0a53be;
                }
            """)


class Dashboard(QWidget):
    """Main dashboard widget."""

    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)

        # Data refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(300000)  # Refresh every 5 minutes

        self._setup_ui()
        self.refresh_data()

    def _setup_ui(self):
        """Set up the dashboard UI."""
        # Set dashboard background
        self.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
            }
        """)

        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(30, 25, 30, 25)
        main_layout.setSpacing(25)

        # Header section
        self._create_header(main_layout)

        # Statistics section
        self._create_statistics_section(main_layout)

        # Content area with quick actions and recent activity
        content_layout = QHBoxLayout()
        content_layout.setSpacing(25)

        # Left column - Quick actions
        self._create_quick_actions(content_layout)

        # Right column - Recent activity
        self._create_recent_activity(content_layout)

        main_layout.addLayout(content_layout)

        # Add stretch to push content to top
        main_layout.addStretch()

    def _create_header(self, layout):
        """Create the dashboard header."""
        header_layout = QHBoxLayout()
        header_layout.setSpacing(20)

        # Welcome message with enhanced styling
        user = auth_manager.get_current_user()
        if user:
            welcome_label = QLabel(f"Welcome back, {user.full_name}")
            welcome_font = QFont()
            welcome_font.setPointSize(24)
            welcome_font.setBold(True)
            welcome_font.setFamily("Segoe UI")
            welcome_label.setFont(welcome_font)
            welcome_label.setStyleSheet("""
                color: #1f2937;
                margin-bottom: 5px;
            """)
        else:
            welcome_label = QLabel("Dashboard")
            welcome_font = QFont()
            welcome_font.setPointSize(24)
            welcome_font.setBold(True)
            welcome_font.setFamily("Segoe UI")
            welcome_label.setFont(welcome_font)
            welcome_label.setStyleSheet("color: #1f2937;")

        header_layout.addWidget(welcome_label)

        # Add stretch
        header_layout.addStretch()

        # Current date/time with enhanced styling
        current_time = datetime.now().strftime("%A, %B %d, %Y - %I:%M %p")
        time_label = QLabel(current_time)
        time_label.setStyleSheet("""
            color: #6b7280;
            font-size: 16px;
            font-weight: 500;
            font-family: "Segoe UI";
        """)
        header_layout.addWidget(time_label)

        layout.addLayout(header_layout)

    def _create_statistics_section(self, layout):
        """Create the statistics cards section."""
        stats_group = QGroupBox("System Overview")
        stats_group.setStyleSheet("""
            QGroupBox {
                font-weight: 600;
                font-size: 18px;
                font-family: "Segoe UI";
                color: #1f2937;
                border: 1px solid #e5e7eb;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 20px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: #ffffff;
            }
        """)

        stats_layout = QHBoxLayout(stats_group)
        stats_layout.setContentsMargins(25, 25, 25, 25)
        stats_layout.setSpacing(20)

        # Create stat cards with Bootstrap colors
        self.total_users_card = StatCard("Total Users", "0", "Active accounts", "#0d6efd")  # Bootstrap primary
        self.total_batches_card = StatCard("Student Batches", "0", "Active batches", "#198754")  # Bootstrap success
        self.active_assessments_card = StatCard("Active Assessments", "0", "In progress", "#ffc107")  # Bootstrap warning
        self.completed_assessments_card = StatCard("Completed", "0", "This month", "#6f42c1")  # Bootstrap purple

        stats_layout.addWidget(self.total_users_card)
        stats_layout.addWidget(self.total_batches_card)
        stats_layout.addWidget(self.active_assessments_card)
        stats_layout.addWidget(self.completed_assessments_card)
        stats_layout.addStretch()

        layout.addWidget(stats_group)

    def _create_quick_actions(self, layout):
        """Create the quick actions section."""
        actions_group = QGroupBox("Quick Actions")
        actions_group.setFixedWidth(420)  # Increased width for larger buttons
        actions_group.setStyleSheet("""
            QGroupBox {
                font-weight: 600;
                font-size: 18px;
                font-family: "Segoe UI";
                color: #1f2937;
                border: 1px solid #e5e7eb;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 20px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: #ffffff;
            }
        """)

        actions_layout = QGridLayout(actions_group)
        actions_layout.setContentsMargins(25, 25, 25, 25)
        actions_layout.setSpacing(15)

        # Create action buttons based on user permissions
        user = auth_manager.get_current_user()
        if user:
            row, col = 0, 0

            # User management (admin only)
            if auth_manager.has_permission(Permission.CREATE_USER):
                user_mgmt_btn = QuickActionButton("Manage\nUsers")
                user_mgmt_btn.clicked.connect(self._open_user_management)
                actions_layout.addWidget(user_mgmt_btn, row, col)
                col += 1
                if col >= 2:
                    row += 1
                    col = 0

            # Batch management
            if auth_manager.has_permission(Permission.CREATE_BATCH):
                batch_mgmt_btn = QuickActionButton("Manage\nBatches")
                batch_mgmt_btn.clicked.connect(self._open_batch_management)
                actions_layout.addWidget(batch_mgmt_btn, row, col)
                col += 1
                if col >= 2:
                    row += 1
                    col = 0

            # Assessment creation
            if auth_manager.has_permission(Permission.CREATE_ASSESSMENT):
                assessment_btn = QuickActionButton("Create\nAssessment")
                assessment_btn.clicked.connect(self._open_assessment_creation)
                actions_layout.addWidget(assessment_btn, row, col)
                col += 1
                if col >= 2:
                    row += 1
                    col = 0

            # View reports
            if auth_manager.has_permission(Permission.VIEW_REPORTS):
                reports_btn = QuickActionButton("View\nReports")
                reports_btn.clicked.connect(self._open_reports)
                actions_layout.addWidget(reports_btn, row, col)
                col += 1
                if col >= 2:
                    row += 1
                    col = 0

            # System settings (admin only)
            if auth_manager.has_permission(Permission.SYSTEM_SETTINGS):
                settings_btn = QuickActionButton("System\nSettings")
                settings_btn.clicked.connect(self._open_settings)
                actions_layout.addWidget(settings_btn, row, col)

        layout.addWidget(actions_group)

    def _create_recent_activity(self, layout):
        """Create the recent activity section."""
        activity_group = QGroupBox("Recent Activity")
        activity_group.setStyleSheet("""
            QGroupBox {
                font-weight: 600;
                font-size: 18px;
                font-family: "Segoe UI";
                color: #1f2937;
                border: 1px solid #e5e7eb;
                border-radius: 12px;
                margin-top: 15px;
                padding-top: 20px;
                background-color: #ffffff;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 20px;
                padding: 0 10px 0 10px;
                background-color: #ffffff;
            }
        """)

        activity_layout = QVBoxLayout(activity_group)
        activity_layout.setContentsMargins(25, 25, 25, 25)
        activity_layout.setSpacing(15)

        # Create activity table with enhanced styling
        self.activity_table = QTableWidget()
        self.activity_table.setColumnCount(3)
        self.activity_table.setHorizontalHeaderLabels(["Time", "Action", "User"])
        self.activity_table.horizontalHeader().setStretchLastSection(True)
        self.activity_table.setAlternatingRowColors(True)
        self.activity_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.activity_table.setMinimumHeight(250)
        self.activity_table.setMaximumHeight(350)

        # Enhanced table styling
        self.activity_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #f3f4f6;
                background-color: #ffffff;
                alternate-background-color: #f9fafb;
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                font-size: 14px;
                font-family: "Segoe UI";
            }
            QTableWidget::item {
                padding: 12px 8px;
                border-bottom: 1px solid #f3f4f6;
            }
            QTableWidget::item:selected {
                background-color: #dbeafe;
                color: #1e40af;
            }
            QHeaderView::section {
                background-color: #f9fafb;
                padding: 12px 8px;
                border: none;
                border-bottom: 2px solid #e5e7eb;
                font-weight: 600;
                font-size: 14px;
                color: #374151;
            }
        """)

        activity_layout.addWidget(self.activity_table)

        layout.addWidget(activity_group)

    def refresh_data(self):
        """Refresh dashboard data."""
        try:
            self._update_statistics()
            self._update_recent_activity()
        except Exception as e:
            self.logger.error(f"Error refreshing dashboard data: {e}")

    def _update_statistics(self):
        """Update statistics cards with current data."""
        # TODO: Implement actual data retrieval from database
        # For now, using placeholder data

        self.total_users_card.findChild(QLabel).setText("5")  # Placeholder
        self.total_batches_card.findChild(QLabel).setText("3")  # Placeholder
        self.active_assessments_card.findChild(QLabel).setText("2")  # Placeholder
        self.completed_assessments_card.findChild(QLabel).setText("8")  # Placeholder

    def _update_recent_activity(self):
        """Update recent activity table."""
        # TODO: Implement actual activity log retrieval
        # For now, using placeholder data

        activities = [
            ("10:30 AM", "User login", "admin"),
            ("09:45 AM", "Assessment created", "teacher1"),
            ("09:15 AM", "Batch updated", "admin"),
            ("08:30 AM", "Report generated", "teacher2"),
        ]

        self.activity_table.setRowCount(len(activities))
        for row, (time, action, user) in enumerate(activities):
            self.activity_table.setItem(row, 0, QTableWidgetItem(time))
            self.activity_table.setItem(row, 1, QTableWidgetItem(action))
            self.activity_table.setItem(row, 2, QTableWidgetItem(user))

    # Quick action handlers (placeholder implementations)
    def _open_user_management(self):
        """Open user management module."""
        # TODO: Implement user management interface
        pass

    def _open_batch_management(self):
        """Open batch management module."""
        # TODO: Implement batch management interface
        pass

    def _open_assessment_creation(self):
        """Open assessment creation module."""
        # TODO: Implement assessment creation interface
        pass

    def _open_reports(self):
        """Open reports module."""
        # TODO: Implement reports interface
        pass

    def _open_settings(self):
        """Open system settings."""
        # TODO: Implement settings interface
        pass
