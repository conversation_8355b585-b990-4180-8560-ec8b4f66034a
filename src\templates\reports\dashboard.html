{% extends "base.html" %}

{% block title %}Reports Dashboard - Military Peer Review Assessment System{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">
                        <i class="bi bi-graph-up me-2 text-primary"></i>Reports Dashboard
                    </h1>
                    <p class="text-muted mb-0">Comprehensive analytics and reporting for peer review assessments</p>
                </div>
                <div>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                            <i class="bi bi-download me-2"></i>Quick Export
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{{ url_for('reports_export_all', format='pdf') }}">
                                <i class="bi bi-file-pdf me-2"></i>All Reports (PDF)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports_export_all', format='excel') }}">
                                <i class="bi bi-file-excel me-2"></i>All Data (Excel)
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('reports_export_all', format='csv') }}">
                                <i class="bi bi-file-csv me-2"></i>All Data (CSV)
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Summary Statistics -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3 me-3">
                            <i class="bi bi-clipboard-check text-primary fs-4"></i>
                        </div>
                        <div>
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Total Assessments</h6>
                            <h3 class="fw-bold text-primary mb-0">{{ report_stats.total_assessments or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3 me-3">
                            <i class="bi bi-check-circle text-success fs-4"></i>
                        </div>
                        <div>
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Completed Evaluations</h6>
                            <h3 class="fw-bold text-success mb-0">{{ report_stats.total_evaluations or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3 me-3">
                            <i class="bi bi-people text-info fs-4"></i>
                        </div>
                        <div>
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Active Students</h6>
                            <h3 class="fw-bold text-info mb-0">{{ report_stats.active_students or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3 me-3">
                            <i class="bi bi-star text-warning fs-4"></i>
                        </div>
                        <div>
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Average Score</h6>
                            <h3 class="fw-bold text-warning mb-0">{{ "%.1f"|format(report_stats.average_score or 0) }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-bar-chart me-2 text-primary"></i>Assessment Performance Trends
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="performanceTrendChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-4">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-pie-chart me-2 text-primary"></i>Assessment Types
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="assessmentTypesChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- Report Categories -->
    <div class="row mb-4">
        <div class="col-12">
            <h4 class="fw-bold text-dark mb-3">
                <i class="bi bi-folder me-2 text-primary"></i>Report Categories
            </h4>
        </div>
    </div>

    <div class="row g-4">
        <!-- Assessment Reports -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-primary bg-opacity-10 border-0">
                    <h5 class="fw-semibold mb-0 text-primary">
                        <i class="bi bi-clipboard-check me-2"></i>Assessment Reports
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Generate detailed reports for individual assessments and their results.</p>
                    
                    <div class="mb-3">
                        <label for="assessmentSelect" class="form-label fw-semibold">Select Assessment</label>
                        <select class="form-select" id="assessmentSelect">
                            <option value="">Choose an assessment...</option>
                            {% for assessment in assessments %}
                            <option value="{{ assessment.id }}">{{ assessment.title }} ({{ assessment.batch.name if assessment.batch else 'No Batch' }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary" onclick="generateAssessmentReport('summary')">
                            <i class="bi bi-file-text me-2"></i>Assessment Summary
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="generateAssessmentReport('detailed')">
                            <i class="bi bi-file-earmark-text me-2"></i>Detailed Results
                        </button>
                        <button type="button" class="btn btn-outline-primary" onclick="generateAssessmentReport('analytics')">
                            <i class="bi bi-graph-up me-2"></i>Performance Analytics
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Student Reports -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-success bg-opacity-10 border-0">
                    <h5 class="fw-semibold mb-0 text-success">
                        <i class="bi bi-person-check me-2"></i>Student Reports
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Generate individual student performance reports and progress tracking.</p>
                    
                    <div class="row g-2 mb-3">
                        <div class="col-md-6">
                            <label for="batchSelect" class="form-label fw-semibold">Batch</label>
                            <select class="form-select" id="batchSelect" onchange="loadBatchStudents()">
                                <option value="">Select batch...</option>
                                {% for batch in batches %}
                                <option value="{{ batch.id }}">{{ batch.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-6">
                            <label for="studentSelect" class="form-label fw-semibold">Student</label>
                            <select class="form-select" id="studentSelect" disabled>
                                <option value="">Select student...</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-success" onclick="generateStudentReport('individual')">
                            <i class="bi bi-person me-2"></i>Individual Report
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="generateStudentReport('progress')">
                            <i class="bi bi-graph-up me-2"></i>Progress Report
                        </button>
                        <button type="button" class="btn btn-outline-success" onclick="generateStudentReport('comparison')">
                            <i class="bi bi-bar-chart me-2"></i>Peer Comparison
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Batch Reports -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-info bg-opacity-10 border-0">
                    <h5 class="fw-semibold mb-0 text-info">
                        <i class="bi bi-collection me-2"></i>Batch Reports
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Generate comprehensive reports for entire batches and cohort analysis.</p>
                    
                    <div class="mb-3">
                        <label for="batchReportSelect" class="form-label fw-semibold">Select Batch</label>
                        <select class="form-select" id="batchReportSelect">
                            <option value="">Choose a batch...</option>
                            {% for batch in batches %}
                            <option value="{{ batch.id }}">{{ batch.name }} ({{ batch.academic_year }})</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-info" onclick="generateBatchReport('overview')">
                            <i class="bi bi-eye me-2"></i>Batch Overview
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="generateBatchReport('performance')">
                            <i class="bi bi-trophy me-2"></i>Performance Summary
                        </button>
                        <button type="button" class="btn btn-outline-info" onclick="generateBatchReport('comparison')">
                            <i class="bi bi-bar-chart me-2"></i>Batch Comparison
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Reports -->
        <div class="col-lg-6">
            <div class="card border-0 shadow-sm h-100">
                <div class="card-header bg-warning bg-opacity-10 border-0">
                    <h5 class="fw-semibold mb-0 text-warning">
                        <i class="bi bi-graph-up me-2"></i>Analytics Reports
                    </h5>
                </div>
                <div class="card-body">
                    <p class="text-muted mb-3">Advanced analytics and statistical reports for institutional insights.</p>
                    
                    <div class="row g-2 mb-3">
                        <div class="col-md-6">
                            <label for="dateFrom" class="form-label fw-semibold">From Date</label>
                            <input type="date" class="form-control" id="dateFrom">
                        </div>
                        <div class="col-md-6">
                            <label for="dateTo" class="form-label fw-semibold">To Date</label>
                            <input type="date" class="form-control" id="dateTo">
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-warning" onclick="generateAnalyticsReport('trends')">
                            <i class="bi bi-graph-up me-2"></i>Performance Trends
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="generateAnalyticsReport('statistics')">
                            <i class="bi bi-calculator me-2"></i>Statistical Analysis
                        </button>
                        <button type="button" class="btn btn-outline-warning" onclick="generateAnalyticsReport('participation')">
                            <i class="bi bi-people me-2"></i>Participation Analysis
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Reports -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-clock-history me-2 text-primary"></i>Recent Reports
                    </h5>
                </div>
                <div class="card-body">
                    {% if recent_reports %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-semibold">Report Name</th>
                                    <th class="border-0 fw-semibold">Type</th>
                                    <th class="border-0 fw-semibold">Generated</th>
                                    <th class="border-0 fw-semibold">Generated By</th>
                                    <th class="border-0 fw-semibold text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for report in recent_reports %}
                                <tr>
                                    <td>{{ report.name }}</td>
                                    <td><span class="badge bg-secondary">{{ report.type }}</span></td>
                                    <td>{{ report.created_at.strftime('%Y-%m-%d %H:%M') if report.created_at else 'N/A' }}</td>
                                    <td>{{ report.created_by }}</td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <button type="button" class="btn btn-sm btn-outline-primary" onclick="downloadReport('{{ report.id }}')">
                                                <i class="bi bi-download"></i>
                                            </button>
                                            <button type="button" class="btn btn-sm btn-outline-danger" onclick="deleteReport('{{ report.id }}')">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="bi bi-file-text text-muted" style="font-size: 2rem;"></i>
                        <p class="text-muted mt-2">No recent reports available</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
    // Performance Trend Chart
    const trendCtx = document.getElementById('performanceTrendChart').getContext('2d');
    new Chart(trendCtx, {
        type: 'line',
        data: {
            labels: {{ trend_labels | tojson }},
            datasets: [{
                label: 'Average Score',
                data: {{ trend_data | tojson }},
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    max: 10
                }
            }
        }
    });

    // Assessment Types Chart
    const typesCtx = document.getElementById('assessmentTypesChart').getContext('2d');
    new Chart(typesCtx, {
        type: 'doughnut',
        data: {
            labels: {{ assessment_type_labels | tojson }},
            datasets: [{
                data: {{ assessment_type_data | tojson }},
                backgroundColor: [
                    '#FF6384',
                    '#36A2EB',
                    '#FFCE56',
                    '#4BC0C0'
                ]
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });

    // Report generation functions
    function generateAssessmentReport(type) {
        const assessmentId = document.getElementById('assessmentSelect').value;
        if (!assessmentId) {
            alert('Please select an assessment first.');
            return;
        }
        
        window.open(`/reports/assessment/${assessmentId}/${type}`, '_blank');
    }

    function generateStudentReport(type) {
        const studentId = document.getElementById('studentSelect').value;
        if (!studentId) {
            alert('Please select a student first.');
            return;
        }
        
        window.open(`/reports/student/${studentId}/${type}`, '_blank');
    }

    function generateBatchReport(type) {
        const batchId = document.getElementById('batchReportSelect').value;
        if (!batchId) {
            alert('Please select a batch first.');
            return;
        }
        
        window.open(`/reports/batch/${batchId}/${type}`, '_blank');
    }

    function generateAnalyticsReport(type) {
        const dateFrom = document.getElementById('dateFrom').value;
        const dateTo = document.getElementById('dateTo').value;
        
        let url = `/reports/analytics/${type}`;
        if (dateFrom && dateTo) {
            url += `?from=${dateFrom}&to=${dateTo}`;
        }
        
        window.open(url, '_blank');
    }

    function loadBatchStudents() {
        const batchId = document.getElementById('batchSelect').value;
        const studentSelect = document.getElementById('studentSelect');
        
        if (!batchId) {
            studentSelect.disabled = true;
            studentSelect.innerHTML = '<option value="">Select student...</option>';
            return;
        }
        
        fetch(`/api/batches/${batchId}/students`)
            .then(response => response.json())
            .then(data => {
                studentSelect.innerHTML = '<option value="">Select student...</option>';
                data.students.forEach(student => {
                    studentSelect.innerHTML += `<option value="${student.id}">${student.full_name} (${student.student_id})</option>`;
                });
                studentSelect.disabled = false;
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error loading students.');
            });
    }

    function downloadReport(reportId) {
        window.open(`/reports/download/${reportId}`, '_blank');
    }

    function deleteReport(reportId) {
        if (confirm('Are you sure you want to delete this report?')) {
            fetch(`/api/reports/${reportId}/delete`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error deleting report.');
            });
        }
    }
</script>
{% endblock %}
