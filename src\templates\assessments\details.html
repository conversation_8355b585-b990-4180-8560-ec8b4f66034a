{% extends "base.html" %}

{% block title %}{{ assessment.title }} - Assessment Details{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">
                        <i class="bi bi-clipboard-check me-2 text-primary"></i>{{ assessment.title }}
                    </h1>
                    <p class="text-muted mb-0">
                        <strong>{{ assessment.batch.name if assessment.batch else 'No Batch' }}</strong> -
                        {{ assessment.assessment_type.replace('_', ' ').title() }}
                    </p>
                </div>
                <div>
                    <a href="{{ url_for('assessments_list') }}" class="btn btn-outline-secondary me-2">
                        <i class="bi bi-arrow-left me-2"></i>Back to Assessments
                    </a>
                    {% if current_user.role in ['super_admin', 'admin', 'teacher'] %}
                    <a href="{{ url_for('assessments_edit', assessment_id=assessment.id) }}" class="btn btn-primary">
                        <i class="bi bi-pencil me-2"></i>Edit Assessment
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Assessment Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row g-4">
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary bg-opacity-10 rounded-3 p-3 me-3">
                                    <i class="bi bi-people-fill text-primary fs-4"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Participants</h6>
                                    <h3 class="fw-bold text-primary mb-0">{{ assessment_stats.total_students or 0 }}</h3>
                                    <small class="text-muted">students</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-success bg-opacity-10 rounded-3 p-3 me-3">
                                    <i class="bi bi-check-circle-fill text-success fs-4"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Completed</h6>
                                    <h3 class="fw-bold text-success mb-0">{{ assessment_stats.total_evaluations or 0 }}</h3>
                                    <small class="text-muted">evaluations</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-info bg-opacity-10 rounded-3 p-3 me-3">
                                    <i class="bi bi-percent text-info fs-4"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Participation</h6>
                                    <h3 class="fw-bold text-info mb-0">{{ "%.1f"|format(assessment_stats.participation_rate or 0) }}%</h3>
                                    <small class="text-muted">rate</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-warning bg-opacity-10 rounded-3 p-3 me-3">
                                    <i class="bi bi-star-fill text-warning fs-4"></i>
                                </div>
                                <div>
                                    <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Avg Score</h6>
                                    <h3 class="fw-bold text-warning mb-0">{{ "%.1f"|format(assessment_stats.average_scores.overall or 0) }}</h3>
                                    <small class="text-muted">/ {{ assessment.max_score }}</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Assessment Details -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-info-circle me-2 text-primary"></i>Assessment Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-muted">Assessment Type</label>
                            <p class="mb-0">{{ assessment.assessment_type.replace('_', ' ').title() }}</p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-muted">Status</label>
                            <p class="mb-0">
                                {% if assessment.is_active %}
                                    <span class="badge bg-success">Active</span>
                                {% else %}
                                    <span class="badge bg-secondary">Inactive</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-muted">Start Date</label>
                            <p class="mb-0">{{ assessment.start_date.strftime('%Y-%m-%d') if assessment.start_date else 'Not set' }}</p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-muted">End Date</label>
                            <p class="mb-0">{{ assessment.end_date.strftime('%Y-%m-%d') if assessment.end_date else 'Not set' }}</p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-muted">Maximum Score</label>
                            <p class="mb-0">{{ assessment.max_score }}</p>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label fw-semibold text-muted">Passing Score</label>
                            <p class="mb-0">{{ assessment.passing_score }}</p>
                        </div>
                        {% if assessment.description %}
                        <div class="col-12">
                            <label class="form-label fw-semibold text-muted">Description</label>
                            <p class="mb-0">{{ assessment.description }}</p>
                        </div>
                        {% endif %}
                        {% if assessment.instructions %}
                        <div class="col-12">
                            <label class="form-label fw-semibold text-muted">Instructions</label>
                            <div class="bg-light p-3 rounded">
                                <p class="mb-0">{{ assessment.instructions }}</p>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <!-- Peer Evaluation Form (for students) -->
            {% if current_user.role == 'student' and assessment.is_active %}
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-person-check me-2 text-primary"></i>Peer Evaluation
                    </h5>
                </div>
                <div class="card-body">
                    <form id="peerEvaluationForm">
                        <input type="hidden" name="assessment_id" value="{{ assessment.id }}">
                        <input type="hidden" name="evaluator_id" value="{{ current_user.user_id }}">

                        <!-- Student Selection -->
                        <div class="mb-4">
                            <label for="evaluated_id" class="form-label fw-semibold">
                                <i class="bi bi-person me-2"></i>Select Student to Evaluate <span class="text-danger">*</span>
                            </label>
                            <select class="form-select" id="evaluated_id" name="evaluated_id" required>
                                <option value="">Choose a student...</option>
                                {% for student in batch_students %}
                                    {% if student.id != current_user.user_id %}
                                    <option value="{{ student.id }}">{{ student.full_name }} ({{ student.student_id }})</option>
                                    {% endif %}
                                {% endfor %}
                            </select>
                        </div>

                        <!-- Evaluation Criteria -->
                        <div class="row g-3">
                            <div class="col-12">
                                <h6 class="fw-semibold text-primary mb-3">Evaluation Criteria (1-10 scale)</h6>
                            </div>

                            <!-- Leadership -->
                            <div class="col-md-6">
                                <label for="leadership_score" class="form-label fw-semibold">Leadership</label>
                                <input type="range" class="form-range" id="leadership_score" name="leadership_score"
                                       min="1" max="10" value="5" oninput="updateScoreDisplay('leadership')">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">Poor (1)</small>
                                    <span id="leadership_display" class="fw-bold">5</span>
                                    <small class="text-muted">Excellent (10)</small>
                                </div>
                            </div>

                            <!-- Teamwork -->
                            <div class="col-md-6">
                                <label for="teamwork_score" class="form-label fw-semibold">Teamwork</label>
                                <input type="range" class="form-range" id="teamwork_score" name="teamwork_score"
                                       min="1" max="10" value="5" oninput="updateScoreDisplay('teamwork')">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">Poor (1)</small>
                                    <span id="teamwork_display" class="fw-bold">5</span>
                                    <small class="text-muted">Excellent (10)</small>
                                </div>
                            </div>

                            <!-- Communication -->
                            <div class="col-md-6">
                                <label for="communication_score" class="form-label fw-semibold">Communication</label>
                                <input type="range" class="form-range" id="communication_score" name="communication_score"
                                       min="1" max="10" value="5" oninput="updateScoreDisplay('communication')">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">Poor (1)</small>
                                    <span id="communication_display" class="fw-bold">5</span>
                                    <small class="text-muted">Excellent (10)</small>
                                </div>
                            </div>

                            <!-- Technical Skills -->
                            <div class="col-md-6">
                                <label for="technical_score" class="form-label fw-semibold">Technical Skills</label>
                                <input type="range" class="form-range" id="technical_score" name="technical_score"
                                       min="1" max="10" value="5" oninput="updateScoreDisplay('technical')">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">Poor (1)</small>
                                    <span id="technical_display" class="fw-bold">5</span>
                                    <small class="text-muted">Excellent (10)</small>
                                </div>
                            </div>

                            <!-- Professionalism -->
                            <div class="col-md-6">
                                <label for="professionalism_score" class="form-label fw-semibold">Professionalism</label>
                                <input type="range" class="form-range" id="professionalism_score" name="professionalism_score"
                                       min="1" max="10" value="5" oninput="updateScoreDisplay('professionalism')">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">Poor (1)</small>
                                    <span id="professionalism_display" class="fw-bold">5</span>
                                    <small class="text-muted">Excellent (10)</small>
                                </div>
                            </div>

                            <!-- Overall Score (calculated) -->
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">Overall Score</label>
                                <div class="bg-light p-3 rounded text-center">
                                    <span id="overall_score_display" class="h4 fw-bold text-primary">5.0</span>
                                    <span class="text-muted">/ 10</span>
                                </div>
                            </div>

                            <!-- Comments -->
                            <div class="col-12">
                                <label for="comments" class="form-label fw-semibold">General Comments</label>
                                <textarea class="form-control" id="comments" name="comments" rows="3"
                                          placeholder="Provide general feedback about this student's performance"></textarea>
                            </div>

                            <!-- Strengths -->
                            <div class="col-md-6">
                                <label for="strengths" class="form-label fw-semibold">Strengths</label>
                                <textarea class="form-control" id="strengths" name="strengths" rows="3"
                                          placeholder="What are this student's key strengths?"></textarea>
                            </div>

                            <!-- Areas for Improvement -->
                            <div class="col-md-6">
                                <label for="areas_for_improvement" class="form-label fw-semibold">Areas for Improvement</label>
                                <textarea class="form-control" id="areas_for_improvement" name="areas_for_improvement" rows="3"
                                          placeholder="What areas could this student improve?"></textarea>
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-primary btn-lg">
                                        <i class="bi bi-check-circle me-2"></i>Submit Evaluation
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Assessment Settings -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white border-0 pb-0">
                    <h6 class="fw-semibold mb-0">
                        <i class="bi bi-gear me-2 text-primary"></i>Settings
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <span>Self Evaluation</span>
                        <span class="badge bg-{{ 'success' if assessment.allow_self_evaluation else 'secondary' }}">
                            {{ 'Allowed' if assessment.allow_self_evaluation else 'Not Allowed' }}
                        </span>
                    </div>
                    <div class="d-flex justify-content-between align-items-center">
                        <span>Anonymous Feedback</span>
                        <span class="badge bg-{{ 'success' if assessment.anonymous_feedback else 'secondary' }}">
                            {{ 'Yes' if assessment.anonymous_feedback else 'No' }}
                        </span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            {% if current_user.role in ['super_admin', 'admin', 'teacher'] %}
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h6 class="fw-semibold mb-0">
                        <i class="bi bi-lightning me-2 text-primary"></i>Quick Actions
                    </h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ url_for('assessment_results', assessment_id=assessment.id) }}" class="btn btn-outline-primary">
                            <i class="bi bi-bar-chart me-2"></i>View Results
                        </a>
                        <a href="{{ url_for('reports_assessment', assessment_id=assessment.id) }}" class="btn btn-outline-success">
                            <i class="bi bi-download me-2"></i>Export Data
                        </a>
                        <button type="button" class="btn btn-outline-warning" onclick="toggleAssessmentStatus()">
                            <i class="bi bi-{{ 'pause' if assessment.is_active else 'play' }} me-2"></i>
                            {{ 'Deactivate' if assessment.is_active else 'Activate' }}
                        </button>
                    </div>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    function updateScoreDisplay(criterion) {
        const score = document.getElementById(criterion + '_score').value;
        document.getElementById(criterion + '_display').textContent = score;
        calculateOverallScore();
    }

    function calculateOverallScore() {
        const criteria = ['leadership', 'teamwork', 'communication', 'technical', 'professionalism'];
        let total = 0;

        criteria.forEach(criterion => {
            total += parseInt(document.getElementById(criterion + '_score').value);
        });

        const average = (total / criteria.length).toFixed(1);
        document.getElementById('overall_score_display').textContent = average;
    }

    // Peer evaluation form submission
    document.getElementById('peerEvaluationForm')?.addEventListener('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        // Add overall score
        const overallScore = document.getElementById('overall_score_display').textContent;
        formData.append('overall_score', overallScore);

        fetch('/api/assessments/submit-evaluation', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Evaluation submitted successfully!');
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while submitting the evaluation.');
        });
    });

    function toggleAssessmentStatus() {
        const assessmentId = {{ assessment.id }};
        const isActive = {{ 'true' if assessment.is_active else 'false' }};

        fetch(`/api/assessments/${assessmentId}/toggle-status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload();
            } else {
                alert('Error: ' + data.error);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while updating the assessment status.');
        });
    }
</script>
{% endblock %}
