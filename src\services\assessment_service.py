"""
Assessment Service

This module provides comprehensive assessment management functionality
including CRUD operations, peer evaluation management, and scoring.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from database.connection import db_connection
from models.assessment import Assessment, Question, AssessmentResponse, PeerEvaluation
from models.batch import Batch, Student
from models.user import User
from core.exceptions import ValidationError, DatabaseError


class AssessmentService:
    """Service class for assessment management operations."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def create_assessment(self, assessment_data: Dict[str, Any]) -> Assessment:
        """Create a new assessment."""
        try:
            # Validate required fields
            required_fields = ['title', 'batch_id', 'assessment_type']
            for field in required_fields:
                if not assessment_data.get(field):
                    raise ValidationError(f"Field '{field}' is required")

            def _create_assessment_transaction(session: Session) -> Assessment:
                # Check if batch exists
                batch = session.query(Batch).filter(Batch.id == assessment_data['batch_id']).first()
                if not batch:
                    raise ValidationError("Batch not found")

                # Create assessment
                assessment = Assessment(
                    title=assessment_data['title'],
                    description=assessment_data.get('description', ''),
                    assessment_type=assessment_data['assessment_type'],
                    batch_id=assessment_data['batch_id'],
                    created_by_id=assessment_data.get('created_by_id'),
                    start_date=assessment_data.get('start_date'),
                    end_date=assessment_data.get('end_date'),
                    max_score=assessment_data.get('max_score', 100),
                    passing_score=assessment_data.get('passing_score', 60),
                    is_active=assessment_data.get('is_active', True),
                    allow_self_evaluation=assessment_data.get('allow_self_evaluation', False),
                    anonymous_feedback=assessment_data.get('anonymous_feedback', True),
                    instructions=assessment_data.get('instructions', ''),
                    notes=assessment_data.get('notes', ''),
                    created_at=datetime.utcnow()
                )

                session.add(assessment)
                session.flush()  # Get the ID

                self.logger.info(f"Assessment created: {assessment.title} (ID: {assessment.id})")
                return assessment

            return db_connection.execute_transaction(_create_assessment_transaction)

        except Exception as e:
            self.logger.error(f"Failed to create assessment: {e}")
            raise

    def get_assessment_by_id(self, assessment_id: int) -> Optional[Assessment]:
        """Get assessment by ID."""
        try:
            def _get_assessment(session: Session) -> Optional[Assessment]:
                return session.query(Assessment).filter(Assessment.id == assessment_id).first()

            return db_connection.execute_transaction(_get_assessment)
        except Exception as e:
            self.logger.error(f"Failed to get assessment by ID {assessment_id}: {e}")
            raise DatabaseError(f"Failed to retrieve assessment: {e}")

    def get_assessments_by_batch(self, batch_id: int, include_inactive: bool = False) -> List[Assessment]:
        """Get all assessments for a batch."""
        try:
            def _get_assessments(session: Session) -> List[Assessment]:
                query = session.query(Assessment).filter(Assessment.batch_id == batch_id)
                if not include_inactive:
                    query = query.filter(Assessment.is_active == True)
                return query.order_by(Assessment.created_at.desc()).all()

            return db_connection.execute_transaction(_get_assessments)
        except Exception as e:
            self.logger.error(f"Failed to get assessments for batch {batch_id}: {e}")
            raise DatabaseError(f"Failed to retrieve assessments: {e}")

    def update_assessment(self, assessment_id: int, update_data: Dict[str, Any]) -> Assessment:
        """Update assessment information."""
        try:
            def _update_assessment_transaction(session: Session) -> Assessment:
                assessment = session.query(Assessment).filter(Assessment.id == assessment_id).first()
                if not assessment:
                    raise ValidationError("Assessment not found")

                # Update allowed fields
                allowed_fields = [
                    'title', 'description', 'start_date', 'end_date',
                    'max_score', 'passing_score', 'is_active',
                    'allow_self_evaluation', 'anonymous_feedback',
                    'instructions', 'notes'
                ]

                for field, value in update_data.items():
                    if field in allowed_fields:
                        setattr(assessment, field, value)

                assessment.modified_at = datetime.utcnow()

                self.logger.info(f"Assessment updated: {assessment.title} (ID: {assessment.id})")
                return assessment

            return db_connection.execute_transaction(_update_assessment_transaction)

        except Exception as e:
            self.logger.error(f"Failed to update assessment {assessment_id}: {e}")
            raise

    def delete_assessment(self, assessment_id: int, soft_delete: bool = True) -> bool:
        """Delete assessment (soft delete by default)."""
        try:
            def _delete_assessment_transaction(session: Session) -> bool:
                assessment = session.query(Assessment).filter(Assessment.id == assessment_id).first()
                if not assessment:
                    raise ValidationError("Assessment not found")

                if soft_delete:
                    assessment.is_active = False
                    assessment.modified_at = datetime.utcnow()
                    self.logger.info(f"Assessment soft deleted: {assessment.title} (ID: {assessment.id})")
                else:
                    session.delete(assessment)
                    self.logger.info(f"Assessment hard deleted: {assessment.title} (ID: {assessment.id})")

                return True

            return db_connection.execute_transaction(_delete_assessment_transaction)

        except Exception as e:
            self.logger.error(f"Failed to delete assessment {assessment_id}: {e}")
            raise

    def add_question_to_assessment(self, assessment_id: int, question_data: Dict[str, Any]) -> Question:
        """Add a question to an assessment."""
        try:
            # Validate required fields
            required_fields = ['question_text', 'question_type']
            for field in required_fields:
                if not question_data.get(field):
                    raise ValidationError(f"Field '{field}' is required")

            def _add_question_transaction(session: Session) -> Question:
                # Check if assessment exists
                assessment = session.query(Assessment).filter(Assessment.id == assessment_id).first()
                if not assessment:
                    raise ValidationError("Assessment not found")

                # Get next order number
                max_order = session.query(func.max(Question.order_number)).filter(
                    Question.assessment_id == assessment_id
                ).scalar() or 0

                # Create question
                question = Question(
                    assessment_id=assessment_id,
                    question_text=question_data['question_text'],
                    question_type=question_data['question_type'],
                    options=question_data.get('options'),
                    correct_answer=question_data.get('correct_answer'),
                    max_score=question_data.get('max_score', 10),
                    order_number=max_order + 1,
                    is_required=question_data.get('is_required', True),
                    category=question_data.get('category', ''),
                    notes=question_data.get('notes', ''),
                    created_at=datetime.utcnow()
                )

                session.add(question)
                session.flush()  # Get the ID

                self.logger.info(f"Question added to assessment {assessment_id}: {question.id}")
                return question

            return db_connection.execute_transaction(_add_question_transaction)

        except Exception as e:
            self.logger.error(f"Failed to add question to assessment: {e}")
            raise

    def get_questions_by_assessment(self, assessment_id: int) -> List[Question]:
        """Get all questions for an assessment."""
        try:
            def _get_questions(session: Session) -> List[Question]:
                return session.query(Question).filter(
                    Question.assessment_id == assessment_id
                ).order_by(Question.order_number).all()

            return db_connection.execute_transaction(_get_questions)
        except Exception as e:
            self.logger.error(f"Failed to get questions for assessment {assessment_id}: {e}")
            raise DatabaseError(f"Failed to retrieve questions: {e}")

    def submit_peer_evaluation(self, evaluation_data: Dict[str, Any]) -> PeerEvaluation:
        """Submit a peer evaluation."""
        try:
            # Validate required fields
            required_fields = ['assessment_id', 'evaluator_id', 'evaluated_id']
            for field in required_fields:
                if not evaluation_data.get(field):
                    raise ValidationError(f"Field '{field}' is required")

            def _submit_evaluation_transaction(session: Session) -> PeerEvaluation:
                # Check if evaluation already exists
                existing = session.query(PeerEvaluation).filter(
                    and_(
                        PeerEvaluation.assessment_id == evaluation_data['assessment_id'],
                        PeerEvaluation.evaluator_id == evaluation_data['evaluator_id'],
                        PeerEvaluation.evaluated_id == evaluation_data['evaluated_id']
                    )
                ).first()

                if existing:
                    raise ValidationError("Evaluation already submitted for this student")

                # Create peer evaluation
                evaluation = PeerEvaluation(
                    assessment_id=evaluation_data['assessment_id'],
                    evaluator_id=evaluation_data['evaluator_id'],
                    evaluated_id=evaluation_data['evaluated_id'],
                    overall_score=evaluation_data.get('overall_score', 0),
                    leadership_score=evaluation_data.get('leadership_score', 0),
                    teamwork_score=evaluation_data.get('teamwork_score', 0),
                    communication_score=evaluation_data.get('communication_score', 0),
                    technical_score=evaluation_data.get('technical_score', 0),
                    professionalism_score=evaluation_data.get('professionalism_score', 0),
                    comments=evaluation_data.get('comments', ''),
                    strengths=evaluation_data.get('strengths', ''),
                    areas_for_improvement=evaluation_data.get('areas_for_improvement', ''),
                    is_anonymous=evaluation_data.get('is_anonymous', True),
                    submitted_at=datetime.utcnow()
                )

                session.add(evaluation)
                session.flush()  # Get the ID

                self.logger.info(f"Peer evaluation submitted: {evaluation.id}")
                return evaluation

            return db_connection.execute_transaction(_submit_evaluation_transaction)

        except Exception as e:
            self.logger.error(f"Failed to submit peer evaluation: {e}")
            raise

    def get_assessment_statistics(self, assessment_id: int) -> Dict[str, Any]:
        """Get assessment statistics."""
        try:
            def _get_stats_transaction(session: Session) -> Dict[str, Any]:
                assessment = session.query(Assessment).filter(Assessment.id == assessment_id).first()
                if not assessment:
                    raise ValidationError("Assessment not found")

                # Get batch students count
                total_students = session.query(Student).filter(
                    and_(Student.batch_id == assessment.batch_id, Student.is_active == True)
                ).count()

                # Get evaluation counts
                total_evaluations = session.query(PeerEvaluation).filter(
                    PeerEvaluation.assessment_id == assessment_id
                ).count()

                # Get unique evaluators
                unique_evaluators = session.query(PeerEvaluation.evaluator_id).filter(
                    PeerEvaluation.assessment_id == assessment_id
                ).distinct().count()

                # Get average scores
                avg_scores = session.query(
                    func.avg(PeerEvaluation.overall_score),
                    func.avg(PeerEvaluation.leadership_score),
                    func.avg(PeerEvaluation.teamwork_score),
                    func.avg(PeerEvaluation.communication_score),
                    func.avg(PeerEvaluation.technical_score),
                    func.avg(PeerEvaluation.professionalism_score)
                ).filter(PeerEvaluation.assessment_id == assessment_id).first()

                return {
                    'assessment_id': assessment_id,
                    'total_students': total_students,
                    'total_evaluations': total_evaluations,
                    'unique_evaluators': unique_evaluators,
                    'participation_rate': (unique_evaluators / total_students * 100) if total_students > 0 else 0,
                    'average_scores': {
                        'overall': float(avg_scores[0]) if avg_scores[0] else 0,
                        'leadership': float(avg_scores[1]) if avg_scores[1] else 0,
                        'teamwork': float(avg_scores[2]) if avg_scores[2] else 0,
                        'communication': float(avg_scores[3]) if avg_scores[3] else 0,
                        'technical': float(avg_scores[4]) if avg_scores[4] else 0,
                        'professionalism': float(avg_scores[5]) if avg_scores[5] else 0
                    }
                }

            return db_connection.execute_transaction(_get_stats_transaction)

        except Exception as e:
            self.logger.error(f"Failed to get assessment statistics: {e}")
            raise DatabaseError(f"Failed to get assessment statistics: {e}")


    def get_all_assessments(self, include_inactive: bool = False) -> List[Assessment]:
        """Get all assessments."""
        try:
            def _get_all_assessments(session: Session) -> List[Assessment]:
                query = session.query(Assessment)
                if not include_inactive:
                    query = query.filter(Assessment.is_active == True)
                return query.order_by(Assessment.created_at.desc()).all()

            return db_connection.execute_transaction(_get_all_assessments)
        except Exception as e:
            self.logger.error(f"Failed to get all assessments: {e}")
            raise DatabaseError(f"Failed to retrieve assessments: {e}")

    def get_student_evaluations(self, assessment_id: int, student_id: int) -> List[PeerEvaluation]:
        """Get all evaluations for a specific student in an assessment."""
        try:
            def _get_evaluations(session: Session) -> List[PeerEvaluation]:
                return session.query(PeerEvaluation).filter(
                    and_(
                        PeerEvaluation.assessment_id == assessment_id,
                        PeerEvaluation.evaluated_id == student_id
                    )
                ).all()

            return db_connection.execute_transaction(_get_evaluations)
        except Exception as e:
            self.logger.error(f"Failed to get student evaluations: {e}")
            raise DatabaseError(f"Failed to retrieve evaluations: {e}")

    def get_assessment_results_summary(self, assessment_id: int) -> Dict[str, Any]:
        """Get comprehensive results summary for an assessment."""
        try:
            def _get_results_summary(session: Session) -> Dict[str, Any]:
                assessment = session.query(Assessment).filter(Assessment.id == assessment_id).first()
                if not assessment:
                    raise ValidationError("Assessment not found")

                # Get all evaluations for this assessment
                evaluations = session.query(PeerEvaluation).filter(
                    PeerEvaluation.assessment_id == assessment_id
                ).all()

                # Get unique students being evaluated
                evaluated_students = session.query(PeerEvaluation.evaluated_id).filter(
                    PeerEvaluation.assessment_id == assessment_id
                ).distinct().all()

                # Calculate student results
                student_results = []
                for (student_id,) in evaluated_students:
                    student_evals = [e for e in evaluations if e.evaluated_id == student_id]
                    if student_evals:
                        avg_overall = sum(e.overall_score for e in student_evals) / len(student_evals)
                        avg_leadership = sum(e.leadership_score for e in student_evals) / len(student_evals)
                        avg_teamwork = sum(e.teamwork_score for e in student_evals) / len(student_evals)
                        avg_communication = sum(e.communication_score for e in student_evals) / len(student_evals)
                        avg_technical = sum(e.technical_score for e in student_evals) / len(student_evals)
                        avg_professionalism = sum(e.professionalism_score for e in student_evals) / len(student_evals)

                        # Get student info
                        from models.batch import Student
                        student = session.query(Student).filter(Student.id == student_id).first()

                        student_results.append({
                            'student_id': student_id,
                            'student_name': student.full_name if student else 'Unknown',
                            'student_number': student.student_id if student else 'Unknown',
                            'overall_score': avg_overall,
                            'leadership_score': avg_leadership,
                            'teamwork_score': avg_teamwork,
                            'communication_score': avg_communication,
                            'technical_score': avg_technical,
                            'professionalism_score': avg_professionalism,
                            'evaluation_count': len(student_evals)
                        })

                # Calculate summary statistics
                if student_results:
                    overall_scores = [r['overall_score'] for r in student_results]
                    summary_stats = {
                        'total_students': len(student_results),
                        'completed_evaluations': len(evaluations),
                        'participation_rate': (len(evaluated_students) / len(student_results) * 100) if student_results else 0,
                        'average_overall_score': sum(overall_scores) / len(overall_scores),
                        'highest_score': max(overall_scores),
                        'lowest_score': min(overall_scores),
                        'average_leadership_score': sum(r['leadership_score'] for r in student_results) / len(student_results),
                        'average_teamwork_score': sum(r['teamwork_score'] for r in student_results) / len(student_results),
                        'average_communication_score': sum(r['communication_score'] for r in student_results) / len(student_results),
                        'average_technical_score': sum(r['technical_score'] for r in student_results) / len(student_results),
                        'average_professionalism_score': sum(r['professionalism_score'] for r in student_results) / len(student_results),
                        'excellent_count': len([s for s in overall_scores if s >= 8]),
                        'good_count': len([s for s in overall_scores if 6 <= s < 8]),
                        'average_count': len([s for s in overall_scores if 4 <= s < 6]),
                        'below_average_count': len([s for s in overall_scores if s < 4])
                    }
                else:
                    summary_stats = {
                        'total_students': 0,
                        'completed_evaluations': 0,
                        'participation_rate': 0,
                        'average_overall_score': 0,
                        'highest_score': 0,
                        'lowest_score': 0,
                        'average_leadership_score': 0,
                        'average_teamwork_score': 0,
                        'average_communication_score': 0,
                        'average_technical_score': 0,
                        'average_professionalism_score': 0,
                        'excellent_count': 0,
                        'good_count': 0,
                        'average_count': 0,
                        'below_average_count': 0
                    }

                return {
                    'assessment': assessment,
                    'student_results': student_results,
                    'summary_stats': summary_stats
                }

            return db_connection.execute_transaction(_get_results_summary)

        except Exception as e:
            self.logger.error(f"Failed to get assessment results summary: {e}")
            raise DatabaseError(f"Failed to get results summary: {e}")


# Global service instance
assessment_service = AssessmentService()
