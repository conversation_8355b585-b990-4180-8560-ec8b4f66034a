{% extends "base.html" %}

{% block title %}Assessments - Military Peer Review Assessment System{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">
                        <i class="bi bi-clipboard-check me-2 text-primary"></i>Assessment Management
                    </h1>
                    <p class="text-muted mb-0">Manage peer review assessments and evaluations</p>
                </div>
                <div>
                    {% if current_user.role in ['super_admin', 'admin', 'teacher'] %}
                    <a href="{{ url_for('assessments_create') }}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>Create Assessment
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="bg-primary bg-opacity-10 rounded-3 p-3 me-3">
                            <i class="bi bi-clipboard-check text-primary fs-4"></i>
                        </div>
                        <div>
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Total Assessments</h6>
                            <h3 class="fw-bold text-primary mb-0">{{ assessment_stats.total_assessments or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="bg-success bg-opacity-10 rounded-3 p-3 me-3">
                            <i class="bi bi-play-circle text-success fs-4"></i>
                        </div>
                        <div>
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Active</h6>
                            <h3 class="fw-bold text-success mb-0">{{ assessment_stats.active_assessments or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="bg-warning bg-opacity-10 rounded-3 p-3 me-3">
                            <i class="bi bi-clock text-warning fs-4"></i>
                        </div>
                        <div>
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Pending</h6>
                            <h3 class="fw-bold text-warning mb-0">{{ assessment_stats.pending_assessments or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="bg-info bg-opacity-10 rounded-3 p-3 me-3">
                            <i class="bi bi-check-circle text-info fs-4"></i>
                        </div>
                        <div>
                            <h6 class="text-muted text-uppercase fw-semibold mb-1" style="font-size: 0.75rem;">Completed</h6>
                            <h3 class="fw-bold text-info mb-0">{{ assessment_stats.completed_assessments or 0 }}</h3>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Filters and Search -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="row g-3">
                        <div class="col-md-4">
                            <label for="searchAssessments" class="form-label fw-semibold">Search Assessments</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="searchAssessments" placeholder="Search by title, batch, or type...">
                                <button class="btn btn-outline-secondary" type="button">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <label for="filterStatus" class="form-label fw-semibold">Status</label>
                            <select class="form-select" id="filterStatus">
                                <option value="">All Status</option>
                                <option value="active">Active</option>
                                <option value="pending">Pending</option>
                                <option value="completed">Completed</option>
                                <option value="inactive">Inactive</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="filterType" class="form-label fw-semibold">Type</label>
                            <select class="form-select" id="filterType">
                                <option value="">All Types</option>
                                <option value="peer_evaluation">Peer Evaluation</option>
                                <option value="self_assessment">Self Assessment</option>
                                <option value="instructor_review">Instructor Review</option>
                                <option value="final_assessment">Final Assessment</option>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label for="filterBatch" class="form-label fw-semibold">Batch</label>
                            <select class="form-select" id="filterBatch">
                                <option value="">All Batches</option>
                                {% for batch in batches %}
                                <option value="{{ batch.id }}">{{ batch.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-2">
                            <label class="form-label fw-semibold">&nbsp;</label>
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-secondary" onclick="clearFilters()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Clear
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Assessments Table -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-table me-2 text-primary"></i>Assessments
                    </h5>
                </div>
                <div class="card-body">
                    {% if assessments %}
                    <div class="table-responsive">
                        <table class="table table-hover align-middle" id="assessmentsTable">
                            <thead class="table-light">
                                <tr>
                                    <th class="border-0 fw-semibold">Assessment Details</th>
                                    <th class="border-0 fw-semibold">Batch & Type</th>
                                    <th class="border-0 fw-semibold">Schedule</th>
                                    <th class="border-0 fw-semibold">Progress</th>
                                    <th class="border-0 fw-semibold">Status</th>
                                    <th class="border-0 fw-semibold text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for assessment in assessments %}
                                <tr data-status="{{ 'active' if assessment.is_active else 'inactive' }}"
                                    data-type="{{ assessment.assessment_type }}"
                                    data-batch="{{ assessment.batch_id }}">
                                    <td>
                                        <div>
                                            <h6 class="mb-1 fw-semibold">{{ assessment.title }}</h6>
                                            {% if assessment.description %}
                                            <small class="text-muted">{{ assessment.description[:100] }}{% if assessment.description|length > 100 %}...{% endif %}</small>
                                            {% endif %}
                                            <br><small class="text-muted">Created: {{ assessment.created_at.strftime('%Y-%m-%d') if assessment.created_at else 'N/A' }}</small>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            <span class="fw-semibold">{{ assessment.batch.name if assessment.batch else 'No Batch' }}</span>
                                            <br><span class="badge bg-secondary">{{ assessment.assessment_type.replace('_', ' ').title() }}</span>
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            {% if assessment.start_date %}
                                            <small class="text-muted">Start: {{ assessment.start_date.strftime('%Y-%m-%d') }}</small><br>
                                            {% endif %}
                                            {% if assessment.end_date %}
                                            <small class="text-muted">End: {{ assessment.end_date.strftime('%Y-%m-%d') }}</small>
                                            {% else %}
                                            <small class="text-muted">No end date</small>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td>
                                        <div>
                                            {% set progress = assessment.progress or 0 %}
                                            <div class="progress mb-1" style="height: 8px;">
                                                <div class="progress-bar" role="progressbar" style="width: {{ progress }}%"></div>
                                            </div>
                                            <small class="text-muted">{{ progress }}% Complete</small>
                                        </div>
                                    </td>
                                    <td>
                                        {% if assessment.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group" role="group">
                                            <a href="{{ url_for('assessment_details', assessment_id=assessment.id) }}"
                                               class="btn btn-sm btn-outline-primary" title="View Details">
                                                <i class="bi bi-eye"></i>
                                            </a>
                                            {% if current_user.role in ['super_admin', 'admin', 'teacher'] %}
                                            <a href="{{ url_for('assessments_edit', assessment_id=assessment.id) }}"
                                               class="btn btn-sm btn-outline-secondary" title="Edit">
                                                <i class="bi bi-pencil"></i>
                                            </a>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger"
                                                    onclick="deleteAssessment({{ assessment.id }}, '{{ assessment.title }}')"
                                                    title="Delete">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="text-center py-5">
                        <div class="mb-3">
                            <i class="bi bi-clipboard-check text-muted" style="font-size: 3rem;"></i>
                        </div>
                        <h5 class="text-muted">No Assessments Found</h5>
                        <p class="text-muted">No assessments have been created yet.</p>
                        {% if current_user.role in ['super_admin', 'admin', 'teacher'] %}
                        <a href="{{ url_for('assessments_create') }}" class="btn btn-primary">
                            <i class="bi bi-plus-circle me-2"></i>Create First Assessment
                        </a>
                        {% endif %}
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Search functionality
    document.getElementById('searchAssessments').addEventListener('input', function() {
        filterTable();
    });

    // Filter functionality
    document.getElementById('filterStatus').addEventListener('change', filterTable);
    document.getElementById('filterType').addEventListener('change', filterTable);
    document.getElementById('filterBatch').addEventListener('change', filterTable);

    function filterTable() {
        const searchTerm = document.getElementById('searchAssessments').value.toLowerCase();
        const statusFilter = document.getElementById('filterStatus').value;
        const typeFilter = document.getElementById('filterType').value;
        const batchFilter = document.getElementById('filterBatch').value;

        const rows = document.querySelectorAll('#assessmentsTable tbody tr');

        rows.forEach(row => {
            const text = row.textContent.toLowerCase();
            const status = row.getAttribute('data-status');
            const type = row.getAttribute('data-type');
            const batch = row.getAttribute('data-batch');

            const matchesSearch = text.includes(searchTerm);
            const matchesStatus = !statusFilter || status === statusFilter;
            const matchesType = !typeFilter || type === typeFilter;
            const matchesBatch = !batchFilter || batch === batchFilter;

            row.style.display = (matchesSearch && matchesStatus && matchesType && matchesBatch) ? '' : 'none';
        });
    }

    function clearFilters() {
        document.getElementById('searchAssessments').value = '';
        document.getElementById('filterStatus').value = '';
        document.getElementById('filterType').value = '';
        document.getElementById('filterBatch').value = '';
        filterTable();
    }

    function deleteAssessment(assessmentId, assessmentTitle) {
        if (confirm(`Are you sure you want to delete the assessment "${assessmentTitle}"?`)) {
            fetch(`/api/assessments/${assessmentId}/delete`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    location.reload();
                } else {
                    alert('Error: ' + data.error);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the assessment.');
            });
        }
    }
</script>
{% endblock %}
