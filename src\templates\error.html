{% extends "base.html" %}

{% block title %}Error {{ error_code }} - Military Peer Review Assessment System{% endblock %}

{% block content %}
<div class="min-vh-100 d-flex align-items-center justify-content-center">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6">
                <div class="text-center">
                    <!-- Error Icon -->
                    <div class="mb-4">
                        {% if error_code == 404 %}
                            <i class="bi bi-exclamation-triangle text-warning" style="font-size: 5rem;"></i>
                        {% else %}
                            <i class="bi bi-x-circle text-danger" style="font-size: 5rem;"></i>
                        {% endif %}
                    </div>

                    <!-- Error Code -->
                    <h1 class="display-1 fw-bold text-primary mb-3">{{ error_code }}</h1>

                    <!-- Error Message -->
                    <h2 class="h4 fw-semibold text-dark mb-3">
                        {% if error_code == 404 %}
                            Page Not Found
                        {% elif error_code == 500 %}
                            Internal Server Error
                        {% else %}
                            An Error Occurred
                        {% endif %}
                    </h2>

                    <p class="text-muted mb-4">
                        {% if error_code == 404 %}
                            The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.
                        {% elif error_code == 500 %}
                            We're experiencing some technical difficulties. Please try again later.
                        {% else %}
                            {{ error_message }}
                        {% endif %}
                    </p>

                    <!-- Action Buttons -->
                    <div class="d-flex flex-column flex-sm-row gap-3 justify-content-center">
                        {% if session.user_id %}
                            <a href="{{ url_for('dashboard') }}" class="btn btn-primary btn-lg">
                                <i class="bi bi-house me-2"></i>Go to Dashboard
                            </a>
                        {% else %}
                            <a href="{{ url_for('login') }}" class="btn btn-primary btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>Go to Login
                            </a>
                        {% endif %}
                        
                        <button onclick="history.back()" class="btn btn-outline-secondary btn-lg">
                            <i class="bi bi-arrow-left me-2"></i>Go Back
                        </button>
                    </div>

                    <!-- Additional Help -->
                    {% if error_code == 500 %}
                    <div class="mt-5">
                        <div class="alert alert-info" role="alert">
                            <h6 class="alert-heading">
                                <i class="bi bi-info-circle me-2"></i>Need Help?
                            </h6>
                            <p class="mb-0">
                                If this problem persists, please contact your system administrator or 
                                check the application logs for more details.
                            </p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
