# Military Peer Review Assessment System - Installation Guide

## 🚀 **<PERSON><PERSON><PERSON>K START GUIDE**

### **Prerequisites**
- Python 3.8 or higher installed on your system
- Modern web browser (Chrome, Firefox, Safari, Edge)

### **Installation Steps**

#### **Step 1: Download and Extract**
1. Download the complete application folder
2. Extract to your desired location (e.g., `C:\PeerReviewSystem\`)

#### **Step 2: Install Dependencies**
Open command prompt/terminal in the application folder and run:
```bash
pip install Flask SQLAlchemy cryptography bcrypt python-dateutil validators
```

#### **Step 3: Start the Application**

**Option A: Desktop Application (Recommended)**
- Double-click `start_desktop_app.bat` (Windows)
- Or run: `python desktop_app.py`

**Option B: Web Application Only**
- Run: `python src/web_app.py`
- Open browser to: `http://localhost:5000`

#### **Step 4: Login**
- **Username**: `admin`
- **Password**: `Admin@123`
- **⚠️ Important**: Change the default password after first login!

## 📋 **DETAILED INSTALLATION**

### **System Requirements**
- **Operating System**: Windows 10/11, macOS 10.14+, Linux (Ubuntu 18.04+)
- **Python**: Version 3.8 or higher
- **RAM**: Minimum 4GB (8GB recommended)
- **Storage**: 1GB free space
- **Network**: No internet required for operation (offline capable)

### **Python Installation**
If Python is not installed:

**Windows:**
1. Download from: https://python.org/downloads/
2. Run installer and check "Add Python to PATH"
3. Verify: Open cmd and type `python --version`

**macOS:**
1. Install via Homebrew: `brew install python3`
2. Or download from: https://python.org/downloads/
3. Verify: Open terminal and type `python3 --version`

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install python3 python3-pip
```

### **Dependency Installation**
Navigate to the application directory and install required packages:

```bash
# Navigate to application directory
cd path/to/peer-review-system-2

# Install dependencies
pip install Flask SQLAlchemy cryptography bcrypt python-dateutil validators

# Alternative: Install from requirements file
pip install -r requirements.txt
```

### **Database Setup**
The database is automatically created on first run. To manually create the admin user:

```bash
python create_admin_user.py
```

## 🖥️ **RUNNING THE APPLICATION**

### **Desktop Mode (Recommended)**

**Windows:**
```batch
# Double-click the batch file
start_desktop_app.bat

# Or run manually
python desktop_app.py
```

**macOS/Linux:**
```bash
python desktop_app.py
```

The desktop application provides:
- Server start/stop controls
- Status monitoring
- Integrated browser launcher
- Professional desktop interface

### **Web Mode**

**Direct Web Server:**
```bash
python src/web_app.py
```

Then open your browser to: `http://localhost:5000`

### **Production Deployment**

For production use, consider using a proper WSGI server:

```bash
# Install gunicorn
pip install gunicorn

# Run with gunicorn
cd src
gunicorn -w 4 -b 0.0.0.0:5000 web_app:app
```

## 👥 **USER ACCOUNTS**

### **Default Accounts**
The system comes with pre-configured accounts:

| Username | Password | Role | Description |
|----------|----------|------|-------------|
| admin | Admin@123 | Super Admin | Full system access |
| teacher1 | Teacher@123 | Teacher | Sample teacher account |
| teacher2 | Teacher@123 | Teacher | Sample teacher account |

### **Security Notes**
- **Change default passwords immediately**
- Use strong passwords (8+ characters, mixed case, numbers, symbols)
- Regularly review user accounts and permissions
- Monitor login activity through the dashboard

## 🔧 **CONFIGURATION**

### **Database Configuration**
The database is stored in: `data/peer_review.db`

To change database location, edit `src/config/settings.py`:
```python
"database": {
    "path": "path/to/your/database.db"
}
```

### **Server Configuration**
To change server port, edit `src/web_app.py`:
```python
app.run(
    host='127.0.0.1',
    port=5000,  # Change this port
    debug=True
)
```

### **Security Configuration**
For production deployment:
1. Set `debug=False` in `web_app.py`
2. Use environment variables for secrets
3. Configure proper SSL/HTTPS
4. Set up proper firewall rules

## 🔍 **TROUBLESHOOTING**

### **Common Issues**

**1. "Python not found" Error**
- Install Python from python.org
- Ensure Python is added to system PATH
- Restart command prompt/terminal

**2. "Module not found" Error**
- Install dependencies: `pip install -r requirements.txt`
- Check Python version: `python --version`
- Use virtual environment if needed

**3. "Port already in use" Error**
- Change port in `web_app.py`
- Kill existing process using the port
- Use different port number

**4. "Database error" Error**
- Check file permissions in `data/` directory
- Run: `python create_admin_user.py`
- Delete `data/peer_review.db` to reset database

**5. "Permission denied" Error**
- Run as administrator (Windows)
- Check file/folder permissions
- Ensure write access to application directory

### **Log Files**
Check log files for detailed error information:
- `logs/web_app.log` - Web application logs
- `logs/app.log` - General application logs

### **Getting Help**
1. Check log files for error details
2. Verify all dependencies are installed
3. Ensure Python version compatibility
4. Contact system administrator if issues persist

## 📦 **BACKUP AND MAINTENANCE**

### **Backup**
Important files to backup:
- `data/peer_review.db` - Main database
- `logs/` - Application logs
- Configuration files in `src/config/`

### **Updates**
To update the application:
1. Backup current database
2. Replace application files
3. Run database migrations if needed
4. Test functionality

### **Maintenance**
Regular maintenance tasks:
- Monitor log files for errors
- Backup database regularly
- Review user accounts and permissions
- Update dependencies as needed

## 🎯 **NEXT STEPS**

After successful installation:

1. **Login** with admin credentials
2. **Change default password**
3. **Create user accounts** for your organization
4. **Configure system settings**
5. **Create student batches**
6. **Begin peer assessments**

For detailed usage instructions, see the user manual and help documentation within the application.

---

**Support Contact:**
- Author: Maj. Sachin Kumar Singh
- Developer: Hrishikesh Mohite
- Company: Ajinkyacreatiion PVT. LTD.
