const { app, BrowserWindow, <PERSON>u, dialog, shell, ipcMain } = require('electron');
const path = require('path');
const { spawn } = require('child_process');
const fs = require('fs');
const log = require('electron-log');
const pty = require('node-pty');

// Configure logging
log.transports.file.level = 'info';
log.transports.console.level = 'debug';

class MilitaryPeerReviewCLIApp {
    constructor() {
        this.mainWindow = null;
        this.pythonProcess = null;
        this.ptyProcess = null;
        this.isQuitting = false;

        // Set app properties
        app.setName('Military Peer Review Assessment System');

        // Initialize app
        this.initializeApp();
    }

    initializeApp() {
        // Handle app ready
        app.whenReady().then(() => {
            this.createMainWindow();
            this.setupMenu();

            app.on('activate', () => {
                if (BrowserWindow.getAllWindows().length === 0) {
                    this.createMainWindow();
                }
            });
        });

        // Handle app quit
        app.on('before-quit', (event) => {
            this.isQuitting = true;
            if (this.pythonProcess || this.ptyProcess) {
                event.preventDefault();
                this.stopPythonCLI().then(() => {
                    app.quit();
                });
            }
        });

        app.on('window-all-closed', () => {
            if (process.platform !== 'darwin') {
                app.quit();
            }
        });

        // Handle IPC messages
        this.setupIPC();
    }

    createMainWindow() {
        // Create the browser window with terminal
        this.mainWindow = new BrowserWindow({
            width: 1200,
            height: 800,
            minWidth: 1000,
            minHeight: 600,
            icon: this.getIconPath(),
            webPreferences: {
                nodeIntegration: true,
                contextIsolation: false,
                enableRemoteModule: true
            },
            show: false,
            titleBarStyle: 'default',
            autoHideMenuBar: false,
            backgroundColor: '#1e1e1e'
        });

        // Set window title
        this.mainWindow.setTitle('Military Peer Review Assessment System - CLI');

        // Load the terminal HTML
        this.mainWindow.loadFile(path.join(__dirname, 'terminal.html'));

        // Show window when ready
        this.mainWindow.once('ready-to-show', () => {
            this.mainWindow.show();

            // Focus the window
            if (process.platform === 'darwin') {
                app.dock.show();
            }
            this.mainWindow.focus();

            // Start the CLI application
            this.startPythonCLI();
        });

        // Handle window closed
        this.mainWindow.on('closed', () => {
            this.mainWindow = null;
        });

        // Development tools
        if (process.env.NODE_ENV === 'development') {
            this.mainWindow.webContents.openDevTools();
        }
    }

    getIconPath() {
        const iconName = process.platform === 'win32' ? 'icon.ico' :
                        process.platform === 'darwin' ? 'icon.png' : 'icon.png';
        return path.join(__dirname, 'assets', iconName);
    }

    setupMenu() {
        const template = [
            {
                label: 'File',
                submenu: [
                    {
                        label: 'Restart CLI',
                        accelerator: 'CmdOrCtrl+R',
                        click: () => {
                            this.restartCLI();
                        }
                    },
                    { type: 'separator' },
                    {
                        label: 'Exit',
                        accelerator: process.platform === 'darwin' ? 'Cmd+Q' : 'Ctrl+Q',
                        click: () => {
                            app.quit();
                        }
                    }
                ]
            },
            {
                label: 'View',
                submenu: [
                    { role: 'reload' },
                    { role: 'forceReload' },
                    { role: 'toggleDevTools' },
                    { type: 'separator' },
                    { role: 'resetZoom' },
                    { role: 'zoomIn' },
                    { role: 'zoomOut' },
                    { type: 'separator' },
                    { role: 'togglefullscreen' }
                ]
            },
            {
                label: 'Terminal',
                submenu: [
                    {
                        label: 'Clear Terminal',
                        accelerator: 'CmdOrCtrl+K',
                        click: () => {
                            this.clearTerminal();
                        }
                    },
                    {
                        label: 'Copy',
                        accelerator: 'CmdOrCtrl+C',
                        role: 'copy'
                    },
                    {
                        label: 'Paste',
                        accelerator: 'CmdOrCtrl+V',
                        role: 'paste'
                    }
                ]
            },
            {
                label: 'Help',
                submenu: [
                    {
                        label: 'About',
                        click: () => {
                            this.showAboutDialog();
                        }
                    },
                    {
                        label: 'User Manual',
                        click: () => {
                            this.showUserManual();
                        }
                    }
                ]
            }
        ];

        if (process.platform === 'darwin') {
            template.unshift({
                label: app.getName(),
                submenu: [
                    { role: 'about' },
                    { type: 'separator' },
                    { role: 'services' },
                    { type: 'separator' },
                    { role: 'hide' },
                    { role: 'hideOthers' },
                    { role: 'unhide' },
                    { type: 'separator' },
                    { role: 'quit' }
                ]
            });
        }

        const menu = Menu.buildFromTemplate(template);
        Menu.setApplicationMenu(menu);
    }

    setupIPC() {
        ipcMain.handle('get-app-version', () => {
            return app.getVersion();
        });

        ipcMain.handle('start-cli', () => {
            return this.startPythonCLI();
        });

        ipcMain.handle('restart-cli', () => {
            return this.restartCLI();
        });

        ipcMain.on('terminal-input', (event, data) => {
            if (this.ptyProcess) {
                this.ptyProcess.write(data);
            }
        });

        ipcMain.on('terminal-resize', (event, cols, rows) => {
            if (this.ptyProcess) {
                this.ptyProcess.resize(cols, rows);
            }
        });
    }

    async startPythonCLI() {
        try {
            log.info('Starting Python CLI application...');

            // Determine Python executable path
            const pythonPath = this.getPythonPath();
            const scriptPath = path.join(__dirname, '..', 'src', 'cli_app.py');

            // Create PTY process for better terminal interaction
            this.ptyProcess = pty.spawn(pythonPath, [scriptPath], {
                name: 'xterm-color',
                cols: 120,
                rows: 30,
                cwd: path.join(__dirname, '..'),
                env: process.env
            });

            // Handle PTY output
            this.ptyProcess.onData((data) => {
                if (this.mainWindow) {
                    this.mainWindow.webContents.send('terminal-output', data);
                }
            });

            this.ptyProcess.onExit((exitCode) => {
                log.info(`Python CLI process exited with code ${exitCode}`);
                this.ptyProcess = null;

                if (!this.isQuitting) {
                    this.showCLIError();
                }
            });

            log.info('Python CLI application started successfully');
            return { success: true };

        } catch (error) {
            log.error('Error starting Python CLI:', error);
            return { success: false, error: error.message };
        }
    }

    async stopPythonCLI() {
        return new Promise((resolve) => {
            if (this.ptyProcess) {
                log.info('Stopping Python CLI...');

                this.ptyProcess.kill();
                this.ptyProcess = null;
                
                setTimeout(() => {
                    resolve();
                }, 1000);
            } else {
                resolve();
            }
        });
    }

    async restartCLI() {
        try {
            await this.stopPythonCLI();
            await this.startPythonCLI();
            return { success: true };
        } catch (error) {
            log.error('Failed to restart CLI:', error);
            return { success: false, error: error.message };
        }
    }

    getPythonPath() {
        // Try to find Python executable
        return process.platform === 'win32' ? 'python' : 'python3';
    }

    clearTerminal() {
        if (this.mainWindow) {
            this.mainWindow.webContents.send('clear-terminal');
        }
    }

    showCLIError() {
        if (this.mainWindow) {
            dialog.showErrorBox(
                'CLI Error',
                'The Python CLI application has stopped unexpectedly. Use File > Restart CLI to restart it.'
            );
        }
    }

    showAboutDialog() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'About Military Peer Review Assessment System',
            message: 'Military Peer Review Assessment System - CLI Version',
            detail: `Version: ${app.getVersion()}\n\nAuthor: Maj. Sachin Kumar Singh\nDeveloper: Hrishikesh Mohite\nCompany: Ajinkyacreatiion PVT. LTD.\n\nA comprehensive command-line peer evaluation system for military training programs.`,
            buttons: ['OK']
        });
    }

    showUserManual() {
        dialog.showMessageBox(this.mainWindow, {
            type: 'info',
            title: 'User Manual',
            message: 'Military Peer Review Assessment System - CLI User Guide',
            detail: `Getting Started:
1. Login with your credentials (default: admin/Admin@123)
2. Navigate using numbered menu options
3. Use Ctrl+C to cancel operations
4. Use File > Restart CLI if the application becomes unresponsive

Main Features:
- User Management (Admin/Super Admin only)
- Batch Management (Admin/Teacher)
- Assessment Management (Admin/Teacher)
- Peer Evaluations (Students)
- Reports and Analytics

For detailed help, select the appropriate menu option in the CLI.`,
            buttons: ['OK']
        });
    }
}

// Create and start the application
new MilitaryPeerReviewCLIApp();
