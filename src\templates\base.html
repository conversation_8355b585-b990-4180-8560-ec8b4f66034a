<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Military Peer Review Assessment System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{{ url_for('static', filename='css/custom.css') }}" rel="stylesheet">
    
    {% block extra_head %}{% endblock %}
</head>
<body class="bg-light">
    <!-- Navigation Bar -->
    {% if session.user_id %}
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="{{ url_for('dashboard') }}">
                <i class="bi bi-shield-check me-2"></i>
                Military Peer Review System
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('dashboard') }}">
                            <i class="bi bi-speedometer2 me-1"></i>Dashboard
                        </a>
                    </li>
                    {% if session.role in ['super_admin', 'admin'] %}
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showComingSoon()">
                            <i class="bi bi-people me-1"></i>Users
                        </a>
                    </li>
                    {% endif %}
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showComingSoon()">
                            <i class="bi bi-collection me-1"></i>Batches
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showComingSoon()">
                            <i class="bi bi-clipboard-check me-1"></i>Assessments
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showComingSoon()">
                            <i class="bi bi-graph-up me-1"></i>Reports
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle me-1"></i>{{ session.full_name }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><h6 class="dropdown-header">{{ session.username }}</h6></li>
                            <li><span class="dropdown-item-text small text-muted">Role: {{ session.role|title }}</span></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="showComingSoon()">
                                <i class="bi bi-gear me-2"></i>Settings
                            </a></li>
                            <li><a class="dropdown-item" href="{{ url_for('logout') }}">
                                <i class="bi bi-box-arrow-right me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    {% endif %}

    <!-- Flash Messages -->
    <div class="container-fluid mt-3">
        {% with messages = get_flashed_messages(with_categories=true) %}
            {% if messages %}
                {% for category, message in messages %}
                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                        {% if category == 'error' %}
                            <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        {% elif category == 'success' %}
                            <i class="bi bi-check-circle-fill me-2"></i>
                        {% elif category == 'info' %}
                            <i class="bi bi-info-circle-fill me-2"></i>
                        {% elif category == 'warning' %}
                            <i class="bi bi-exclamation-circle-fill me-2"></i>
                        {% endif %}
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}
        {% endwith %}
    </div>

    <!-- Main Content -->
    <main class="{% if session.user_id %}container-fluid mt-4{% else %}{% endif %}">
        {% block content %}{% endblock %}
    </main>

    <!-- Footer -->
    {% if session.user_id %}
    <footer class="bg-dark text-light mt-5 py-4">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="fw-bold">Military Peer Review Assessment System</h6>
                    <p class="small mb-0">Version 1.0.0</p>
                    <p class="small text-muted">Developed by Ajinkyacreatiion PVT. LTD.</p>
                </div>
                <div class="col-md-6 text-md-end">
                    <p class="small mb-0">
                        <strong>Author:</strong> Maj. Sachin Kumar Singh<br>
                        <strong>Developer:</strong> Hrishikesh Mohite
                    </p>
                    <p class="small text-muted mb-0">
                        © {{ moment().year }} Ajinkyacreatiion PVT. LTD.
                    </p>
                </div>
            </div>
        </div>
    </footer>
    {% endif %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Custom JS -->
    <script src="{{ url_for('static', filename='js/custom.js') }}"></script>
    
    {% block extra_scripts %}{% endblock %}

    <script>
        // Coming soon notification
        function showComingSoon() {
            alert('This feature is coming soon!');
        }
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);
    </script>
</body>
</html>
