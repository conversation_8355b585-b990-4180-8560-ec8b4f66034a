{% extends "base.html" %}

{% block title %}Create User - Military Peer Review Assessment System{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">
                        <i class="bi bi-person-plus me-2 text-primary"></i>Create New User
                    </h1>
                    <p class="text-muted mb-0">Add a new user to the system</p>
                </div>
                <div>
                    <a href="{{ url_for('users_list') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Users
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Create User Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-form me-2 text-primary"></i>User Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('users_create') }}" novalidate>
                        <div class="row g-3">
                            <!-- Username -->
                            <div class="col-md-6">
                                <label for="username" class="form-label fw-semibold">
                                    <i class="bi bi-person me-2"></i>Username <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="username" 
                                       name="username" 
                                       placeholder="Enter username"
                                       required
                                       autocomplete="username">
                                <div class="form-text">Username must be unique and contain only letters, numbers, and underscores.</div>
                            </div>

                            <!-- Full Name -->
                            <div class="col-md-6">
                                <label for="full_name" class="form-label fw-semibold">
                                    <i class="bi bi-person-badge me-2"></i>Full Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="full_name" 
                                       name="full_name" 
                                       placeholder="Enter full name"
                                       required
                                       autocomplete="name">
                            </div>

                            <!-- Email -->
                            <div class="col-md-6">
                                <label for="email" class="form-label fw-semibold">
                                    <i class="bi bi-envelope me-2"></i>Email Address
                                </label>
                                <input type="email" 
                                       class="form-control" 
                                       id="email" 
                                       name="email" 
                                       placeholder="Enter email address"
                                       autocomplete="email">
                            </div>

                            <!-- Role -->
                            <div class="col-md-6">
                                <label for="role" class="form-label fw-semibold">
                                    <i class="bi bi-shield me-2"></i>Role <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="role" name="role" required>
                                    <option value="">Select a role</option>
                                    {% if current_user.role == 'super_admin' %}
                                    <option value="super_admin">Super Administrator</option>
                                    {% endif %}
                                    <option value="admin">Administrator</option>
                                    <option value="teacher">Teacher</option>
                                    <option value="student">Student</option>
                                </select>
                                <div class="form-text">
                                    <strong>Super Admin:</strong> Full system access<br>
                                    <strong>Admin:</strong> User and system management<br>
                                    <strong>Teacher:</strong> Assessment creation and management<br>
                                    <strong>Student:</strong> Assessment participation
                                </div>
                            </div>

                            <!-- Password -->
                            <div class="col-md-6">
                                <label for="password" class="form-label fw-semibold">
                                    <i class="bi bi-lock me-2"></i>Password <span class="text-danger">*</span>
                                </label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control" 
                                           id="password" 
                                           name="password" 
                                           placeholder="Enter password"
                                           required
                                           autocomplete="new-password">
                                    <button class="btn btn-outline-secondary" 
                                            type="button" 
                                            id="togglePassword"
                                            title="Show/Hide Password">
                                        <i class="bi bi-eye" id="toggleIcon"></i>
                                    </button>
                                </div>
                                <div class="form-text">
                                    Password must be at least 8 characters long and contain uppercase, lowercase, number, and special character.
                                </div>
                            </div>

                            <!-- Confirm Password -->
                            <div class="col-md-6">
                                <label for="confirm_password" class="form-label fw-semibold">
                                    <i class="bi bi-lock-fill me-2"></i>Confirm Password <span class="text-danger">*</span>
                                </label>
                                <input type="password" 
                                       class="form-control" 
                                       id="confirm_password" 
                                       name="confirm_password" 
                                       placeholder="Confirm password"
                                       required
                                       autocomplete="new-password">
                                <div class="invalid-feedback" id="passwordMismatch">
                                    Passwords do not match.
                                </div>
                            </div>

                            <!-- Active Status -->
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active" 
                                           checked>
                                    <label class="form-check-label fw-semibold" for="is_active">
                                        <i class="bi bi-check-circle me-2"></i>Active User
                                    </label>
                                    <div class="form-text">Inactive users cannot log in to the system.</div>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="col-12">
                                <label for="notes" class="form-label fw-semibold">
                                    <i class="bi bi-sticky me-2"></i>Notes
                                </label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3" 
                                          placeholder="Additional notes about this user (optional)"></textarea>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-3 justify-content-end">
                                    <a href="{{ url_for('users_list') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Create User
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.className = 'bi bi-eye-slash';
        } else {
            passwordField.type = 'password';
            toggleIcon.className = 'bi bi-eye';
        }
    });

    // Password confirmation validation
    function validatePasswordMatch() {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const confirmField = document.getElementById('confirm_password');
        const mismatchFeedback = document.getElementById('passwordMismatch');
        
        if (confirmPassword && password !== confirmPassword) {
            confirmField.classList.add('is-invalid');
            mismatchFeedback.style.display = 'block';
            return false;
        } else {
            confirmField.classList.remove('is-invalid');
            mismatchFeedback.style.display = 'none';
            return true;
        }
    }

    // Add event listeners for password validation
    document.getElementById('password').addEventListener('input', validatePasswordMatch);
    document.getElementById('confirm_password').addEventListener('input', validatePasswordMatch);

    // Form validation
    (function() {
        'use strict';
        
        const form = document.querySelector('form');
        
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity() || !validatePasswordMatch()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    })();

    // Auto-focus username field
    document.getElementById('username').focus();

    // Username validation
    document.getElementById('username').addEventListener('input', function() {
        const username = this.value;
        const validPattern = /^[a-zA-Z0-9_]+$/;
        
        if (username && !validPattern.test(username)) {
            this.setCustomValidity('Username can only contain letters, numbers, and underscores');
        } else {
            this.setCustomValidity('');
        }
    });

    // Password strength indicator
    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        const strengthIndicator = document.getElementById('passwordStrength') || createPasswordStrengthIndicator();
        
        const strength = calculatePasswordStrength(password);
        updatePasswordStrengthIndicator(strengthIndicator, strength);
    });

    function createPasswordStrengthIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'passwordStrength';
        indicator.className = 'mt-2';
        document.getElementById('password').parentNode.appendChild(indicator);
        return indicator;
    }

    function calculatePasswordStrength(password) {
        let score = 0;
        const checks = {
            length: password.length >= 8,
            lowercase: /[a-z]/.test(password),
            uppercase: /[A-Z]/.test(password),
            number: /\d/.test(password),
            special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
        };
        
        score = Object.values(checks).filter(Boolean).length;
        
        return {
            score: score,
            checks: checks,
            level: score < 3 ? 'weak' : score < 5 ? 'medium' : 'strong'
        };
    }

    function updatePasswordStrengthIndicator(indicator, strength) {
        const colors = {
            weak: 'danger',
            medium: 'warning',
            strong: 'success'
        };
        
        const labels = {
            weak: 'Weak',
            medium: 'Medium',
            strong: 'Strong'
        };
        
        indicator.innerHTML = `
            <div class="d-flex align-items-center">
                <small class="text-muted me-2">Password strength:</small>
                <span class="badge bg-${colors[strength.level]}">${labels[strength.level]}</span>
            </div>
        `;
    }
</script>
{% endblock %}
