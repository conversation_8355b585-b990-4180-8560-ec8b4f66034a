#!/usr/bin/env python3
"""
Test login functionality
"""

import sys
import os
import requests
import time

def test_login_api():
    """Test login via HTTP requests."""
    base_url = "http://localhost:5000"
    
    # Create a session to maintain cookies
    session = requests.Session()
    
    print("Testing login functionality...")
    
    try:
        # First, get the login page to establish session
        print("1. Getting login page...")
        response = session.get(f"{base_url}/login")
        if response.status_code == 200:
            print("✅ Login page accessible")
        else:
            print(f"❌ Login page error: {response.status_code}")
            return
        
        # Test login with correct credentials
        print("2. Testing login with admin/Admin@123...")
        login_data = {
            'username': 'admin',
            'password': 'Admin@123'
        }
        
        response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
        
        if response.status_code == 302:  # Redirect indicates successful login
            print("✅ Login successful - redirected to dashboard")
            
            # Follow the redirect to dashboard
            dashboard_response = session.get(f"{base_url}/dashboard")
            if dashboard_response.status_code == 200:
                print("✅ Dashboard accessible after login")
                
                # Check if we can access user management (admin feature)
                users_response = session.get(f"{base_url}/users")
                if users_response.status_code == 200:
                    print("✅ User management accessible (admin privileges confirmed)")
                else:
                    print(f"❌ User management not accessible: {users_response.status_code}")
            else:
                print(f"❌ Dashboard not accessible: {dashboard_response.status_code}")
        else:
            print(f"❌ Login failed - status code: {response.status_code}")
            print(f"Response content: {response.text[:500]}")
        
        # Test login with wrong credentials
        print("3. Testing login with wrong password...")
        wrong_login_data = {
            'username': 'admin',
            'password': 'wrongpassword'
        }
        
        response = session.post(f"{base_url}/login", data=wrong_login_data, allow_redirects=False)
        
        if response.status_code == 200:  # Should stay on login page
            print("✅ Wrong password correctly rejected")
        else:
            print(f"❌ Unexpected response for wrong password: {response.status_code}")
        
        # Test logout
        print("4. Testing logout...")
        logout_response = session.get(f"{base_url}/logout", allow_redirects=False)
        if logout_response.status_code == 302:
            print("✅ Logout successful")
        else:
            print(f"❌ Logout failed: {logout_response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to web server. Make sure it's running on http://localhost:5000")
    except Exception as e:
        print(f"❌ Test error: {e}")

def test_direct_authentication():
    """Test authentication function directly."""
    print("\nTesting direct authentication function...")
    
    try:
        # Add src to path
        sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))
        
        # Import the authentication function
        from web_app import authenticate_user
        
        # Test correct credentials
        print("Testing admin/Admin@123...")
        user = authenticate_user("admin", "Admin@123")
        if user:
            print(f"✅ Authentication successful: {user.username} ({user.role})")
        else:
            print("❌ Authentication failed")
        
        # Test wrong password
        print("Testing admin/wrongpassword...")
        user = authenticate_user("admin", "wrongpassword")
        if user:
            print("❌ Authentication should have failed")
        else:
            print("✅ Wrong password correctly rejected")
        
        # Test non-existent user
        print("Testing nonexistent/password...")
        user = authenticate_user("nonexistent", "password")
        if user:
            print("❌ Non-existent user should not authenticate")
        else:
            print("✅ Non-existent user correctly rejected")
            
    except Exception as e:
        print(f"❌ Direct authentication test error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("=== Login Functionality Test ===")
    
    # Wait a moment for server to be ready
    time.sleep(2)
    
    # Test via HTTP API
    test_login_api()
    
    # Test direct function
    test_direct_authentication()
    
    print("\n=== Test Complete ===")
