"""
Batch Management Service

This module provides comprehensive batch and student management functionality
including CRUD operations, enrollment management, and batch analytics.
"""

import logging
from typing import List, Optional, Dict, Any
from datetime import datetime, date
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func

from database.connection import db_connection
from models.batch import Batch, Student
from models.user import User
from core.exceptions import ValidationError, DatabaseError


class BatchService:
    """Service class for batch management operations."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def create_batch(self, batch_data: Dict[str, Any]) -> Batch:
        """Create a new batch."""
        try:
            # Validate required fields
            required_fields = ['name', 'code', 'academic_year', 'semester']
            for field in required_fields:
                if not batch_data.get(field):
                    raise ValidationError(f"Field '{field}' is required")
            
            with db_connection.get_session() as session:
                # Check if batch name already exists
                existing_batch = session.query(Batch).filter(
                    Batch.name == batch_data['name']
                ).first()
                if existing_batch:
                    raise ValidationError("Batch name already exists")
                
                # Check if batch code already exists
                existing_code = session.query(Batch).filter(
                    Batch.code == batch_data['code']
                ).first()
                if existing_code:
                    raise ValidationError("Batch code already exists")
                
                # Create batch
                batch = Batch(
                    name=batch_data['name'],
                    code=batch_data['code'],
                    description=batch_data.get('description', ''),
                    academic_year=batch_data['academic_year'],
                    semester=batch_data['semester'],
                    course_name=batch_data.get('course_name', ''),
                    course_code=batch_data.get('course_code', ''),
                    start_date=batch_data.get('start_date'),
                    end_date=batch_data.get('end_date'),
                    instructor_id=batch_data.get('instructor_id'),
                    max_students=batch_data.get('max_students', 50),
                    is_active=batch_data.get('is_active', True),
                    notes=batch_data.get('notes', ''),
                    created_at=datetime.utcnow(),
                    created_by_id=batch_data.get('created_by_id')
                )
                
                session.add(batch)
                session.flush()  # Get the ID
                
                self.logger.info(f"Batch created: {batch.name} (ID: {batch.id})")
                return batch
                
        except Exception as e:
            self.logger.error(f"Failed to create batch: {e}")
            raise
    
    def get_batch_by_id(self, batch_id: int) -> Optional[Batch]:
        """Get batch by ID."""
        try:
            with db_connection.get_session() as session:
                return session.query(Batch).filter(Batch.id == batch_id).first()
        except Exception as e:
            self.logger.error(f"Failed to get batch by ID {batch_id}: {e}")
            raise DatabaseError(f"Failed to retrieve batch: {e}")
    
    def get_all_batches(self, include_inactive: bool = False) -> List[Batch]:
        """Get all batches."""
        try:
            with db_connection.get_session() as session:
                query = session.query(Batch)
                if not include_inactive:
                    query = query.filter(Batch.is_active == True)
                return query.order_by(Batch.name).all()
        except Exception as e:
            self.logger.error(f"Failed to get all batches: {e}")
            raise DatabaseError(f"Failed to retrieve batches: {e}")
    
    def update_batch(self, batch_id: int, update_data: Dict[str, Any]) -> Batch:
        """Update batch information."""
        try:
            with db_connection.get_session() as session:
                batch = session.query(Batch).filter(Batch.id == batch_id).first()
                if not batch:
                    raise ValidationError("Batch not found")
                
                # Update allowed fields
                allowed_fields = [
                    'name', 'description', 'academic_year', 'semester',
                    'course_name', 'course_code', 'start_date', 'end_date',
                    'instructor_id', 'max_students', 'is_active', 'notes'
                ]
                
                for field, value in update_data.items():
                    if field in allowed_fields:
                        setattr(batch, field, value)
                
                batch.modified_at = datetime.utcnow()
                
                self.logger.info(f"Batch updated: {batch.name} (ID: {batch.id})")
                return batch
                
        except Exception as e:
            self.logger.error(f"Failed to update batch {batch_id}: {e}")
            raise
    
    def delete_batch(self, batch_id: int, soft_delete: bool = True) -> bool:
        """Delete batch (soft delete by default)."""
        try:
            with db_connection.get_session() as session:
                batch = session.query(Batch).filter(Batch.id == batch_id).first()
                if not batch:
                    raise ValidationError("Batch not found")
                
                if soft_delete:
                    batch.is_active = False
                    batch.modified_at = datetime.utcnow()
                    self.logger.info(f"Batch soft deleted: {batch.name} (ID: {batch.id})")
                else:
                    session.delete(batch)
                    self.logger.info(f"Batch hard deleted: {batch.name} (ID: {batch.id})")
                
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to delete batch {batch_id}: {e}")
            raise
    
    def get_batch_statistics(self) -> Dict[str, Any]:
        """Get batch statistics."""
        try:
            with db_connection.get_session() as session:
                total_batches = session.query(Batch).count()
                active_batches = session.query(Batch).filter(Batch.is_active == True).count()
                total_students = session.query(Student).count()
                active_students = session.query(Student).filter(Student.is_active == True).count()
                
                return {
                    'total_batches': total_batches,
                    'active_batches': active_batches,
                    'inactive_batches': total_batches - active_batches,
                    'total_students': total_students,
                    'active_students': active_students,
                    'inactive_students': total_students - active_students
                }
                
        except Exception as e:
            self.logger.error(f"Failed to get batch statistics: {e}")
            raise DatabaseError(f"Failed to get batch statistics: {e}")
    
    # Student Management Methods
    def add_student_to_batch(self, batch_id: int, student_data: Dict[str, Any]) -> Student:
        """Add a student to a batch."""
        try:
            # Validate required fields
            required_fields = ['student_id', 'full_name']
            for field in required_fields:
                if not student_data.get(field):
                    raise ValidationError(f"Field '{field}' is required")
            
            with db_connection.get_session() as session:
                # Check if batch exists
                batch = session.query(Batch).filter(Batch.id == batch_id).first()
                if not batch:
                    raise ValidationError("Batch not found")
                
                # Check if student ID already exists
                existing_student = session.query(Student).filter(
                    Student.student_id == student_data['student_id']
                ).first()
                if existing_student:
                    raise ValidationError("Student ID already exists")
                
                # Create student
                student = Student(
                    student_id=student_data['student_id'],
                    service_number=student_data.get('service_number'),
                    first_name=student_data.get('first_name', ''),
                    last_name=student_data.get('last_name', ''),
                    middle_name=student_data.get('middle_name', ''),
                    full_name=student_data['full_name'],
                    email=student_data.get('email'),
                    phone=student_data.get('phone'),
                    rank=student_data.get('rank'),
                    unit=student_data.get('unit'),
                    branch=student_data.get('branch'),
                    batch_id=batch_id,
                    enrollment_date=student_data.get('enrollment_date', date.today()),
                    is_active=student_data.get('is_active', True),
                    status=student_data.get('status', 'enrolled'),
                    notes=student_data.get('notes', ''),
                    created_at=datetime.utcnow(),
                    created_by_id=student_data.get('created_by_id')
                )
                
                session.add(student)
                session.flush()  # Get the ID
                
                self.logger.info(f"Student added to batch: {student.full_name} (ID: {student.id})")
                return student
                
        except Exception as e:
            self.logger.error(f"Failed to add student to batch: {e}")
            raise
    
    def get_students_by_batch(self, batch_id: int, include_inactive: bool = False) -> List[Student]:
        """Get all students in a batch."""
        try:
            with db_connection.get_session() as session:
                query = session.query(Student).filter(Student.batch_id == batch_id)
                if not include_inactive:
                    query = query.filter(Student.is_active == True)
                return query.order_by(Student.full_name).all()
        except Exception as e:
            self.logger.error(f"Failed to get students for batch {batch_id}: {e}")
            raise DatabaseError(f"Failed to retrieve students: {e}")
    
    def update_student(self, student_id: int, update_data: Dict[str, Any]) -> Student:
        """Update student information."""
        try:
            with db_connection.get_session() as session:
                student = session.query(Student).filter(Student.id == student_id).first()
                if not student:
                    raise ValidationError("Student not found")
                
                # Update allowed fields
                allowed_fields = [
                    'full_name', 'email', 'phone', 'rank', 'unit', 'branch',
                    'enrollment_date', 'graduation_date', 'is_active', 'status',
                    'overall_grade', 'gpa', 'notes', 'emergency_contact', 'address'
                ]
                
                for field, value in update_data.items():
                    if field in allowed_fields:
                        setattr(student, field, value)
                
                student.modified_at = datetime.utcnow()
                
                self.logger.info(f"Student updated: {student.full_name} (ID: {student.id})")
                return student
                
        except Exception as e:
            self.logger.error(f"Failed to update student {student_id}: {e}")
            raise
    
    def remove_student_from_batch(self, student_id: int, soft_delete: bool = True) -> bool:
        """Remove student from batch."""
        try:
            with db_connection.get_session() as session:
                student = session.query(Student).filter(Student.id == student_id).first()
                if not student:
                    raise ValidationError("Student not found")
                
                if soft_delete:
                    student.is_active = False
                    student.status = "dropped"
                    student.modified_at = datetime.utcnow()
                    self.logger.info(f"Student soft deleted: {student.full_name} (ID: {student.id})")
                else:
                    session.delete(student)
                    self.logger.info(f"Student hard deleted: {student.full_name} (ID: {student.id})")
                
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to remove student {student_id}: {e}")
            raise


# Global service instance
batch_service = BatchService()
