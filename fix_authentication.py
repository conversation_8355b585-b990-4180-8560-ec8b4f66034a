#!/usr/bin/env python3
"""
Fix authentication issues by resetting user passwords
"""

import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(os.path.abspath(__file__)), 'src'))

from database.connection import db_connection
from models.user import User
from core.encryption import password_hasher
from datetime import datetime

def fix_user_passwords():
    """Fix user passwords by rehashing them properly."""
    try:
        print("Fixing user passwords...")
        
        with db_connection.get_session() as session:
            # Get all users
            users = session.query(User).all()
            print(f"Found {len(users)} users")
            
            # Define correct passwords for each user
            user_passwords = {
                'admin': 'Admin@123',
                'teacher1': 'Teacher@123',
                'teacher2': 'Teacher@123'
            }
            
            for user in users:
                if user.username in user_passwords:
                    correct_password = user_passwords[user.username]
                    
                    print(f"Fixing password for user: {user.username}")
                    print(f"  Current hash: {repr(user.password_hash)}")
                    
                    # Generate new hash
                    new_hash = password_hasher.hash_password(correct_password)
                    print(f"  New hash: {repr(new_hash)}")
                    
                    # Update user
                    user.password_hash = new_hash
                    user.modified_at = datetime.utcnow()
                    
                    # Test the new hash
                    test_result = password_hasher.verify_password(correct_password, new_hash)
                    print(f"  Verification test: {'✅ PASS' if test_result else '❌ FAIL'}")
                    
                else:
                    print(f"Unknown user: {user.username} - skipping")
            
            # Commit changes
            session.commit()
            print("✅ All user passwords fixed successfully!")
            
    except Exception as e:
        print(f"❌ Error fixing passwords: {e}")
        import traceback
        traceback.print_exc()

def test_authentication():
    """Test authentication with fixed passwords."""
    try:
        print("\nTesting authentication...")
        
        with db_connection.get_session() as session:
            test_cases = [
                ('admin', 'Admin@123'),
                ('teacher1', 'Teacher@123'),
                ('teacher2', 'Teacher@123'),
                ('admin', 'wrong_password'),  # Should fail
            ]
            
            for username, password in test_cases:
                user = session.query(User).filter(
                    User.username == username,
                    User.is_active == True
                ).first()
                
                if user and user.password_hash:
                    result = password_hasher.verify_password(password, user.password_hash)
                    status = "✅ PASS" if result else "❌ FAIL"
                    print(f"  {username} / {password}: {status}")
                else:
                    print(f"  {username}: ❌ USER NOT FOUND OR NO HASH")
                    
    except Exception as e:
        print(f"❌ Error testing authentication: {e}")
        import traceback
        traceback.print_exc()

def create_missing_admin():
    """Create admin user if it doesn't exist."""
    try:
        print("\nChecking for admin user...")
        
        with db_connection.get_session() as session:
            admin_user = session.query(User).filter(
                User.username == "admin"
            ).first()
            
            if not admin_user:
                print("Creating missing admin user...")
                
                password_hash = password_hasher.hash_password("Admin@123")
                
                admin_user = User(
                    username="admin",
                    password_hash=password_hash,
                    full_name="System Administrator",
                    email="<EMAIL>",
                    role="super_admin",
                    is_active=True,
                    is_verified=True,
                    created_at=datetime.utcnow(),
                    notes="Default system administrator account"
                )
                
                session.add(admin_user)
                session.commit()
                
                print("✅ Admin user created successfully!")
            else:
                print("✅ Admin user already exists")
                
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function."""
    print("=== Authentication Fix Script ===")
    print("This script will fix password hashing issues in the database.")
    print()
    
    # Initialize database connection
    try:
        # Test database connection
        with db_connection.get_session() as session:
            user_count = session.query(User).count()
            print(f"Database connection successful. Found {user_count} users.")
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return
    
    # Create missing admin user if needed
    create_missing_admin()
    
    # Fix existing user passwords
    fix_user_passwords()
    
    # Test authentication
    test_authentication()
    
    print("\n=== Fix Complete ===")
    print("You can now try logging in with:")
    print("  Username: admin")
    print("  Password: Admin@123")

if __name__ == "__main__":
    main()
