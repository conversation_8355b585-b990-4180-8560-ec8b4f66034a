"""
Responsive Layout System for Qt Applications

This module provides Bootstrap-inspired responsive layout managers
that adapt to different screen sizes and maintain proper proportions.
"""

import logging
from typing import List, Dict, Optional, Tuple
from PySide6.QtWidgets import (
    QWidget, QLayout, QHB<PERSON>Layout, Q<PERSON><PERSON>Layout, QGridLayout, 
    QSizePolicy, QSpacerItem, QApplication
)
from PySide6.QtCore import Qt, QSize, QRect, QTimer
from PySide6.QtGui import QResizeEvent

from utils.bootstrap_style import BootstrapBreakpoints, BootstrapSpacing


class ResponsiveGridLayout(QGridLayout):
    """Bootstrap-inspired responsive grid layout."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.breakpoints = BootstrapBreakpoints()
        self.spacing_system = BootstrapSpacing()
        
        # Grid configuration
        self.columns = 12  # Bootstrap standard
        self.current_breakpoint = "lg"
        self.widget_configs = {}  # Store widget column configurations
        
        # Set default spacing
        self.setSpacing(self.spacing_system.MD)
        self.setContentsMargins(
            self.spacing_system.MD, 
            self.spacing_system.MD, 
            self.spacing_system.MD, 
            self.spacing_system.MD
        )
    
    def add_widget(self, widget: QWidget, row: int, col_config: Dict[str, int], 
                   row_span: int = 1, alignment: Qt.AlignmentFlag = Qt.AlignTop):
        """
        Add widget with responsive column configuration.
        
        Args:
            widget: Widget to add
            row: Row position
            col_config: Dictionary with breakpoint column configurations
                       e.g., {"xs": 12, "md": 6, "lg": 4}
            row_span: Number of rows to span
            alignment: Widget alignment
        """
        # Store configuration for responsive updates
        self.widget_configs[widget] = {
            "row": row,
            "col_config": col_config,
            "row_span": row_span,
            "alignment": alignment
        }
        
        # Add widget with current breakpoint configuration
        col_span = col_config.get(self.current_breakpoint, col_config.get("md", 12))
        col_start = self._calculate_column_start(row, col_span)
        
        super().addWidget(widget, row, col_start, row_span, col_span, alignment)
    
    def update_layout_for_size(self, size: QSize):
        """Update layout based on container size."""
        width = size.width()
        
        # Determine current breakpoint
        new_breakpoint = self._get_breakpoint_for_width(width)
        
        if new_breakpoint != self.current_breakpoint:
            self.current_breakpoint = new_breakpoint
            self._reorganize_widgets()
    
    def _get_breakpoint_for_width(self, width: int) -> str:
        """Determine breakpoint based on width."""
        if width >= self.breakpoints.XXL:
            return "xxl"
        elif width >= self.breakpoints.XL:
            return "xl"
        elif width >= self.breakpoints.LG:
            return "lg"
        elif width >= self.breakpoints.MD:
            return "md"
        elif width >= self.breakpoints.SM:
            return "sm"
        else:
            return "xs"
    
    def _reorganize_widgets(self):
        """Reorganize widgets based on current breakpoint."""
        # Remove all widgets temporarily
        widgets_to_readd = []
        
        for widget, config in self.widget_configs.items():
            # Remove widget from layout
            self.removeWidget(widget)
            widgets_to_readd.append((widget, config))
        
        # Re-add widgets with new configuration
        for widget, config in widgets_to_readd:
            col_span = config["col_config"].get(
                self.current_breakpoint, 
                config["col_config"].get("md", 12)
            )
            col_start = self._calculate_column_start(config["row"], col_span)
            
            super().addWidget(
                widget, 
                config["row"], 
                col_start, 
                config["row_span"], 
                col_span, 
                config["alignment"]
            )
    
    def _calculate_column_start(self, row: int, col_span: int) -> int:
        """Calculate starting column for widget placement."""
        # Simple left-to-right placement
        # In a more sophisticated implementation, this would handle
        # proper grid positioning and wrapping
        return 0


class ResponsiveContainer(QWidget):
    """Container widget that provides responsive behavior."""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = logging.getLogger(__name__)
        self.breakpoints = BootstrapBreakpoints()
        self.spacing_system = BootstrapSpacing()
        
        # Responsive configuration
        self.current_breakpoint = "lg"
        self.layout_configs = {}
        self.resize_timer = QTimer()
        self.resize_timer.setSingleShot(True)
        self.resize_timer.timeout.connect(self._handle_resize_complete)
        
        # Set up responsive grid layout
        self.grid_layout = ResponsiveGridLayout(self)
        self.setLayout(self.grid_layout)
    
    def add_responsive_widget(self, widget: QWidget, row: int, 
                            col_config: Dict[str, int], row_span: int = 1):
        """Add a widget with responsive column configuration."""
        self.grid_layout.add_widget(widget, row, col_config, row_span)
    
    def resizeEvent(self, event: QResizeEvent):
        """Handle resize events with debouncing."""
        super().resizeEvent(event)
        
        # Debounce resize events
        self.resize_timer.stop()
        self.resize_timer.start(100)  # 100ms delay
    
    def _handle_resize_complete(self):
        """Handle resize completion."""
        self.grid_layout.update_layout_for_size(self.size())


class FlexLayout(QHBoxLayout):
    """Flexbox-inspired layout for Qt."""
    
    def __init__(self, direction: str = "row", justify: str = "start", 
                 align: str = "stretch", wrap: bool = False):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        self.spacing_system = BootstrapSpacing()
        
        self.direction = direction  # "row" or "column"
        self.justify = justify      # "start", "center", "end", "space-between", "space-around"
        self.align = align          # "start", "center", "end", "stretch"
        self.wrap = wrap
        
        # Convert to QVBoxLayout if column direction
        if direction == "column":
            # Note: In a real implementation, you'd need to handle this differently
            # as you can't change the base class after initialization
            pass
        
        self._setup_layout()
    
    def _setup_layout(self):
        """Set up layout based on flex properties."""
        # Set spacing
        self.setSpacing(self.spacing_system.SM)
        
        # Handle justify-content
        if self.justify == "center":
            self.addStretch()
        elif self.justify == "end":
            self.addStretch()
    
    def addWidget(self, widget: QWidget, stretch: int = 0):
        """Add widget with flex properties."""
        if self.justify == "center":
            if self.count() == 1:  # First widget after initial stretch
                super().addWidget(widget, stretch)
                self.addStretch()
            else:
                super().addWidget(widget, stretch)
        elif self.justify == "end":
            super().addWidget(widget, stretch)
        elif self.justify == "space-between":
            if self.count() > 0:
                self.addStretch()
            super().addWidget(widget, stretch)
        elif self.justify == "space-around":
            self.addStretch()
            super().addWidget(widget, stretch)
            self.addStretch()
        else:  # start
            super().addWidget(widget, stretch)
            if self.justify == "start" and stretch == 0:
                # Add stretch at the end for start alignment
                pass


class CardLayout(QVBoxLayout):
    """Bootstrap card-inspired layout."""
    
    def __init__(self, title: str = "", padding: int = None):
        super().__init__()
        self.spacing_system = BootstrapSpacing()
        
        # Set padding
        if padding is None:
            padding = self.spacing_system.MD
        
        self.setContentsMargins(padding, padding, padding, padding)
        self.setSpacing(self.spacing_system.SM)
        
        # Add title if provided
        if title:
            from PySide6.QtWidgets import QLabel
            from PySide6.QtGui import QFont
            
            title_label = QLabel(title)
            title_font = QFont()
            title_font.setPointSize(18)
            title_font.setBold(True)
            title_label.setFont(title_font)
            title_label.setStyleSheet(f"color: #212529; margin-bottom: {self.spacing_system.SM}px;")
            
            self.addWidget(title_label)


def create_responsive_row(*widgets, col_configs: List[Dict[str, int]] = None) -> ResponsiveContainer:
    """
    Create a responsive row with multiple widgets.
    
    Args:
        widgets: Widgets to add to the row
        col_configs: List of column configurations for each widget
    
    Returns:
        ResponsiveContainer with widgets arranged in a row
    """
    container = ResponsiveContainer()
    
    if col_configs is None:
        # Equal width columns
        cols_per_widget = 12 // len(widgets)
        col_configs = [{"xs": 12, "md": cols_per_widget} for _ in widgets]
    
    for i, (widget, config) in enumerate(zip(widgets, col_configs)):
        container.add_responsive_widget(widget, 0, config)
    
    return container


def create_card_container(title: str = "", shadow: bool = True) -> QWidget:
    """Create a Bootstrap-style card container."""
    from PySide6.QtWidgets import QFrame
    from utils.bootstrap_style import bootstrap_style
    
    card = QFrame()
    card.setStyleSheet(bootstrap_style.get_card_style(shadow=shadow))
    
    layout = CardLayout(title)
    card.setLayout(layout)
    
    return card
