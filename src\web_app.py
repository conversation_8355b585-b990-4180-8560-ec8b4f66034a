#!/usr/bin/env python3
"""
Military Peer Review Assessment System - Web Application
Main Flask application entry point

Author: Maj<PERSON><PERSON>
Developer: Hrishikesh Mohite
Company: Ajinkyacreatiion PVT. LTD.
"""

import os
import sys
import logging
from pathlib import Path
from datetime import datetime, timedelta

# Add src directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
try:
    from werkzeug.security import check_password_hash
except ImportError:
    # Fallback for older versions or if werkzeug is not available
    import bcrypt
    def check_password_hash(hash_val, password):
        return bcrypt.checkpw(password.encode('utf-8'), hash_val.encode('utf-8'))
import secrets

from config.settings import AppSettings
from config.database import DatabaseManager
from core.exceptions import DatabaseError, AuthenticationError, ValidationError
from core.auth import auth_manager
from database.connection import db_connection
from models.user import User
from services.user_service import UserService


def create_app():
    """Create and configure the Flask application."""
    app = Flask(__name__)

    # Configure Flask app
    app.config['SECRET_KEY'] = secrets.token_hex(16)
    app.config['SESSION_TYPE'] = 'filesystem'
    app.config['SESSION_PERMANENT'] = False
    app.config['SESSION_USE_SIGNER'] = True
    app.config['SESSION_KEY_PREFIX'] = 'peer_review_'
    app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=8)

    # Setup logging
    setup_logging()

    # Initialize database
    if not initialize_database():
        raise RuntimeError("Failed to initialize database")

    # Register routes
    register_routes(app)

    return app


def setup_logging():
    """Configure application logging."""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "web_app.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )


def initialize_database():
    """Initialize the database and run migrations."""
    try:
        db_manager = DatabaseManager()
        db_manager.initialize()
        logging.info("Database initialized successfully")
        return True
    except DatabaseError as e:
        logging.error(f"Database initialization failed: {e}")
        return False


def register_routes(app):
    """Register all application routes."""

    @app.route('/')
    def index():
        """Home page - redirect to login or dashboard."""
        if 'user_id' in session:
            return redirect(url_for('dashboard'))
        return redirect(url_for('login'))

    @app.route('/login', methods=['GET', 'POST'])
    def login():
        """User login page."""
        if request.method == 'POST':
            username = request.form.get('username')
            password = request.form.get('password')

            if not username or not password:
                flash('Please enter both username and password.', 'error')
                return render_template('login.html')

            try:
                # Authenticate user
                user = authenticate_user(username, password)
                if user:
                    # Set session
                    session['user_id'] = user.id
                    session['username'] = user.username
                    session['full_name'] = user.full_name
                    session['role'] = user.role
                    session.permanent = True

                    # Update last login
                    update_last_login(user.id)

                    flash(f'Welcome back, {user.full_name}!', 'success')
                    return redirect(url_for('dashboard'))
                else:
                    flash('Invalid username or password.', 'error')
            except Exception as e:
                logging.error(f"Login error: {e}")
                flash('An error occurred during login. Please try again.', 'error')

        return render_template('login.html')

    @app.route('/logout')
    def logout():
        """User logout."""
        session.clear()
        flash('You have been logged out successfully.', 'info')
        return redirect(url_for('login'))

    @app.route('/dashboard')
    def dashboard():
        """Main dashboard page."""
        if 'user_id' not in session:
            return redirect(url_for('login'))

        try:
            # Get dashboard data
            stats = get_dashboard_stats()
            recent_activity = get_recent_activity()

            return render_template('dashboard.html',
                                 user=session,
                                 stats=stats,
                                 recent_activity=recent_activity)
        except Exception as e:
            logging.error(f"Dashboard error: {e}")
            flash('An error occurred loading the dashboard.', 'error')
            return render_template('dashboard.html',
                                 user=session,
                                 stats={},
                                 recent_activity=[])

    @app.route('/api/stats')
    def api_stats():
        """API endpoint for dashboard statistics."""
        if 'user_id' not in session:
            return jsonify({'error': 'Not authenticated'}), 401

        try:
            stats = get_dashboard_stats()
            return jsonify(stats)
        except Exception as e:
            logging.error(f"API stats error: {e}")
            return jsonify({'error': 'Failed to load statistics'}), 500

    # User Management Routes
    @app.route('/users')
    def users_list():
        """User management page."""
        if 'user_id' not in session:
            return redirect(url_for('login'))

        # Check if user has admin privileges
        if session.get('role') not in ['super_admin', 'admin']:
            flash('Access denied. Admin privileges required.', 'error')
            return redirect(url_for('dashboard'))

        try:
            user_service = UserService()
            users = user_service.get_all_users(include_inactive=True)
            user_stats = user_service.get_user_statistics()

            return render_template('users/list.html',
                                 users=users,
                                 user_stats=user_stats,
                                 current_user=session)
        except Exception as e:
            logging.error(f"Users list error: {e}")
            flash('An error occurred loading users.', 'error')
            return redirect(url_for('dashboard'))

    @app.route('/users/create', methods=['GET', 'POST'])
    def users_create():
        """Create new user page."""
        if 'user_id' not in session:
            return redirect(url_for('login'))

        # Check if user has admin privileges
        if session.get('role') not in ['super_admin', 'admin']:
            flash('Access denied. Admin privileges required.', 'error')
            return redirect(url_for('dashboard'))

        if request.method == 'POST':
            try:
                user_service = UserService()
                user_data = {
                    'username': request.form.get('username'),
                    'password': request.form.get('password'),
                    'full_name': request.form.get('full_name'),
                    'email': request.form.get('email'),
                    'role': request.form.get('role'),
                    'is_active': request.form.get('is_active') == 'on',
                    'notes': request.form.get('notes', '')
                }

                user = user_service.create_user(user_data)
                flash(f'User "{user.username}" created successfully!', 'success')
                return redirect(url_for('users_list'))

            except ValidationError as e:
                flash(str(e), 'error')
            except Exception as e:
                logging.error(f"User creation error: {e}")
                flash('An error occurred creating the user.', 'error')

        return render_template('users/create.html', current_user=session)

    @app.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
    def users_edit(user_id):
        """Edit user page."""
        if 'user_id' not in session:
            return redirect(url_for('login'))

        # Check if user has admin privileges or is editing their own profile
        if session.get('role') not in ['super_admin', 'admin'] and session.get('user_id') != user_id:
            flash('Access denied.', 'error')
            return redirect(url_for('dashboard'))

        try:
            user_service = UserService()
            user = user_service.get_user_by_id(user_id)

            if not user:
                flash('User not found.', 'error')
                return redirect(url_for('users_list'))

            if request.method == 'POST':
                update_data = {
                    'full_name': request.form.get('full_name'),
                    'email': request.form.get('email'),
                    'notes': request.form.get('notes', '')
                }

                # Only admins can change role and active status
                if session.get('role') in ['super_admin', 'admin']:
                    update_data['role'] = request.form.get('role')
                    update_data['is_active'] = request.form.get('is_active') == 'on'

                # Handle password update
                new_password = request.form.get('password')
                if new_password:
                    update_data['password'] = new_password

                user = user_service.update_user(user_id, update_data)
                flash(f'User "{user.username}" updated successfully!', 'success')

                if session.get('role') in ['super_admin', 'admin']:
                    return redirect(url_for('users_list'))
                else:
                    return redirect(url_for('dashboard'))

            return render_template('users/edit.html', user=user, current_user=session)

        except ValidationError as e:
            flash(str(e), 'error')
            return render_template('users/edit.html', user=user, current_user=session)
        except Exception as e:
            logging.error(f"User edit error: {e}")
            flash('An error occurred updating the user.', 'error')
            return redirect(url_for('users_list'))

    @app.route('/users/<int:user_id>/delete', methods=['POST'])
    def users_delete(user_id):
        """Delete user."""
        if 'user_id' not in session:
            return jsonify({'error': 'Not authenticated'}), 401

        # Check if user has admin privileges
        if session.get('role') not in ['super_admin', 'admin']:
            return jsonify({'error': 'Access denied'}), 403

        try:
            user_service = UserService()
            user = user_service.get_user_by_id(user_id)

            if not user:
                return jsonify({'error': 'User not found'}), 404

            # Prevent deleting self
            if user_id == session.get('user_id'):
                return jsonify({'error': 'Cannot delete your own account'}), 400

            user_service.delete_user(user_id)
            return jsonify({'success': True, 'message': f'User "{user.username}" deleted successfully'})

        except Exception as e:
            logging.error(f"User deletion error: {e}")
            return jsonify({'error': 'Failed to delete user'}), 500

    @app.errorhandler(404)
    def not_found(error):
        """Handle 404 errors."""
        return render_template('error.html',
                             error_code=404,
                             error_message="Page not found"), 404

    @app.errorhandler(500)
    def internal_error(error):
        """Handle 500 errors."""
        return render_template('error.html',
                             error_code=500,
                             error_message="Internal server error"), 500


def authenticate_user(username: str, password: str) -> User:
    """Authenticate user credentials."""
    try:
        with db_connection.get_session() as session:
            user = session.query(User).filter(
                User.username == username,
                User.is_active == True
            ).first()

            if user and check_password_hash(user.password_hash, password):
                return user
            return None
    except Exception as e:
        logging.error(f"Authentication error: {e}")
        return None


def update_last_login(user_id: int):
    """Update user's last login timestamp."""
    try:
        with db_connection.get_session() as session:
            user = session.query(User).filter(User.id == user_id).first()
            if user:
                user.last_login = datetime.utcnow()
                # Session is automatically committed by context manager
    except Exception as e:
        logging.error(f"Failed to update last login: {e}")


def get_dashboard_stats():
    """Get dashboard statistics."""
    try:
        with db_connection.get_session() as session:
            total_users = session.query(User).filter(User.is_active == True).count()

            # TODO: Add actual queries for other stats when models are available
            stats = {
                'total_users': total_users,
                'total_batches': 0,  # Placeholder
                'active_assessments': 0,  # Placeholder
                'completed_assessments': 0  # Placeholder
            }
            return stats
    except Exception as e:
        logging.error(f"Failed to get dashboard stats: {e}")
        return {
            'total_users': 0,
            'total_batches': 0,
            'active_assessments': 0,
            'completed_assessments': 0
        }


def get_recent_activity():
    """Get recent activity for dashboard."""
    # TODO: Implement actual activity logging and retrieval
    return [
        {'time': '10:30 AM', 'action': 'User login', 'user': 'admin'},
        {'time': '09:45 AM', 'action': 'Assessment created', 'user': 'teacher1'},
        {'time': '09:15 AM', 'action': 'Batch updated', 'user': 'admin'},
        {'time': '08:30 AM', 'action': 'Report generated', 'user': 'teacher2'},
    ]


if __name__ == '__main__':
    app = create_app()

    # Run the application
    print("Starting Military Peer Review Assessment System (Web Interface)")
    print("Access the application at: http://localhost:5000")

    app.run(
        host='127.0.0.1',
        port=5000,
        debug=True,
        use_reloader=False  # Disable reloader to avoid database issues
    )
