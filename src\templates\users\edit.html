{% extends "base.html" %}

{% block title %}Edit User - Military Peer Review Assessment System{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h2 fw-bold text-dark mb-1">
                        <i class="bi bi-person-gear me-2 text-primary"></i>Edit User
                    </h1>
                    <p class="text-muted mb-0">Update user information and settings</p>
                </div>
                <div>
                    {% if current_user.role in ['super_admin', 'admin'] %}
                    <a href="{{ url_for('users_list') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Users
                    </a>
                    {% else %}
                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Edit User Form -->
    <div class="row justify-content-center">
        <div class="col-lg-8 col-xl-6">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-white border-0 pb-0">
                    <h5 class="fw-semibold mb-0">
                        <i class="bi bi-form me-2 text-primary"></i>User Information
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('users_edit', user_id=user.id) }}" novalidate>
                        <div class="row g-3">
                            <!-- Username (Read-only) -->
                            <div class="col-md-6">
                                <label for="username" class="form-label fw-semibold">
                                    <i class="bi bi-person me-2"></i>Username
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="username" 
                                       value="{{ user.username }}"
                                       readonly>
                                <div class="form-text">Username cannot be changed after creation.</div>
                            </div>

                            <!-- Full Name -->
                            <div class="col-md-6">
                                <label for="full_name" class="form-label fw-semibold">
                                    <i class="bi bi-person-badge me-2"></i>Full Name <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="full_name" 
                                       name="full_name" 
                                       value="{{ user.full_name }}"
                                       placeholder="Enter full name"
                                       required
                                       autocomplete="name">
                            </div>

                            <!-- Email -->
                            <div class="col-md-6">
                                <label for="email" class="form-label fw-semibold">
                                    <i class="bi bi-envelope me-2"></i>Email Address
                                </label>
                                <input type="email" 
                                       class="form-control" 
                                       id="email" 
                                       name="email" 
                                       value="{{ user.email or '' }}"
                                       placeholder="Enter email address"
                                       autocomplete="email">
                            </div>

                            <!-- Role (Admin only) -->
                            {% if current_user.role in ['super_admin', 'admin'] %}
                            <div class="col-md-6">
                                <label for="role" class="form-label fw-semibold">
                                    <i class="bi bi-shield me-2"></i>Role
                                </label>
                                <select class="form-select" id="role" name="role">
                                    {% if current_user.role == 'super_admin' %}
                                    <option value="super_admin" {% if user.role == 'super_admin' %}selected{% endif %}>Super Administrator</option>
                                    {% endif %}
                                    <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>Administrator</option>
                                    <option value="teacher" {% if user.role == 'teacher' %}selected{% endif %}>Teacher</option>
                                    <option value="student" {% if user.role == 'student' %}selected{% endif %}>Student</option>
                                </select>
                            </div>
                            {% else %}
                            <div class="col-md-6">
                                <label class="form-label fw-semibold">
                                    <i class="bi bi-shield me-2"></i>Role
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       value="{{ user.role|title }}"
                                       readonly>
                                <div class="form-text">Role can only be changed by administrators.</div>
                            </div>
                            {% endif %}

                            <!-- Password Change -->
                            <div class="col-12">
                                <hr class="my-4">
                                <h6 class="fw-semibold mb-3">
                                    <i class="bi bi-key me-2"></i>Change Password
                                </h6>
                                <div class="form-text mb-3">Leave password fields empty to keep current password.</div>
                            </div>

                            <div class="col-md-6">
                                <label for="password" class="form-label fw-semibold">
                                    <i class="bi bi-lock me-2"></i>New Password
                                </label>
                                <div class="input-group">
                                    <input type="password" 
                                           class="form-control" 
                                           id="password" 
                                           name="password" 
                                           placeholder="Enter new password"
                                           autocomplete="new-password">
                                    <button class="btn btn-outline-secondary" 
                                            type="button" 
                                            id="togglePassword"
                                            title="Show/Hide Password">
                                        <i class="bi bi-eye" id="toggleIcon"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <label for="confirm_password" class="form-label fw-semibold">
                                    <i class="bi bi-lock-fill me-2"></i>Confirm New Password
                                </label>
                                <input type="password" 
                                       class="form-control" 
                                       id="confirm_password" 
                                       name="confirm_password" 
                                       placeholder="Confirm new password"
                                       autocomplete="new-password">
                                <div class="invalid-feedback" id="passwordMismatch">
                                    Passwords do not match.
                                </div>
                            </div>

                            <!-- Active Status (Admin only) -->
                            {% if current_user.role in ['super_admin', 'admin'] %}
                            <div class="col-12">
                                <hr class="my-4">
                                <div class="form-check">
                                    <input class="form-check-input" 
                                           type="checkbox" 
                                           id="is_active" 
                                           name="is_active" 
                                           {% if user.is_active %}checked{% endif %}>
                                    <label class="form-check-label fw-semibold" for="is_active">
                                        <i class="bi bi-check-circle me-2"></i>Active User
                                    </label>
                                    <div class="form-text">Inactive users cannot log in to the system.</div>
                                </div>
                            </div>
                            {% endif %}

                            <!-- Notes -->
                            <div class="col-12">
                                <label for="notes" class="form-label fw-semibold">
                                    <i class="bi bi-sticky me-2"></i>Notes
                                </label>
                                <textarea class="form-control" 
                                          id="notes" 
                                          name="notes" 
                                          rows="3" 
                                          placeholder="Additional notes about this user">{{ user.notes or '' }}</textarea>
                            </div>

                            <!-- User Information Display -->
                            <div class="col-12">
                                <hr class="my-4">
                                <h6 class="fw-semibold mb-3">
                                    <i class="bi bi-info-circle me-2"></i>Account Information
                                </h6>
                                <div class="row g-3">
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">Created</small>
                                        <span class="fw-semibold">{{ user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else 'N/A' }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">Last Modified</small>
                                        <span class="fw-semibold">{{ user.modified_at.strftime('%Y-%m-%d %H:%M') if user.modified_at else 'N/A' }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">Last Login</small>
                                        <span class="fw-semibold">{{ user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else 'Never' }}</span>
                                    </div>
                                    <div class="col-md-6">
                                        <small class="text-muted d-block">Status</small>
                                        {% if user.is_active %}
                                            <span class="badge bg-success">Active</span>
                                        {% else %}
                                            <span class="badge bg-secondary">Inactive</span>
                                        {% endif %}
                                        {% if user.is_verified %}
                                            <span class="badge bg-info ms-1">Verified</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Form Actions -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="d-flex gap-3 justify-content-end">
                                    {% if current_user.role in ['super_admin', 'admin'] %}
                                    <a href="{{ url_for('users_list') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    {% else %}
                                    <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                                        <i class="bi bi-x-circle me-2"></i>Cancel
                                    </a>
                                    {% endif %}
                                    <button type="submit" class="btn btn-primary">
                                        <i class="bi bi-check-circle me-2"></i>Update User
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_scripts %}
<script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const toggleIcon = document.getElementById('toggleIcon');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.className = 'bi bi-eye-slash';
        } else {
            passwordField.type = 'password';
            toggleIcon.className = 'bi bi-eye';
        }
    });

    // Password confirmation validation
    function validatePasswordMatch() {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        const confirmField = document.getElementById('confirm_password');
        const mismatchFeedback = document.getElementById('passwordMismatch');
        
        // Only validate if both fields have values
        if (password && confirmPassword && password !== confirmPassword) {
            confirmField.classList.add('is-invalid');
            mismatchFeedback.style.display = 'block';
            return false;
        } else {
            confirmField.classList.remove('is-invalid');
            mismatchFeedback.style.display = 'none';
            return true;
        }
    }

    // Add event listeners for password validation
    document.getElementById('password').addEventListener('input', validatePasswordMatch);
    document.getElementById('confirm_password').addEventListener('input', validatePasswordMatch);

    // Form validation
    (function() {
        'use strict';
        
        const form = document.querySelector('form');
        
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity() || !validatePasswordMatch()) {
                event.preventDefault();
                event.stopPropagation();
            }
            
            form.classList.add('was-validated');
        }, false);
    })();

    // Auto-focus full name field
    document.getElementById('full_name').focus();
</script>
{% endblock %}
