"""
Tests for the improved peer review system functionality
"""

import pytest
import sys
import os
from unittest.mock import patch, MagicMock

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


def test_bootstrap_style_manager():
    """Test Bootstrap style manager functionality."""
    from utils.bootstrap_style import BootstrapStyleManager, BootstrapColors
    
    style_manager = BootstrapStyleManager()
    
    # Test color constants
    assert BootstrapColors.PRIMARY == "#0d6efd"
    assert BootstrapColors.SUCCESS == "#198754"
    assert BootstrapColors.DANGER == "#dc3545"
    
    # Test button style generation
    primary_style = style_manager.get_button_style("primary", "md", False)
    assert "background-color: #0d6efd" in primary_style
    assert "border-radius: 6px" in primary_style
    
    # Test outline button style
    outline_style = style_manager.get_button_style("primary", "md", True)
    assert "background-color: transparent" in outline_style
    assert "border: 2px solid #0d6efd" in outline_style
    
    # Test card style generation
    card_style = style_manager.get_card_style(shadow=True, border=True)
    assert "background-color: #ffffff" in card_style
    assert "border-radius: 8px" in card_style
    
    # Test form control style
    form_style = style_manager.get_form_control_style("md", "normal")
    assert "border: 2px solid #dee2e6" in form_style
    assert "border-radius: 6px" in form_style


def test_responsive_layout():
    """Test responsive layout functionality."""
    from ui.layouts.responsive import ResponsiveGridLayout, BootstrapBreakpoints
    from PySide6.QtCore import QSize
    
    layout = ResponsiveGridLayout()
    
    # Test breakpoint detection
    assert layout._get_breakpoint_for_width(500) == "xs"
    assert layout._get_breakpoint_for_width(800) == "md"
    assert layout._get_breakpoint_for_width(1200) == "xl"
    
    # Test layout update
    layout.update_layout_for_size(QSize(800, 600))
    assert layout.current_breakpoint == "md"


def test_database_connection_manager():
    """Test database connection manager improvements."""
    from database.connection import DatabaseConnection
    from core.exceptions import DatabaseError
    
    db_conn = DatabaseConnection()
    
    # Test that it properly handles uninitialized database
    with pytest.raises(DatabaseError, match="Database not initialized"):
        with db_conn.get_session() as session:
            pass


def test_database_migrations():
    """Test database migrations functionality."""
    from database.migrations import DatabaseMigrations
    
    migrations = DatabaseMigrations()
    
    # Test that migrations object is created properly
    assert migrations is not None
    assert hasattr(migrations, 'run_initial_setup')
    assert hasattr(migrations, '_create_default_admin')
    assert hasattr(migrations, '_create_sample_data')


@patch('PySide6.QtWidgets.QApplication')
def test_main_application_setup(mock_app):
    """Test main application setup without deprecated attributes."""
    from main import setup_application
    
    # Mock QApplication instance
    mock_app_instance = MagicMock()
    mock_app.return_value = mock_app_instance
    
    # Test application setup
    app = setup_application()
    
    # Verify application properties are set
    mock_app_instance.setApplicationName.assert_called_with("Military Peer Review System")
    mock_app_instance.setApplicationVersion.assert_called_with("1.0.0")
    mock_app_instance.setOrganizationName.assert_called_with("Ajinkyacreatiion PVT. LTD.")
    
    # Verify deprecated attributes are NOT called
    assert not mock_app_instance.setAttribute.called


def test_stat_card_bootstrap_styling():
    """Test StatCard uses Bootstrap styling."""
    from ui.dashboard import StatCard
    from PySide6.QtWidgets import QApplication
    
    # Create QApplication if it doesn't exist
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    try:
        card = StatCard("Test Title", "42", "Test Subtitle", "#0d6efd")
        
        # Test card properties
        assert card.size().width() == 300
        assert card.size().height() == 160
        
        # Test that stylesheet contains Bootstrap colors
        stylesheet = card.styleSheet()
        assert "#ffffff" in stylesheet  # Bootstrap white background
        assert "#0d6efd" in stylesheet  # Bootstrap primary color
        assert "border-radius: 8px" in stylesheet  # Bootstrap border radius
        
    finally:
        if app:
            app.quit()


def test_quick_action_button_bootstrap_styling():
    """Test QuickActionButton uses Bootstrap styling."""
    from ui.dashboard import QuickActionButton
    from PySide6.QtWidgets import QApplication
    
    # Create QApplication if it doesn't exist
    app = QApplication.instance()
    if app is None:
        app = QApplication([])
    
    try:
        button = QuickActionButton("Test Action", "outline-primary")
        
        # Test button properties
        assert button.size().width() == 200
        assert button.size().height() == 140
        
        # Test that stylesheet contains Bootstrap styling
        stylesheet = button.styleSheet()
        assert "#0d6efd" in stylesheet  # Bootstrap primary color
        assert "border-radius: 8px" in stylesheet  # Bootstrap border radius
        assert "background-color: transparent" in stylesheet  # Outline style
        
    finally:
        if app:
            app.quit()


def test_improved_error_handling():
    """Test improved error handling in database operations."""
    from core.exceptions import DatabaseError
    
    # Test that DatabaseError is properly defined
    assert DatabaseError is not None
    
    # Test error creation
    error = DatabaseError("Test error message")
    assert str(error) == "Test error message"


def test_settings_functionality():
    """Test settings functionality."""
    from config.settings import AppSettings
    
    settings = AppSettings()
    
    # Test that settings can be retrieved
    db_path = settings.get("database/path")
    assert db_path is not None
    
    # Test UI settings
    ui_settings = settings.get_ui_settings()
    assert isinstance(ui_settings, dict)
    assert "window_width" in ui_settings
    assert "window_height" in ui_settings


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
