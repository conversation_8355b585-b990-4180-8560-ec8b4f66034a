#!/usr/bin/env python3
"""
CLI Desktop Application Builder

Builds the Military Peer Review Assessment System as a desktop CLI application
using Electron with embedded terminal interface.

Author: <PERSON><PERSON>
Developer: Hrishikesh <PERSON>hite
Company: Ajinkyacreatiion PVT. LTD.
"""

import os
import sys
import subprocess
import shutil
import json
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class CLIDesktopBuilder:
    """Builder for CLI desktop application."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.dist_dir = self.project_root / "dist"
        self.desktop_dir = self.project_root / "desktop"
        
    def check_prerequisites(self):
        """Check if all prerequisites are installed."""
        logger.info("Checking prerequisites...")
        
        # Check Node.js and npm
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception("Node.js not found")
            logger.info(f"Node.js version: {result.stdout.strip()}")
        except Exception as e:
            logger.error("Node.js is required but not found. Please install Node.js.")
            return False
        
        try:
            result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception("npm not found")
            logger.info(f"npm version: {result.stdout.strip()}")
        except Exception as e:
            logger.error("npm is required but not found. Please install npm.")
            return False
        
        # Check Python
        try:
            result = subprocess.run([sys.executable, '--version'], capture_output=True, text=True)
            if result.returncode != 0:
                raise Exception("Python not found")
            logger.info(f"Python version: {result.stdout.strip()}")
        except Exception as e:
            logger.error("Python is required but not found.")
            return False
        
        return True
    
    def install_dependencies(self):
        """Install Node.js dependencies."""
        logger.info("Installing Node.js dependencies...")
        
        try:
            # Install dependencies
            result = subprocess.run(['npm', 'install'], cwd=self.project_root, check=True)
            logger.info("Node.js dependencies installed successfully")
            return True
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to install Node.js dependencies: {e}")
            return False
    
    def prepare_cli_package(self):
        """Prepare package.json for CLI build."""
        logger.info("Preparing CLI package configuration...")
        
        package_json_path = self.project_root / "package.json"
        
        try:
            # Read current package.json
            with open(package_json_path, 'r') as f:
                package_data = json.load(f)
            
            # Update main entry point for CLI
            package_data['main'] = 'desktop/main_cli.js'
            package_data['name'] = 'military-peer-review-cli'
            package_data['description'] = 'Military Peer Review Assessment System - CLI Desktop Application'
            
            # Update build configuration
            if 'build' in package_data:
                package_data['build']['productName'] = 'Military Peer Review CLI'
                
                # Update NSIS configuration for CLI
                if 'nsis' in package_data['build']:
                    package_data['build']['nsis']['shortcutName'] = 'Military Peer Review CLI'
            
            # Write updated package.json
            with open(package_json_path, 'w') as f:
                json.dump(package_data, f, indent=2)
            
            logger.info("Package configuration updated for CLI build")
            return True
            
        except Exception as e:
            logger.error(f"Failed to prepare CLI package: {e}")
            return False
    
    def build_application(self, platform='all'):
        """Build the desktop application."""
        logger.info(f"Building CLI desktop application for {platform}...")
        
        try:
            # Clean dist directory
            if self.dist_dir.exists():
                shutil.rmtree(self.dist_dir)
            
            # Build command based on platform
            if platform == 'windows' or platform == 'win':
                cmd = ['npm', 'run', 'build-win']
            elif platform == 'macos' or platform == 'mac':
                cmd = ['npm', 'run', 'build-mac']
            elif platform == 'linux':
                cmd = ['npm', 'run', 'build-linux']
            else:
                cmd = ['npm', 'run', 'build']
            
            # Execute build
            result = subprocess.run(cmd, cwd=self.project_root, check=True)
            logger.info("Desktop application built successfully")
            
            # List built files
            if self.dist_dir.exists():
                logger.info("Built files:")
                for item in self.dist_dir.iterdir():
                    logger.info(f"  {item.name}")
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to build desktop application: {e}")
            return False
    
    def create_portable_version(self):
        """Create a portable version of the application."""
        logger.info("Creating portable version...")
        
        try:
            # Create portable directory
            portable_dir = self.project_root / "portable"
            if portable_dir.exists():
                shutil.rmtree(portable_dir)
            portable_dir.mkdir()
            
            # Copy essential files
            essential_files = [
                "src",
                "data",
                "requirements.txt",
                "README.md",
                "INSTALLATION_GUIDE.md"
            ]
            
            for file_name in essential_files:
                src_path = self.project_root / file_name
                if src_path.exists():
                    if src_path.is_dir():
                        shutil.copytree(src_path, portable_dir / file_name)
                    else:
                        shutil.copy2(src_path, portable_dir / file_name)
            
            # Create run script for CLI
            if os.name == 'nt':  # Windows
                run_script = portable_dir / "run_cli.bat"
                with open(run_script, 'w') as f:
                    f.write("@echo off\n")
                    f.write("echo Starting Military Peer Review Assessment System - CLI\n")
                    f.write("python src\\cli_app.py\n")
                    f.write("pause\n")
            else:  # Unix-like
                run_script = portable_dir / "run_cli.sh"
                with open(run_script, 'w') as f:
                    f.write("#!/bin/bash\n")
                    f.write("echo 'Starting Military Peer Review Assessment System - CLI'\n")
                    f.write("python3 src/cli_app.py\n")
                os.chmod(run_script, 0o755)
            
            # Create README for portable version
            readme_path = portable_dir / "PORTABLE_README.md"
            with open(readme_path, 'w') as f:
                f.write("# Military Peer Review Assessment System - Portable CLI\n\n")
                f.write("## Quick Start\n\n")
                f.write("### Windows\n")
                f.write("1. Double-click `run_cli.bat`\n")
                f.write("2. Or open Command Prompt and run: `python src\\cli_app.py`\n\n")
                f.write("### Linux/macOS\n")
                f.write("1. Run: `./run_cli.sh`\n")
                f.write("2. Or run: `python3 src/cli_app.py`\n\n")
                f.write("## Requirements\n")
                f.write("- Python 3.8 or higher\n")
                f.write("- Required Python packages (install with: `pip install -r requirements.txt`)\n\n")
                f.write("## Default Login\n")
                f.write("- Username: admin\n")
                f.write("- Password: Admin@123\n\n")
                f.write("## Features\n")
                f.write("- Complete command-line interface\n")
                f.write("- User management\n")
                f.write("- Batch management\n")
                f.write("- Assessment creation and management\n")
                f.write("- Peer evaluation system\n")
                f.write("- Comprehensive reporting\n")
                f.write("- Data export capabilities\n")
            
            logger.info(f"Portable version created in: {portable_dir}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to create portable version: {e}")
            return False
    
    def test_cli_application(self):
        """Test the CLI application."""
        logger.info("Testing CLI application...")
        
        try:
            # Test CLI import
            cli_app_path = self.project_root / "src" / "cli_app.py"
            if not cli_app_path.exists():
                raise Exception("CLI application not found")
            
            # Test Python dependencies
            test_script = """
import sys
sys.path.insert(0, 'src')
try:
    from cli_app import MilitaryPeerReviewCLI
    print("CLI application imports successfully")
except ImportError as e:
    print(f"Import error: {e}")
    sys.exit(1)
"""
            
            result = subprocess.run(
                [sys.executable, '-c', test_script],
                cwd=self.project_root,
                capture_output=True,
                text=True
            )
            
            if result.returncode == 0:
                logger.info("CLI application test passed")
                return True
            else:
                logger.error(f"CLI application test failed: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to test CLI application: {e}")
            return False
    
    def build(self, platform='all', create_portable=True):
        """Main build process."""
        logger.info("Starting CLI desktop application build process...")
        
        # Check prerequisites
        if not self.check_prerequisites():
            logger.error("Prerequisites check failed")
            return False
        
        # Install dependencies
        if not self.install_dependencies():
            logger.error("Dependency installation failed")
            return False
        
        # Test CLI application
        if not self.test_cli_application():
            logger.error("CLI application test failed")
            return False
        
        # Prepare CLI package
        if not self.prepare_cli_package():
            logger.error("CLI package preparation failed")
            return False
        
        # Build desktop application
        if not self.build_application(platform):
            logger.error("Desktop application build failed")
            return False
        
        # Create portable version
        if create_portable:
            if not self.create_portable_version():
                logger.error("Portable version creation failed")
                return False
        
        logger.info("CLI desktop application build completed successfully!")
        logger.info(f"Built files are available in: {self.dist_dir}")
        
        if create_portable:
            logger.info(f"Portable version is available in: {self.project_root / 'portable'}")
        
        return True


def main():
    """Main entry point."""
    import argparse
    
    parser = argparse.ArgumentParser(description='Build Military Peer Review CLI Desktop Application')
    parser.add_argument('--platform', choices=['all', 'windows', 'win', 'macos', 'mac', 'linux'], 
                       default='all', help='Target platform for build')
    parser.add_argument('--no-portable', action='store_true', 
                       help='Skip creating portable version')
    
    args = parser.parse_args()
    
    builder = CLIDesktopBuilder()
    success = builder.build(
        platform=args.platform,
        create_portable=not args.no_portable
    )
    
    if success:
        print("\n✅ Build completed successfully!")
        print("\nNext steps:")
        print("1. Test the built application")
        print("2. Distribute the installer or portable version")
        print("3. Provide users with the default login credentials:")
        print("   Username: admin")
        print("   Password: Admin@123")
    else:
        print("\n❌ Build failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
