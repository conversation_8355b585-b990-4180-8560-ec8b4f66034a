"""
User Management CLI Module

Provides command-line interface for user management operations.
"""

from typing import List, Optional
from datetime import datetime
from core.exceptions import ValidationError, DatabaseError


class UserManagementCLI:
    """CLI for user management operations."""

    def __init__(self, main_app):
        self.app = main_app
        self.user_service = main_app.user_service

    def show_menu(self):
        """Show user management menu."""
        while True:
            self.app.clear_screen()
            print(f"\n{self.app.CLIColors.BOLD}👥 USER MANAGEMENT{self.app.CLIColors.ENDC}")
            print("-" * 40)

            menu_options = [
                "📋 List All Users",
                "➕ Create New User",
                "✏️ Edit User",
                "🗑️ Delete User",
                "🔍 Search Users",
                "📊 User Statistics",
                "🔙 Back to Main Menu"
            ]

            choice = self.app.get_choice("Select an option:", menu_options)

            if choice == -1 or choice == 6:  # Back or cancelled
                break
            elif choice == 0:  # List Users
                self.list_users()
            elif choice == 1:  # Create User
                self.create_user()
            elif choice == 2:  # Edit User
                self.edit_user()
            elif choice == 3:  # Delete User
                self.delete_user()
            elif choice == 4:  # Search Users
                self.search_users()
            elif choice == 5:  # User Statistics
                self.show_user_statistics()

    def list_users(self):
        """List all users."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}📋 USER LIST{self.app.CLIColors.ENDC}")
            print("-" * 40)

            self.app.print_info("Loading user data...")
            users = self.user_service.get_all_users(include_inactive=True)

            if not users:
                self.app.print_info("No users found in the system.")
                self.app.wait_for_enter()
                return

            # Display users in table format
            headers = ["ID", "Username", "Full Name", "Role", "Email", "Status", "Last Login"]
            widths = [5, 15, 25, 12, 25, 8, 12]

            self.app.print_table_header(headers, widths)

            for user in users:
                values = [
                    str(user.id),
                    user.username,
                    user.full_name,
                    user.role.replace('_', ' ').title(),
                    user.email or "Not set",
                    "Active" if user.is_active else "Inactive",
                    user.last_login.strftime('%Y-%m-%d') if user.last_login else "Never"
                ]
                self.app.print_table_row(values, widths)

            print(f"\nTotal users: {len(users)}")

        except ValidationError as e:
            self.app.print_error(f"Data validation issue: {e}")
        except DatabaseError as e:
            self.app.print_error("Unable to retrieve user information from database.")
            self.app.logger.error(f"Database error in list_users: {e}")
        except Exception as e:
            self.app.print_error("An unexpected error occurred while retrieving users.")
            self.app.logger.error(f"Unexpected error in list_users: {e}")

        self.app.wait_for_enter()

    def create_user(self):
        """Create a new user."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}➕ CREATE NEW USER{self.app.CLIColors.ENDC}")
            print("-" * 40)

            # Get user information
            username = self.app.get_input("Username: ")
            if not username:
                return

            full_name = self.app.get_input("Full Name: ")
            if not full_name:
                return

            email = self.app.get_input("Email (optional): ", required=False)
            phone = self.app.get_input("Phone (optional): ", required=False)

            # Role selection
            roles = ["super_admin", "admin", "teacher", "student"]
            role_choice = self.app.get_choice("Select Role:", [role.replace('_', ' ').title() for role in roles])
            if role_choice == -1:
                return

            role = roles[role_choice]

            password = self.app.get_password("Password: ")
            if not password:
                return

            confirm_password = self.app.get_password("Confirm Password: ")
            if password != confirm_password:
                self.app.print_error("Passwords do not match.")
                self.app.wait_for_enter()
                return

            # Additional fields for students
            student_id = None
            service_number = None
            rank = None
            unit = None
            branch = None

            if role == 'student':
                student_id = self.app.get_input("Student ID: ")
                service_number = self.app.get_input("Service Number (optional): ", required=False)
                rank = self.app.get_input("Rank (optional): ", required=False)
                unit = self.app.get_input("Unit (optional): ", required=False)
                branch = self.app.get_input("Branch (optional): ", required=False)

            # Create user data
            user_data = {
                'username': username,
                'password': password,
                'full_name': full_name,
                'email': email if email else None,
                'phone': phone if phone else None,
                'role': role,
                'is_active': True,
                'created_by_id': self.app.current_user.id
            }

            # Add student-specific fields
            if role == 'student':
                user_data.update({
                    'student_id': student_id,
                    'service_number': service_number if service_number else None,
                    'rank': rank if rank else None,
                    'unit': unit if unit else None,
                    'branch': branch if branch else None
                })

            # Create user
            user = self.user_service.create_user(user_data)
            self.app.print_success(f"User '{user.username}' created successfully!")

        except ValidationError as e:
            self.app.print_error(f"User creation failed: {e}")
        except DatabaseError as e:
            self.app.print_error("Unable to create user due to database issue.")
            self.app.logger.error(f"Database error in create_user: {e}")
        except Exception as e:
            self.app.print_error("An unexpected error occurred while creating the user.")
            self.app.logger.error(f"Unexpected error in create_user: {e}")

        self.app.wait_for_enter()

    def edit_user(self):
        """Edit an existing user."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}✏️ EDIT USER{self.app.CLIColors.ENDC}")
            print("-" * 40)

            # Get user to edit
            user_id = self.app.get_input("Enter User ID to edit: ")
            if not user_id:
                return

            try:
                user_id = int(user_id)
            except ValueError:
                self.app.print_error("Invalid User ID.")
                self.app.wait_for_enter()
                return

            user = self.user_service.get_user_by_id(user_id)
            if not user:
                self.app.print_error("User not found.")
                self.app.wait_for_enter()
                return

            print(f"\nEditing user: {user.full_name} ({user.username})")
            print("-" * 40)

            # Get updated information
            full_name = self.app.get_input(f"Full Name [{user.full_name}]: ", required=False)
            email = self.app.get_input(f"Email [{user.email or 'Not set'}]: ", required=False)
            phone = self.app.get_input(f"Phone [{user.phone or 'Not set'}]: ", required=False)

            # Role selection (only for super_admin)
            new_role = None
            if self.app.current_user.role == 'super_admin':
                roles = ["super_admin", "admin", "teacher", "student"]
                role_choice = self.app.get_choice(
                    f"Select Role (current: {user.role.replace('_', ' ').title()}):",
                    [role.replace('_', ' ').title() for role in roles] + ["Keep Current"]
                )
                if role_choice != -1 and role_choice < len(roles):
                    new_role = roles[role_choice]

            # Status change
            status_options = ["Active", "Inactive", "Keep Current"]
            status_choice = self.app.get_choice(
                f"Status (current: {'Active' if user.is_active else 'Inactive'}):",
                status_options
            )

            new_status = None
            if status_choice == 0:
                new_status = True
            elif status_choice == 1:
                new_status = False

            # Build update data
            update_data = {}
            if full_name:
                update_data['full_name'] = full_name
            if email:
                update_data['email'] = email
            if phone:
                update_data['phone'] = phone
            if new_role and new_role != user.role:
                update_data['role'] = new_role
            if new_status is not None:
                update_data['is_active'] = new_status

            if not update_data:
                self.app.print_info("No changes made.")
                self.app.wait_for_enter()
                return

            # Confirm changes
            print(f"\nChanges to be made:")
            for key, value in update_data.items():
                print(f"  {key.replace('_', ' ').title()}: {value}")

            if self.app.confirm_action("Apply these changes?"):
                self.user_service.update_user(user_id, update_data)
                self.app.print_success("User updated successfully!")
            else:
                self.app.print_info("Changes cancelled.")

        except ValidationError as e:
            self.app.print_error(f"User update failed: {e}")
        except DatabaseError as e:
            self.app.print_error("Unable to update user due to database issue.")
            self.app.logger.error(f"Database error in edit_user: {e}")
        except Exception as e:
            self.app.print_error("An unexpected error occurred while updating the user.")
            self.app.logger.error(f"Unexpected error in edit_user: {e}")

        self.app.wait_for_enter()

    def delete_user(self):
        """Delete a user."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}🗑️ DELETE USER{self.app.CLIColors.ENDC}")
            print("-" * 40)

            # Get user to delete
            user_id = self.app.get_input("Enter User ID to delete: ")
            if not user_id:
                return

            try:
                user_id = int(user_id)
            except ValueError:
                self.app.print_error("Invalid User ID.")
                self.app.wait_for_enter()
                return

            user = self.user_service.get_user_by_id(user_id)
            if not user:
                self.app.print_error("User not found.")
                self.app.wait_for_enter()
                return

            # Prevent self-deletion
            if user.id == self.app.current_user.id:
                self.app.print_error("You cannot delete your own account.")
                self.app.wait_for_enter()
                return

            print(f"\nUser to delete:")
            print(f"  ID: {user.id}")
            print(f"  Username: {user.username}")
            print(f"  Full Name: {user.full_name}")
            print(f"  Role: {user.role.replace('_', ' ').title()}")

            if self.app.confirm_action("Are you sure you want to delete this user?"):
                self.user_service.delete_user(user_id, soft_delete=True)
                self.app.print_success("User deleted successfully!")
            else:
                self.app.print_info("Deletion cancelled.")

        except ValidationError as e:
            self.app.print_error(f"User deletion failed: {e}")
        except DatabaseError as e:
            self.app.print_error("Unable to delete user due to database issue.")
            self.app.logger.error(f"Database error in delete_user: {e}")
        except Exception as e:
            self.app.print_error("An unexpected error occurred while deleting the user.")
            self.app.logger.error(f"Unexpected error in delete_user: {e}")

        self.app.wait_for_enter()

    def search_users(self):
        """Search for users."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}🔍 SEARCH USERS{self.app.CLIColors.ENDC}")
            print("-" * 40)

            search_term = self.app.get_input("Enter search term (username, name, or email): ")
            if not search_term:
                return

            users = self.user_service.search_users(search_term)

            if not users:
                self.app.print_info("No users found matching the search criteria.")
                self.app.wait_for_enter()
                return

            # Display search results
            headers = ["ID", "Username", "Full Name", "Role", "Email", "Status"]
            widths = [5, 15, 25, 12, 25, 8]

            self.app.print_table_header(headers, widths)

            for user in users:
                values = [
                    str(user.id),
                    user.username,
                    user.full_name,
                    user.role.replace('_', ' ').title(),
                    user.email or "Not set",
                    "Active" if user.is_active else "Inactive"
                ]
                self.app.print_table_row(values, widths)

            print(f"\nFound {len(users)} user(s) matching '{search_term}'")

        except DatabaseError as e:
            self.app.print_error("Unable to search users due to database issue.")
            self.app.logger.error(f"Database error in search_users: {e}")
        except Exception as e:
            self.app.print_error("An unexpected error occurred while searching users.")
            self.app.logger.error(f"Unexpected error in search_users: {e}")

        self.app.wait_for_enter()

    def show_user_statistics(self):
        """Show user statistics."""
        try:
            print(f"\n{self.app.CLIColors.BOLD}📊 USER STATISTICS{self.app.CLIColors.ENDC}")
            print("-" * 40)

            stats = self.user_service.get_user_statistics()

            print(f"Total Users: {stats.get('total_users', 0)}")
            print(f"Active Users: {stats.get('active_users', 0)}")
            print(f"Inactive Users: {stats.get('inactive_users', 0)}")
            print()

            print("Users by Role:")
            role_counts = stats.get('role_counts', {})
            for role, count in role_counts.items():
                print(f"  {role.replace('_', ' ').title()}: {count}")

        except DatabaseError as e:
            self.app.print_error("Unable to retrieve user statistics due to database issue.")
            self.app.logger.error(f"Database error in show_user_statistics: {e}")
        except Exception as e:
            self.app.print_error("An unexpected error occurred while retrieving statistics.")
            self.app.logger.error(f"Unexpected error in show_user_statistics: {e}")

        self.app.wait_for_enter()
