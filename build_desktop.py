#!/usr/bin/env python3
"""
Desktop Application Builder

This script builds the complete desktop application package including:
- Electron wrapper
- Python runtime
- All dependencies
- Installer package
"""

import os
import sys
import shutil
import subprocess
import platform
import zipfile
import urllib.request
from pathlib import Path

class DesktopBuilder:
    def __init__(self):
        self.root_dir = Path(__file__).parent
        self.build_dir = self.root_dir / "build"
        self.dist_dir = self.root_dir / "dist"
        self.python_runtime_dir = self.root_dir / "python-runtime"
        
        # Platform-specific settings
        self.platform = platform.system().lower()
        self.arch = "x64" if platform.machine().endswith('64') else "x86"
        
        print(f"Building for {self.platform} {self.arch}")
    
    def clean_build_dirs(self):
        """Clean previous build artifacts."""
        print("Cleaning build directories...")
        
        for dir_path in [self.build_dir, self.dist_dir]:
            if dir_path.exists():
                shutil.rmtree(dir_path)
            dir_path.mkdir(parents=True, exist_ok=True)
    
    def check_dependencies(self):
        """Check if required dependencies are installed."""
        print("Checking dependencies...")
        
        # Check Node.js and npm
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            print(f"Node.js version: {result.stdout.strip()}")
        except FileNotFoundError:
            print("ERROR: Node.js is not installed. Please install Node.js from https://nodejs.org/")
            return False
        
        try:
            result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
            print(f"npm version: {result.stdout.strip()}")
        except FileNotFoundError:
            print("ERROR: npm is not installed.")
            return False
        
        # Check Python
        try:
            result = subprocess.run([sys.executable, '--version'], capture_output=True, text=True)
            print(f"Python version: {result.stdout.strip()}")
        except FileNotFoundError:
            print("ERROR: Python is not installed.")
            return False
        
        return True
    
    def install_node_dependencies(self):
        """Install Node.js dependencies."""
        print("Installing Node.js dependencies...")
        
        try:
            subprocess.run(['npm', 'install'], cwd=self.root_dir, check=True)
            print("Node.js dependencies installed successfully.")
        except subprocess.CalledProcessError as e:
            print(f"ERROR: Failed to install Node.js dependencies: {e}")
            return False
        
        return True
    
    def install_python_dependencies(self):
        """Install Python dependencies."""
        print("Installing Python dependencies...")
        
        try:
            subprocess.run([
                sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
            ], cwd=self.root_dir, check=True)
            print("Python dependencies installed successfully.")
        except subprocess.CalledProcessError as e:
            print(f"ERROR: Failed to install Python dependencies: {e}")
            return False
        
        return True
    
    def download_python_runtime(self):
        """Download embedded Python runtime."""
        print("Setting up Python runtime...")
        
        if self.python_runtime_dir.exists():
            print("Python runtime already exists.")
            return True
        
        # For now, we'll use the system Python
        # In production, you would download and extract embedded Python
        self.python_runtime_dir.mkdir(exist_ok=True)
        
        # Create a simple script to use system Python
        python_script = self.python_runtime_dir / "python.exe" if self.platform == "windows" else self.python_runtime_dir / "python"
        
        with open(python_script, 'w') as f:
            f.write(f'#!/bin/bash\n{sys.executable} "$@"\n')
        
        if self.platform != "windows":
            os.chmod(python_script, 0o755)
        
        print("Python runtime setup completed.")
        return True
    
    def create_application_bundle(self):
        """Create the application bundle."""
        print("Creating application bundle...")
        
        # Copy all necessary files to build directory
        app_dir = self.build_dir / "app"
        app_dir.mkdir(parents=True, exist_ok=True)
        
        # Copy source files
        src_files = [
            "src",
            "data",
            "logs",
            "desktop",
            "requirements.txt",
            "package.json",
            "*.py",
            "*.md",
            "*.txt"
        ]
        
        for pattern in src_files:
            if pattern.startswith("*."):
                # Handle glob patterns
                for file_path in self.root_dir.glob(pattern):
                    if file_path.is_file():
                        shutil.copy2(file_path, app_dir)
            else:
                src_path = self.root_dir / pattern
                if src_path.exists():
                    if src_path.is_dir():
                        shutil.copytree(src_path, app_dir / pattern, dirs_exist_ok=True)
                    else:
                        shutil.copy2(src_path, app_dir)
        
        # Copy Python runtime
        if self.python_runtime_dir.exists():
            shutil.copytree(self.python_runtime_dir, app_dir / "python-runtime", dirs_exist_ok=True)
        
        print("Application bundle created successfully.")
        return True
    
    def build_electron_app(self):
        """Build the Electron application."""
        print("Building Electron application...")
        
        try:
            # Build for current platform
            if self.platform == "windows":
                subprocess.run(['npm', 'run', 'build-win'], cwd=self.root_dir, check=True)
            elif self.platform == "darwin":
                subprocess.run(['npm', 'run', 'build-mac'], cwd=self.root_dir, check=True)
            else:
                subprocess.run(['npm', 'run', 'build-linux'], cwd=self.root_dir, check=True)
            
            print("Electron application built successfully.")
        except subprocess.CalledProcessError as e:
            print(f"ERROR: Failed to build Electron application: {e}")
            return False
        
        return True
    
    def create_installer(self):
        """Create installer package."""
        print("Creating installer package...")
        
        if self.platform == "windows":
            # The electron-builder should have created the installer
            installer_path = self.dist_dir / "Military Peer Review Assessment System Setup 1.0.0.exe"
            if installer_path.exists():
                print(f"Windows installer created: {installer_path}")
                return True
            else:
                print("Windows installer not found. Check electron-builder output.")
                return False
        
        elif self.platform == "darwin":
            # Look for DMG file
            dmg_path = list(self.dist_dir.glob("*.dmg"))
            if dmg_path:
                print(f"macOS installer created: {dmg_path[0]}")
                return True
            else:
                print("macOS installer not found.")
                return False
        
        else:
            # Look for AppImage or deb
            installers = list(self.dist_dir.glob("*.AppImage")) + list(self.dist_dir.glob("*.deb"))
            if installers:
                print(f"Linux installer created: {installers[0]}")
                return True
            else:
                print("Linux installer not found.")
                return False
    
    def create_portable_version(self):
        """Create portable version."""
        print("Creating portable version...")
        
        # Find the unpacked directory
        if self.platform == "windows":
            unpacked_dir = self.dist_dir / "win-unpacked"
        elif self.platform == "darwin":
            unpacked_dir = self.dist_dir / "mac"
        else:
            unpacked_dir = self.dist_dir / "linux-unpacked"
        
        if not unpacked_dir.exists():
            print("Unpacked directory not found.")
            return False
        
        # Create portable zip
        portable_name = f"Military-Peer-Review-System-{self.platform}-{self.arch}-portable.zip"
        portable_path = self.dist_dir / portable_name
        
        with zipfile.ZipFile(portable_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            for root, dirs, files in os.walk(unpacked_dir):
                for file in files:
                    file_path = Path(root) / file
                    arc_path = file_path.relative_to(unpacked_dir)
                    zipf.write(file_path, arc_path)
        
        print(f"Portable version created: {portable_path}")
        return True
    
    def build(self):
        """Main build process."""
        print("=" * 60)
        print("Military Peer Review Assessment System - Desktop Builder")
        print("=" * 60)
        
        steps = [
            ("Checking dependencies", self.check_dependencies),
            ("Cleaning build directories", self.clean_build_dirs),
            ("Installing Node.js dependencies", self.install_node_dependencies),
            ("Installing Python dependencies", self.install_python_dependencies),
            ("Setting up Python runtime", self.download_python_runtime),
            ("Creating application bundle", self.create_application_bundle),
            ("Building Electron application", self.build_electron_app),
            ("Creating installer", self.create_installer),
            ("Creating portable version", self.create_portable_version)
        ]
        
        for step_name, step_func in steps:
            print(f"\n[STEP] {step_name}")
            print("-" * 40)
            
            if not step_func():
                print(f"ERROR: {step_name} failed!")
                return False
            
            print(f"SUCCESS: {step_name} completed!")
        
        print("\n" + "=" * 60)
        print("BUILD COMPLETED SUCCESSFULLY!")
        print("=" * 60)
        
        # Show output files
        print("\nOutput files:")
        for file_path in self.dist_dir.iterdir():
            if file_path.is_file():
                size_mb = file_path.stat().st_size / (1024 * 1024)
                print(f"  - {file_path.name} ({size_mb:.1f} MB)")
        
        return True

def main():
    """Main function."""
    builder = DesktopBuilder()
    
    if not builder.build():
        print("\nBuild failed!")
        sys.exit(1)
    
    print("\nBuild completed successfully!")
    print("You can now distribute the installer and portable versions.")

if __name__ == "__main__":
    main()
