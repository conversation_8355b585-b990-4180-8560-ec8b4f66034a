@echo off
title Military Peer Review Assessment System - Desktop Launcher

echo.
echo ========================================================
echo   Military Peer Review Assessment System
echo   Desktop Application Launcher
echo ========================================================
echo.
echo   Author: Maj<PERSON>
echo   Developer: Hrishikesh Mohite
echo   Company: Ajinkyacreatiion PVT. LTD.
echo.
echo ========================================================
echo.

echo Starting Desktop Application...
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8 or higher from https://python.org
    echo.
    pause
    exit /b 1
)

REM Start the desktop application
python desktop_app.py

REM If we get here, the application has closed
echo.
echo Desktop Application has closed.
echo.
pause
