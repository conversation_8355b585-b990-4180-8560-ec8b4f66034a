/* Military Peer Review Assessment System - Custom Styles */

/* Root Variables */
:root {
    --bs-primary: #0d6efd;
    --bs-secondary: #6c757d;
    --bs-success: #198754;
    --bs-info: #0dcaf0;
    --bs-warning: #ffc107;
    --bs-danger: #dc3545;
    --bs-light: #f8f9fa;
    --bs-dark: #212529;
    
    --military-blue: #1e3a8a;
    --military-gold: #fbbf24;
    --military-green: #059669;
    --military-gray: #374151;
}

/* Global Styles */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    line-height: 1.6;
    color: var(--bs-dark);
}

/* Enhanced Card Styles */
.card {
    border-radius: 12px;
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.card-header {
    border-radius: 12px 12px 0 0 !important;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* Button Enhancements */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
    border-width: 1px;
}

.btn:hover {
    transform: translateY(-1px);
}

.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1.1rem;
}

/* Primary Button Gradient */
.btn-primary {
    background: linear-gradient(135deg, var(--bs-primary) 0%, #0b5ed7 100%);
    border-color: var(--bs-primary);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #0b5ed7 0%, #0a58ca 100%);
    border-color: #0a58ca;
}

/* Form Enhancements */
.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #86b7fe;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-control-lg {
    padding: 0.75rem 1rem;
    font-size: 1.1rem;
}

/* Navigation Enhancements */
.navbar-brand {
    font-size: 1.5rem;
    font-weight: 700;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.navbar-nav .nav-link:hover {
    transform: translateY(-1px);
}

/* Alert Enhancements */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 500;
}

.alert-success {
    background: linear-gradient(135deg, #d1e7dd 0%, #a3d9a4 100%);
    color: #0f5132;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f1aeb5 100%);
    color: #721c24;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #9eeaf9 100%);
    color: #055160;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffecb5 100%);
    color: #664d03;
}

/* Statistics Cards */
.stats-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid rgba(0, 0, 0, 0.125);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.stats-card:hover {
    border-color: rgba(0, 0, 0, 0.175);
    transform: translateY(-3px);
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.15);
}

/* Icon Backgrounds */
.icon-bg-primary {
    background: linear-gradient(135deg, rgba(13, 110, 253, 0.1) 0%, rgba(13, 110, 253, 0.2) 100%);
}

.icon-bg-success {
    background: linear-gradient(135deg, rgba(25, 135, 84, 0.1) 0%, rgba(25, 135, 84, 0.2) 100%);
}

.icon-bg-warning {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.1) 0%, rgba(255, 193, 7, 0.2) 100%);
}

.icon-bg-info {
    background: linear-gradient(135deg, rgba(13, 202, 240, 0.1) 0%, rgba(13, 202, 240, 0.2) 100%);
}

/* Table Enhancements */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* Badge Enhancements */
.badge {
    border-radius: 6px;
    font-weight: 500;
}

/* Login Page Specific */
.login-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.login-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

/* Footer Styles */
footer {
    background: linear-gradient(135deg, var(--bs-dark) 0%, #343a40 100%);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
    
    .card-body {
        padding: 1.25rem;
    }
    
    .btn-lg {
        padding: 0.625rem 1.25rem;
        font-size: 1rem;
    }
    
    .display-1 {
        font-size: 4rem;
    }
}

@media (max-width: 576px) {
    .navbar-brand {
        font-size: 1.25rem;
    }
    
    .h2 {
        font-size: 1.5rem;
    }
    
    .stats-card .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.9rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Loading Spinner */
.spinner-border-sm {
    width: 1rem;
    height: 1rem;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Print Styles */
@media print {
    .navbar,
    .btn,
    footer {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
